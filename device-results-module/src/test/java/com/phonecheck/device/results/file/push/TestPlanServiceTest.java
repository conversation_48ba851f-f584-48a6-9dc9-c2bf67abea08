package com.phonecheck.device.results.file.push;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.ISupportFilePathsStrategy;
import com.phonecheck.model.util.SupportFilePath;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPlanServiceTest {
    @Mock
    private CloudApiRestClient cloudApiRestClient;
    @Mock
    private SupportFilePath supportFilePath;
    @Mock
    private FileUtil fileUtil;
    @Mock
    private InMemoryStore inMemoryStore;

    private TestPlanService testPlanService;

    @BeforeEach
    public void setup() {
        testPlanService = new TestPlanService(cloudApiRestClient, supportFilePath, fileUtil, inMemoryStore);
    }

    @Test
    public void testGetTestPlans() throws IOException {

        File testFile = new File("test.json");
        when(fileUtil.createFile(anyString())).thenReturn(testFile);
        when(supportFilePath.getPaths()).thenReturn(new ISupportFilePathsStrategy() {
            @Override
            public String getRootFolderPath() {
                return ".";
            }

            @Override
            public String getToolsRootFolderPath() {
                return null;
            }

            @Override
            public String getFilesRootFolderPath() {
                return null;
            }

            @Override
            public String getWorkingDirectoryPath() {
                return null;
            }

        });

        CloudCustomizationResponse cloudCustomizationResponse = new CloudCustomizationResponse();

        testPlanService.createTestPlanFiles(cloudCustomizationResponse);

        verify(fileUtil, times(2)).createFile(anyString());
        verify(fileUtil, times(2)).writeStringToFileIfDifferent(any(), anyString());
        testFile.delete();
    }
}

