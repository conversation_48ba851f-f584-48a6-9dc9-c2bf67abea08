package com.phonecheck.device.results.parser;

import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.parser.device.test.DeviceTestResultsParser;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class DeviceTestResultsParserTest {
    private final String testResultsFromFile = "{ \"CosmeticResults\": { \"total\": \"Q1-Fail,Q2-Fail,Q3-Good\"," +
            " \"passed\": \"Q3-Good\", \"failed\": \"Q1-Fail,Q2-Fail\", \"pending\": \"Q4\", \"total_count\": \"4\"," +
            " \"failed_count\": \"2\", \"passed_count\": \"1\", \"pending_count\": \"1\" }," +
            " \"BatteryResults\": { \"batteryStat\" : { \"startBattery\" : \"43%\", \"endBattery\" : \"41%\"," +
            " \"totalDuration\" : \"\", \"batteryDrainType\" : \"Percentage\", \"totalDischarge\" : \"2%\" }," +
            " \"batteryDrainInfo\" : { \"4\" : \"0%\", \"2\" : \"0%\", \"7\" : \"0%\", \"6\" : \"0%\"," +
            " \"5\" : \"1%\", \"1\" : \"0%\", \"3\" : \"0%\" } }, \"GradeResults\": \"B\", " +
            "\"TestResults\": { \"total_count\": \"70\", \"failed\": \"Cosmetics,LCD,Microphone,Flip Switch," +
            "Volume Up Button,Network Connectivity,Loud Speaker,Power Button,UltraWide Camera,Front Camera Quality," +
            "Front Camera,Rear Video Camera,Front Microphone,Front Video Camera,Rear Camera,Video Microphone," +
            "Volume Down Button,Glass Cracked,Digitizer,Proximity Sensor,Telephoto Camera Quality,Flashlight," +
            "Vibration,Sim Reader,Rear Camera Quality,Telephoto Camera,Ultra Wide Camera Quality,Ear Speaker\"," +
            " \"failed_count\": \"31\", \"not_attended\": \"LCD,Microphone,Flip Switch,Volume Up Button," +
            "Network Connectivity,Loud Speaker,Power Button,UltraWide Camera,Front Camera Quality,Front Camera," +
            "Rear Video Camera,Front Microphone,Front Video Camera,Rear Camera,Video Microphone,Volume Down Button," +
            "Glass Cracked,Digitizer,Proximity Sensor,Telephoto Camera Quality,Flashlight,Vibration,Sim Reader," +
            "Rear Camera Quality,Telephoto Camera,Ultra Wide Camera Quality,Ear Speaker\", " +
            "\"passed\": \"Bluetooth,Wifi+GPS,Gyroscope,Screen Rotation,Accelerometer\", " +
            "\"total\": \"Spen Plus buttons,Rear Video Camera@Rear Video Camera,Headset-Left,Screen Rotation," +
            "Bluetooth@Bluetooth,Rear Camera Quality@Rear Camera Quality,Audio Output,Headset-Right,AutoSnapFront," +
            "Volume Up Button@Volume Up Button,Auto Accelerometer,Menu Button,AutoSnapRear,Loud Speaker-M," +
            "Telephoto Camera Quality@Telephoto Camera Quality,Flashlight@Flashlight,Mic ES,Edge Screen,Auto ES," +
            "Power Button@Power Button,Camera AutoFocus,Headset Port,Cosmetics@Cosmetics," +
            "Telephoto Camera@Telephoto Camera,SPen Remove,Glass Condition@Glass Cracked," +
            "Front Microphone@Front Microphone,Microphone@Microphone,Flash,Gyroscope," +
            "Network Connectivity@Network Connectivity,Audio Test,Video Microphone@Video Microphone," +
            "Proximity Sensor@Proximity Sensor,Glass Cracked,WiFi@Wifi+GPS,Vid Mic ES,Flip Switch@Flip Switch," +
            "SPen Back Button,SPen Menu Button,Mic LS Test,Auto LS,SPen Hover,Audio Input,Rear Camera@Rear Camera," +
            "Manual Vibration,Digitizer@Digitizer,Home Button,Volume Down Button@Volume Down Button," +
            "Sim Reader@Sim Reader,Front Video Camera@Front Video Camera,Camera Test," +
            "UltraWide Camera@UltraWide Camera,Earpiece@Ear Speaker,3D Touch,LCD@LCD," +
            "UltraWide Camera Quality@Ultra Wide Camera Quality,Call Test,Front Camera@Front Camera,Back Button," +
            "Mic ES Test,Stylus,Loud Speaker@Loud Speaker,Buttons Test," +
            "Accelerometer@Accelerometer$Screen Rotation$Gyroscope,Vibration@Vibration,FingerTrail Digitizer," +
            "Force Touch,Front Camera Quality@Front Camera Quality,SPen\", \"not_attended_count\": \"27\"," +
            " \"pending\": \"\", \"not_supported\": \"Spen Plus buttons,Headset-Left,Screen Rotation,Audio Output," +
            "Headset-Right,AutoSnapFront,Auto Accelerometer,Menu Button,AutoSnapRear,Loud Speaker-M,Mic ES," +
            "Edge Screen,Auto ES,Camera AutoFocus,Headset Port,SPen Remove,Flash,Gyroscope,Audio Test,Glass Cracked," +
            "Vid Mic ES,SPen Back Button,SPen Menu Button,Mic LS Test,Auto LS,SPen Hover,Audio Input," +
            "Manual Vibration,Home Button,Camera Test,3D Touch,Call Test,Back Button,Mic ES Test,Stylus," +
            "Buttons Test,FingerTrail Digitizer,Force Touch,SPen\", \"not_supported_count\": \"39\", " +
            "\"pending_count\": \"0\", \"passed_count\": \"5\" }, \"MicrophoneResults\": " +
            "{ \"BMAmplitude\": \"0.8586\", \"FMAmplitude\": \"0.8503\", \"RMAmplitude\": \"0.4263\" } }";

    private final String testResultsFromSysLog = "rhkg38yw4w-" + testResultsFromFile + "-4rhjg7x9gw";

    @Test
    public void testResultsParsedFromSysLog() {
        DeviceTestResult deviceTestResult = new DeviceTestResultsParser().parse(testResultsFromSysLog);

        assertNotNull(deviceTestResult.getTestResults());
        assertTrue(deviceTestResult.getTestResults().getPassed().contains("Bluetooth"));
        assertNotNull(deviceTestResult.getCosmeticResults());
        assertNotNull(deviceTestResult.getBatteryResults());
        assertNotNull(deviceTestResult.getGradeResults());
        assertNotNull(deviceTestResult.getMicrophoneResults());
    }

    @Test
    public void testResultsParsedFromFile() {
        DeviceTestResult deviceTestResult = new DeviceTestResultsParser().parse(testResultsFromFile);

        assertNotNull(deviceTestResult.getTestResults());
        assertNotNull(deviceTestResult.getCosmeticResults());
        assertNotNull(deviceTestResult.getBatteryResults());
        assertNotNull(deviceTestResult.getGradeResults());
        assertNotNull(deviceTestResult.getMicrophoneResults());
    }
}