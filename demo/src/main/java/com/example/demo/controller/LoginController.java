package com.example.demo.controller;

import com.example.demo.config.UIStageManager;
import com.example.demo.event.LoginSuccessEvent;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Controller for the login screen.
 * Handles user authentication and publishes login events.
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class LoginController {
    
    @FXML
    private TextField usernameField;
    @FXML
    private PasswordField passwordField;

    private final ApplicationEventPublisher eventPublisher;
    private final UIStageManager uiStageManager;

    /**
     * Handles the login button click event.
     * Validates credentials and publishes a login success event if valid.
     *
     * @param event The action event
     */
    @FXML
    private void handleLogin(ActionEvent event) {
        log.debug("Login attempt initiated");
        
        String username = usernameField.getText();
        String password = passwordField.getText();
        
        // Validate input
        if (username == null || username.trim().isEmpty()) {
            uiStageManager.showErrorAlert("Login Error", "Username cannot be empty");
            return;
        }
        
        if (password == null || password.trim().isEmpty()) {
            uiStageManager.showErrorAlert("Login Error", "Password cannot be empty");
            return;
        }

        if ("admin".equals(username) && "admin".equals(password)) {
            log.info("User '{}' authenticated successfully", username);
            eventPublisher.publishEvent(new LoginSuccessEvent(this));
        } else {
            log.warn("Failed login attempt for user: {}", username);
            uiStageManager.showErrorAlert("Login Failed", "Invalid username or password");
        }
    }
}
