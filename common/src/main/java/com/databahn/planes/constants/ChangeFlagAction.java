package com.databahn.planes.constants;

public enum ChangeFlagAction {
  ADD("add"),
  UPDATE("update"),
  DELETE("delete");
  private String action;

  private ChangeFlagAction(String action) {
    this.action = action;
  }

  public String getAction() {
    return action;
  }

  public static ChangeFlagAction parse(String action) {
    for (ChangeFlagAction cfa : ChangeFlagAction.values()) {
      if (cfa.getAction().equals(action)) {
        return cfa;
      }
    }
    throw new IllegalArgumentException("Invalid action: " + action);
  }
}
