package com.databahn.planes.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Response<T> {
  @JsonProperty("data")
  protected final T data;

  @JsonProperty("message")
  protected final String message;

  @JsonProperty("error")
  protected final Error error;

  @JsonProperty("status")
  protected final Status status;

  public boolean checkSuccess() {
    return this.error == null;
  }

  public boolean checkError() {
    return this.error != null;
  }

  @JsonCreator
  public Response(
      @JsonProperty("data") T data,
      @JsonProperty("message") String message,
      @JsonProperty("error") Error error,
      @JsonProperty("status") Status status) {
    this.data = data;
    this.message = message;
    this.error = error;
    this.status = status;
  }

  public enum Status {
    SUCCESS,
    PARTIAL_FAILURE,
    ERROR
  }

  public static <T> Response<T> Message(String message) {
    return new Response<T>(null, message, null, Status.SUCCESS);
  }

  public static <T> Response<T> Ok(T data) {
    return new Response<T>(data, null, null, Status.SUCCESS);
  }

  public static <T> Response<T> Error(Error err) {
    return new Response<T>(null, null, err, Status.ERROR);
  }

  public static <T> Response<T> Error(Exception exception) {
    Error err = new Error.ErrorBuilder().message(exception.getMessage()).build();
    return new Response<T>(null, null, err, Status.ERROR);
  }

  public static MultiStatusResponse<?> Multi(Map<String, Response<?>> responses) {
    int errorCount =
        Long.valueOf(responses.values().stream().filter(Response::checkError).count()).intValue();
    Status status;
    if (errorCount == 0) {
      status = Status.SUCCESS;
    } else if (errorCount == responses.size()) {
      status = Status.ERROR;
    } else {
      status = Status.PARTIAL_FAILURE;
    }
    return new MultiStatusResponse(responses, status);
  }
}
