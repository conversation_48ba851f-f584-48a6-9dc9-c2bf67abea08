package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.CarrierFilterInfoResponse;
import com.phonecheck.model.service.CarrierFilterDb;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CloudCarrierFilterDBLookupServiceTest {
    @Mock
    private CloudApiRestClient cloudApiRestClient;
    private CloudCarrierFilterService carrierFilterService;

    @BeforeEach
    public void setup() {
        carrierFilterService = new CloudCarrierFilterService(cloudApiRestClient);
    }

    @Test
    public void testGetCarrierFilterDb() {

        CarrierFilterInfoResponse testResponse =
                new CarrierFilterInfoResponse();
        testResponse.setVersion("435");
        testResponse.setCarrierFilterInfo(List.of(new CarrierFilterInfoResponse.CarrierFilterInfo()));

        when(cloudApiRestClient
                .getLatestCarrierFilterInfo(
                        anyString())).thenReturn(testResponse);
        CarrierFilterDb result = carrierFilterService
                .getCarrierFilterDb(
                        "version");
        Assertions.assertEquals("435", testResponse.getVersion());
        Assertions.assertEquals(testResponse.getCarrierFilterInfo(), result.getCarrierFilterInfo());
    }
}
