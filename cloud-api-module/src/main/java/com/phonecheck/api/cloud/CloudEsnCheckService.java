package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.cloudapi.EsnLicenseCheckRequest;
import com.phonecheck.model.cloudapi.EsnResponse;
import com.phonecheck.model.service.EsnCheckInfo;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.Arrays;

@Service
@AllArgsConstructor
public class CloudEsnCheckService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudEsnCheckService.class);
    private final CloudApiRestClient cloudApiRestClient;
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Get the ESN check info from cloud api
     *
     * @param request EsnLicenseCheck request
     * @param useCheckMend Boolean for checkmend api
     * @return List of ES
     */
    public EsnCheckInfo getEsnInfo(final EsnLicenseCheckRequest request, final boolean useCheckMend) {
        try {
            final EsnResponse response = useCheckMend ?
                    cloudApiRestClient.getEsnInfoByCheckMend(request) :
                    cloudApiRestClient.getEsnLicenseInfo(request);

            if (response != null) {
                if (response.getEsnApiResults() != null
                        && response.getEsnApiResults().length > 0) {
                    return EsnCheckInfo.builder()
                            .esnApiResults(Arrays.asList(response.getEsnApiResults()))
                            .build();
                } else {
                    return EsnCheckInfo.builder()
                            .esnLicensesExpiredResponse(response.getLicensesExpiredResponse())
                            .build();
                }
            } else {
                return null;
            }
        } catch (final RestClientException e) {
            LOGGER.error("Error when calling get ESN info endpoint", e);
            return null;
        }
    }

    /**
     * Retrieves ESN blacklist information for the specified device.
     *
     * @param deviceImei The IMEI of the device.
     * @param userToken  The user token for authentication.
     * @return An ESN info with blacklist information, or null if an error occurs or no response is received.
     */
    public EsnCheckInfo getEsnBlacklistInfo(final String deviceImei, final String userToken) {
        try {
            final EsnResponse response =
                    phonecheckApiRestClient.getEsnUsInsuranceBlackList(deviceImei, userToken);

            if (response != null) {
                return EsnCheckInfo.builder()
                        .usInsuranceBlackListInfo(response.getUsInsuranceBlackListInfo())
                        .build();
            } else {
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("Error when calling get ESN US Insurance blacklist info endpoint", e);
        }
        return null;
    }

    /**
     * Retrieves Tracfone/Straight talk eligibility information for the specified device.
     *
     * @param deviceImei The IMEI of the device.
     * @param masterToken  The master token for authentication.
     * @return An ESN info with tracfone eligibility information,
     *          or null if an error occurs or no response is received.
     */
    public EsnCheckInfo getTracfoneStraightTalkEligibility(final String deviceImei, final String masterToken) {
        try {
            final EsnResponse response =
                    phonecheckApiRestClient.getTracFoneStraightTalkEligibility(deviceImei, masterToken);

            if (response != null) {
                return EsnCheckInfo.builder()
                        .tracFoneStraightTalkAPIResponse(response.getTracFoneStraightTalkAPIResponse())
                        .build();
            } else {
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("Error when calling get tracfone/straight talk eligibility info endpoint", e);
        }
        return null;
    }
}
