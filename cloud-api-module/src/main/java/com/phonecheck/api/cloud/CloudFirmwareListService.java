package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.LatestFirmwareListResponse;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.service.LatestFirmwareDB;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.LogicalFirmwareModelMapper;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.List;
import java.util.Map;

@Service
@AllArgsConstructor
public class CloudFirmwareListService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudFirmwareListService.class);
    private final CloudApiRestClient cloudApiRestClient;
    private final LogicalFirmwareModelMapper logicalFirmwareModelMapper;
    private final InMemoryStore inMemoryStore;

    /**
     * Get the latest firmware list from cloud api
     *
     * @return Latest firmware list
     */
    public LatestFirmwareDB getLatestFirmwareList() {
        try {
            final LatestFirmwareListResponse response = cloudApiRestClient.getLatestFirmwareListInfo();
            final Map<String, FirmwareModel.FirmwareResponse> firmwareModel = logicalFirmwareModelMapper
                    .generateFirmwareLogicalDataModelList(response);
            inMemoryStore.setFirmwareModels(firmwareModel);

            return response != null ? LatestFirmwareDB.builder()
                    .latestFirmwareInfo(List.of(response.getLatestFirmwareInfo()))
                    .build() : null;
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get latest firmware info", e);
            return null;
        }
    }
}
