package com.phonecheck.api.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.cloudapi.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

@Component
public class CloudVppApiRestClient extends AbstractCloudClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudVppApiRestClient.class);
    protected static final String ASSOCIATE_VPP_V2_API = "v2/assets/associate";
    protected static final String ASSOCIATE_VPP_V1_API = "manageVPPLicensesByAdamIdSrv";
    protected static final String GET_VPP_STATUS_API_PREFIX = "v2/status?eventId=";
    protected static final String COMPLETE = "COMPLETE";
    private static final String AUTHORIZATION = "Authorization";
    private static final String ASSETS = "assets";
    private static final String SERIAL_NUMBERS = "serialNumbers";

    private final ObjectMapper mapper;

    public CloudVppApiRestClient(final @Qualifier("cloudVppRestTemplate") RestTemplate restTemplate,
                                 final ObjectMapper objectMapper) {
        super(restTemplate);
        this.mapper = objectMapper;
    }

    /**
     * Associates the target device to apple vpp using the new api version V2(ASSOCIATE_VPP_V2_API)
     *
     * @param vppRequest
     * @param vppToken
     * @return true if association is successful
     */
    public synchronized boolean associateVppV2(final AssociateVppV2Request vppRequest,
                                  final String vppToken) {
        LOGGER.info("Calling associate client to VPP call 2");
        HttpHeaders headers = new HttpHeaders();
        headers.set(AUTHORIZATION, "Bearer {" + vppToken + "}");
        headers.setContentType(MediaType.APPLICATION_JSON);

        MultiValueMap<String, Object> payload = new LinkedMultiValueMap<>();
        payload.add(ASSETS, vppRequest.getAssets()[0]);
        payload.add(SERIAL_NUMBERS, vppRequest.getSerialNumbers()[0]);

        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(payload, headers);
        AssociateVppV2Response response = getRestTemplate().postForObject(ASSOCIATE_VPP_V2_API,
                request, AssociateVppV2Response.class);
        if (response.getEventId() != null && response.getUId() != null) {
            return appleVppEventStatus(response.getEventId(), vppToken);
        } else {
            return false;
        }
    }

    /**
     * Associates the target device to apple vpp using
     * the old api version V1(ASSOCIATE_VPP_V1_API)
     *
     * @param vppV1Request AssociateVppV1Request
     * @return true if association is successful
     */
    public synchronized boolean associateVppV1(
            final AssociateVppV1Request vppV1Request) {
        LOGGER.info("Calling associate client to VPP call 1");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        try {
            HttpEntity<String> request = new HttpEntity<>(mapper.writeValueAsString(vppV1Request), headers);
            String response = getRestTemplate().postForObject(ASSOCIATE_VPP_V1_API,
                    request, String.class);
            try {
                AssociateVppV1Response vppUsingTokenResponse = mapper.readValue(response,
                        AssociateVppV1Response.class);
                if (vppUsingTokenResponse.getAssociations() != null) {
                    return true;
                }
            } catch (JsonProcessingException e) {
                try {
                    AssociateVppV1ErrorResponse vppErrorResponse = mapper.readValue(response,
                            AssociateVppV1ErrorResponse.class);
                    if (vppErrorResponse.getErrorNumber() == 9646) {
                        return true;
                    }
                } catch (JsonProcessingException ex) {
                    LOGGER.error("Response for associateVppV1 api could not be processed.", ex);
                }
            }
        } catch (JsonProcessingException e) {
            LOGGER.error("Exception occurred while mapping vppV1Request to json.", e);
        }
        return false;
    }

    /**
     * Gets the event status of associateOrDisassociateVpp method by calling GET_VPP_STATUS_API_PREFIX api
     *
     * @param eventId  of associateOrDisassociateVpp method call
     * @param vppToken
     * @return true if event status equals to complete
     */
    protected boolean appleVppEventStatus(final String eventId, final String vppToken) {
        String getVppStatusApiUrl = GET_VPP_STATUS_API_PREFIX + eventId;
        HttpHeaders headers = new HttpHeaders();
        headers.set(AUTHORIZATION, "Bearer {" + vppToken + "}");
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(headers);
        ResponseEntity<AppleVppEventStatusResponse> response = getRestTemplate().exchange(getVppStatusApiUrl,
                HttpMethod.GET, request, AppleVppEventStatusResponse.class);
        return response.getBody().getEventStatus().equals(COMPLETE);
    }
}
