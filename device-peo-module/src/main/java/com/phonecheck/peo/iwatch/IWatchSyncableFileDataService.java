package com.phonecheck.peo.iwatch;

import com.phonecheck.model.device.IWatchInfo;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.SupportFilePath;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;

import static com.phonecheck.model.constants.FileConstants.ALL_SYNCABLE_DATA_FILE_NAME;
import static com.phonecheck.model.constants.FileConstants.IOS_FILES_FOLDER;

@Service
@AllArgsConstructor
public class IWatchSyncableFileDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(IWatchSyncableFileDataService.class);
    private static final String IWATCH_INFO = "iwatchInfo";
    private static final String IWATCH_DIAGNOSTIC = "isWatchDignostic";

    private final FileUtil fileUtil;
    private final SupportFilePath supportFilePath;

    /**
     * Updates the allSyncAbleData.json file for the given IosDevice by adding or updating
     * the iWatchInfo object and setting a diagnostic flag.
     * This method reads the contents of the allSyncAbleData.json file, removes any existing
     * "iWatchInfo" object if present, and then adds the latest iWatch information provided by the
     * IosDevice object. It also sets a diagnostic flag ("iWatchDiagnostic") to true in the JSON.
     * The updated content is then written back to the file.
     *
     * @param device the IosDevice object
     */
    public void updateAllSyncAble(final IosDevice device) {
        try {
            LOGGER.info("Updating allSyncAbleData.json for iWatch");
            final File allSyncableFile = new File(supportFilePath.getPaths().getRootFolderPath() +
                    File.separator + IOS_FILES_FOLDER + File.separator +
                    device.getSerial() + File.separator + ALL_SYNCABLE_DATA_FILE_NAME);

            String allSyncAbleFileContent = fileUtil.readFile(allSyncableFile);

            IWatchInfo iWatchInfo = device.getIWatchInfo();
            JSONObject jsonObject = new JSONObject(allSyncAbleFileContent);
            if (jsonObject.has(IWATCH_INFO)) {
                jsonObject.remove(IWATCH_INFO);
            }
            jsonObject.put(IWATCH_INFO, createIWatchInfoMap(iWatchInfo));
            jsonObject.put(IWATCH_DIAGNOSTIC, true);

            fileUtil.writeStringToFile(allSyncableFile, jsonObject.toString());
        } catch (IOException e) {
            LOGGER.error("Error while updating allSyncAbleData.json file", e);
        }
    }

    /**
     * Creates a JSON representation of the object.
     * This method maps the fields of the provided instance
     * to a JSONObject. If the input is null, the method returns null.
     *
     * @param iwatch the IWatch object containing the data to be mapped
     * @return a JSONObject containing key-value pairs representing the
     * iWatch information, or null if the input  IWatch is null
     */
    private JSONObject createIWatchInfoMap(final IWatchInfo iwatch) {
        if (iwatch == null) {
            return null;
        }

        final JSONObject iWatchInfo = new JSONObject();
        iWatchInfo.put("ProductType", iwatch.getProductType());
        iWatchInfo.put("SerialNumber", iwatch.getSerial());
        iWatchInfo.put("BatteryCurrentCapacity", iwatch.getBatteryCapacity());
        iWatchInfo.put("BatteryIsCharging", iwatch.getChargingStatus());
        iWatchInfo.put("AmountDataAvailable", iwatch.getAmountDataAvailable());
        iWatchInfo.put("HardwareModel", iwatch.getHardwareModel());
        iWatchInfo.put("DeviceName", iwatch.getName());
        iWatchInfo.put("BluetoothAddress", iwatch.getBluetoothAddress());
        iWatchInfo.put("RegionInfo", iwatch.getRegionInfo());
        iWatchInfo.put("UniqueDeviceID", iwatch.getUniqueDeviceID());
        iWatchInfo.put("ModelNumber", iwatch.getModelNumber());
        iWatchInfo.put("ProductName", iwatch.getProductName());
        iWatchInfo.put("WiFiAddress", iwatch.getWifiAddress());
        iWatchInfo.put("ProductVersion", iwatch.getProductVersion());
        iWatchInfo.put("TotalDataCapacity", iwatch.getTotalDataCapacity());
        return iWatchInfo;
    }

}
