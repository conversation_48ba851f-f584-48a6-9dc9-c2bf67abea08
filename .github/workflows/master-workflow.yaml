name: Master Build

on:
  release:
    types: [ published ]

jobs:
  master-data-plane-controller:
    uses: databahn-ai/ci-github-workflows/.github/workflows/build-java-service.yaml@master
    with:
      service_name: data-plane-controller
      service_version: "${{ github.event.release.tag_name }}"
      build_dir: data-plane
    secrets: inherit


  master-control-plane-controller:
    uses: databahn-ai/ci-github-workflows/.github/workflows/build-java-service.yaml@master
    with:
      service_name: control-plane-controller
      service_version: "${{ github.event.release.tag_name }}"
      build_dir: control-plane
    secrets: inherit
