package com.phonecheck.device.results;

import com.phonecheck.command.device.ios.pair.IosPairDeviceCommand;
import com.phonecheck.command.device.ios.pair.IosValidatePairDeviceCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.status.PairStatus;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.parser.device.ios.pair.IosPairDeviceParser;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;

@Service
@AllArgsConstructor
public class IosPairDeviceService {

    private final CommandExecutor executor;
    private final IosPairDeviceParser iosPairDeviceParser;

    private TimerLoggerUtil timerLoggerUtil;

    /**
     * Command to pair an ios device
     *
     * @param device target device
     * @return pair status
     * @throws IOException in case of failure
     */
    public PairStatus pairDevice(final IosDevice device) throws IOException {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Pair device exe (idevicepair) pair operation start",
                exeStartTime);
        final String output = executor.execute(new IosPairDeviceCommand(device.getId()), 10);
        if (output != null) {
            timerLoggerUtil.printTimerLog(device.getId(),
                    "Pair device exe (idevicepair) pair operation end",
                    exeStartTime);
            timerLoggerUtil.printTimerLog(device.getId(), output.trim(), exeStartTime);
            // Parse pairing status from output
            return iosPairDeviceParser.parse(output);
        } else {
            return PairStatus.FAILED_NO_DEVICE;
        }
    }

    /**
     * Checks if device already paired or not, by validating the pair status
     *
     * @param device target device
     * @return boolean containing device pair status
     * @throws IOException when the output couldn't be read
     */
    public PairStatus validatePair(final IosDevice device) throws IOException {
        LocalDateTime exeStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(),
                "Pair device exe (idevicepair) validate operation start",
                exeStartTime);
        final String output = executor.execute(new IosValidatePairDeviceCommand(device.getId()), 10);
        if (output != null) {
            timerLoggerUtil.printTimerLog(device.getId(),
                    "Pair device exe (idevicepair) validate operation end",
                    exeStartTime);
            // Parse pairing status from output
            return iosPairDeviceParser.parse(output);
        } else {
            return PairStatus.FAILED_NO_DEVICE;
        }
    }
}

