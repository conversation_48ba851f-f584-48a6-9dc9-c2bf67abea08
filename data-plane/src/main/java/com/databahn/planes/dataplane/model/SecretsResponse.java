package com.databahn.planes.dataplane.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SecretsResponse {
  @JsonProperty("errors")
  private Map<String, String> errors = new HashMap<>();

  @JsonProperty("secrets")
  private Map<String, Map<String, String>> secrets = new HashMap<>();
}
