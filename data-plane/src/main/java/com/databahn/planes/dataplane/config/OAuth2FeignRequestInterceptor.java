package com.databahn.planes.dataplane.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.client.*;

@Slf4j
public class OAuth2FeignRequestInterceptor implements RequestInterceptor {

  private final String registrationId;
  private final OAuth2AuthorizedClientManager clientManager;

  public OAuth2FeignRequestInterceptor(
      String registrationId, OAuth2AuthorizedClientManager clientManager) {
    this.registrationId = registrationId;
    this.clientManager = clientManager;
  }

  @Override
  public void apply(RequestTemplate requestTemplate) {
    String accessToken = getAccessToken();
    if (accessToken != null) {
      requestTemplate.header("Authorization", "Bearer " + accessToken);
    }
  }

  public String getAccessToken() {
    OAuth2AuthorizeRequest authorizeRequest =
        OAuth2AuthorizeRequest.withClientRegistrationId(this.registrationId)
            .principal("feignClient")
            .build();
    OAuth2AuthorizedClient authorizedClient = this.clientManager.authorize(authorizeRequest);
    if (authorizedClient != null && authorizedClient.getAccessToken() != null) {
      return authorizedClient.getAccessToken().getTokenValue();
    } else {
      throw new IllegalArgumentException("Authorization failed for client: " + this.registrationId);
    }
  }
}
