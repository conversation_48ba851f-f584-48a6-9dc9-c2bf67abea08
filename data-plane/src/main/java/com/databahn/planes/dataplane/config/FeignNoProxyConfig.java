package com.databahn.planes.dataplane.config;

import feign.Client;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class FeignNoProxyConfig {

  @Bean("feignNoProxyClient")
  public Client feignNoProxyClient() {
    log.info("Feign client configured without proxy");
    return new Client.Default(null, null);
  }
}
