package com.databahn.planes.dataplane.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
@ConfigurationProperties(prefix = "tokens")
public class ControlPlaneTokens {
  private List<ControlPlaneToken> controlPlane;

  @Data
  public static class ControlPlaneToken {
    private String tenantId;
    private String clientId;
    private String clientSecret;
  }
}
