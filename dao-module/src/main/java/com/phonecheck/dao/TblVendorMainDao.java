package com.phonecheck.dao;

import com.phonecheck.dao.mapper.TblVendorMainRowMapper;
import com.phonecheck.dao.model.TblVendorMain;
import com.phonecheck.model.transaction.Transaction;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class TblVendorMainDao extends AbstractDao {
    private static final TblVendorMainRowMapper ROW_MAPPER = new TblVendorMainRowMapper();
    private static final int NO_OF_COLUMNS = 9;
    private static final String DELETE_VENDOR_MAIN_SQL = "DELETE FROM \"TBLVendorMain\" " +
            "WHERE \"TransactionID\" = ?";
    private static final String INSERT_VENDOR_MAIN_SQL = appendQuestionMarkToSql(
            new StringBuilder("INSERT INTO \"TBLVendorMain\" VALUES ("),
            NO_OF_COLUMNS);
    private static final String UPSERT_VENDOR_INVOICE_SQL = "MERGE INTO \"TBLVendorMain\" (\"TransactionID\", "
            + "\"VendorName\", \"InvoiceNo\", \"BoxNo\", \"QTY\", \"StationID\") "
            + "KEY (\"TransactionID\", \"StationID\") VALUES(?, ?, ?, ?, ?, ?)";

    private static final String SELECT_VENDOR_MAIN_RECORDS_SQL = "SELECT * FROM \"TBLVendorMain\"" +
            " ORDER BY \"TransactionID\" DESC";

    private static final String SELECT_VENDOR_RECORD_TRANSACTION_ID_SQL = "SELECT * FROM \"TBLVendorMain\"" +
            " WHERE \"TransactionID\" = ?";

    public TblVendorMainDao(@Qualifier("phonecheckJdbcTemplate") final JdbcTemplate jdbcTemplate) {
        super(jdbcTemplate);
    }

    /**
     * Batch insert data into TBLVendorMain
     *
     * @param tblVendorMain object
     */
    public void insert(final TblVendorMain tblVendorMain) {
        getJdbcTemplate().update(INSERT_VENDOR_MAIN_SQL,
                ps -> {
                    int index = 0;
                    ps.setString(++index, tblVendorMain.getVendorName());
                    ps.setString(++index, tblVendorMain.getInvoiceNo());
                    ps.setString(++index, tblVendorMain.getTransactionDate());
                    ps.setString(++index, tblVendorMain.getTransactionId());
                    ps.setString(++index, tblVendorMain.getLicenseID());
                    ps.setString(++index, tblVendorMain.getStationID());
                    ps.setString(++index, tblVendorMain.getBoxNo());
                    ps.setString(++index, tblVendorMain.getQty());
                    ps.setBoolean(++index, tblVendorMain.isDeleted());
                });
    }

    /**
     * Delete transaction from vendor main
     *
     * @param transactionId to be deleted
     */
    public void deleteTransaction(final String transactionId) {
        getJdbcTemplate().update(DELETE_VENDOR_MAIN_SQL, transactionId);
    }

    /**
     * Updates vendor name, invoice no., qty and box no.
     *
     * @param transaction current transaction
     */
    public synchronized void updateVendorInvoice(final Transaction transaction) {
        getJdbcTemplate().update(UPSERT_VENDOR_INVOICE_SQL, transaction.getTransactionId(),
                transaction.getVendorName(),
                transaction.getInvoiceNo(),
                transaction.getBoxNo(),
                transaction.getQty(),
                transaction.getStationId());
    }

    /**
     * Get all the transaction records from database
     *
     * @return list of TblVendorMain
     */
    public List<TblVendorMain> getVendorMainRecords() {
        List<TblVendorMain> vendorMainRecords = getJdbcTemplate()
                .query(
                        SELECT_VENDOR_MAIN_RECORDS_SQL,
                        ROW_MAPPER
                );

        if (vendorMainRecords.size() > 0) {
            return vendorMainRecords;
        }

        return null;
    }

    /**
     * Get transaction record from database by transactionId
     *
     * @param transactionId transaction id
     * @return TblVendorMain
     */
    public TblVendorMain getVendorMainRecordByTransactionId(final Integer transactionId) {
        List<TblVendorMain> vendorMainRecords = getJdbcTemplate()
                .query(
                        SELECT_VENDOR_RECORD_TRANSACTION_ID_SQL,
                        ROW_MAPPER, String.valueOf(transactionId)
                );

        if (vendorMainRecords.size() > 0) {
            return vendorMainRecords.get(0);
        }

        return null;
    }
}
