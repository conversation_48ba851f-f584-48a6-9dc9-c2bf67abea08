package com.phonecheck.dao.service.lookup;

import com.phonecheck.dao.TblCarrierDao;
import com.phonecheck.dao.TblDeviceInfoDao;
import com.phonecheck.dao.model.TblCarrier;
import com.phonecheck.dao.model.TblCarrierFilter;
import com.phonecheck.dao.model.TblDeviceInfo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class DeviceCarrierDBLookupService {

    private final TblCarrierDao tblCarrierDao;
    private final TblDeviceInfoDao tblDeviceInfoDao;
    private final CarrierFilterDBLookupService carrierFilterDBLookupService;

    public String getFilteredCarrier(final String carrier, final boolean filterFromDb) {
        String filteredCarrier = carrier;

        if (filterFromDb) {
            TblCarrierFilter tblCarrierFilter = carrierFilterDBLookupService.getCarrierFilter(carrier);
            if (tblCarrierFilter != null && StringUtils.isNotEmpty(tblCarrierFilter.getResolvedCarrier())) {
                filteredCarrier = tblCarrierFilter.getResolvedCarrier();
            }
        }

        // return the finalized carrier name
        return filteredCarrier != null ? filteredCarrier.replace("SPR", "Sprint")
                .replace("120", "Sprint")
                .replace("410", "At&t")
                .replace("480", "Verizon")
                .replace("260", "T-Mobile")
                .replace("ATT", "AT&T")
                .replace("att", "AT&T")
                .replace("04", "Verizon")
                .replace("Zeplen", "Verizon")
                .replace("Zeppelin", "Verizon")
                .replace("65535", "AT&T") : null;
    }

    /**
     * Get Carrier based on the following params
     *
     * @param transactionId transaction id
     * @param deviceId      device identifier
     * @param modelNumber   device model number
     * @param region        device region
     * @return Device carrier string
     */
    public String getIosDeviceCarrier(final String transactionId, final String deviceId, final String modelNumber,
                                      final String region) {
        String carrier = null;
        boolean filterFromDb = false;

        // First try to retrieve device carrier from already saved device info record in same transaction
        TblDeviceInfo deviceInfo = tblDeviceInfoDao.getDeviceFromDB(transactionId, deviceId);
        if (deviceInfo != null && StringUtils.isNotBlank(deviceInfo.getCarrier())) {
            carrier = deviceInfo.getCarrier();
        } else {
            // If carrier not found in same transaction device info record,
            // then retrieve it from lookup table based on Model No. and region
            TblCarrier tblCarrier = tblCarrierDao.getCarrierFromDB(modelNumber, region);
            if (tblCarrier != null) {
                carrier = tblCarrier.getCarrier();
                filterFromDb = true;
            }
        }

        return getFilteredCarrier(carrier, filterFromDb);
    }

    /**
     * Get Carrier based on the following params
     *
     * @param transactionId transaction id
     * @param deviceId      device identifier
     * @return Device carrier string
     */
    public String getAndroidDeviceCarrier(final String transactionId, final String deviceId) {
        String carrier = null;

        // First try to retrieve device carrier from already saved device info record in same transaction
        TblDeviceInfo deviceInfo = tblDeviceInfoDao.getDeviceFromDB(transactionId, deviceId);
        if (deviceInfo != null && StringUtils.isNotBlank(deviceInfo.getCarrier())) {
            carrier = deviceInfo.getCarrier();
        }
        // return the finalized carrier name
        return carrier;
    }

    /**
     * Method to insert a list of Carrier data to TblCarrier table
     *
     * @param tblCarrierList inserts the given list to TblCarrier table
     */
    public void insertCarriers(final List<TblCarrier> tblCarrierList) {
        tblCarrierDao.insertDataIntoTblCarrier(tblCarrierList);
    }

    /**
     * Returns the number of records in the table
     * @return no. of records
     */
    public int getRecordCount() {
        return tblCarrierDao.getRecordCount();
    }

    /**
     * Method to delete data from TblCarrier table
     */
    public void deleteCarriers() {
        tblCarrierDao.deleteDataFromTblCarrier();
    }
}
