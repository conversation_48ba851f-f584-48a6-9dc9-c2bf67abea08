package com.phonecheck.dao.service.lookup;

import com.phonecheck.dao.TblSimTechnologyDao;
import com.phonecheck.dao.model.TblSimTechnology;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class NetworkDBLookupService {
    private final TblSimTechnologyDao tblSimTechnologyDao;

    public void insertSimInfo(final List<TblSimTechnology> tblSimTechnologyList) {
        tblSimTechnologyDao.insert(tblSimTechnologyList);
    }

    public void truncateSimInfo() {
        tblSimTechnologyDao.truncate();
    }

    public TblSimTechnology getSimTechnologyInfo(final String modelNo, final String regulatoryNo) {
        return tblSimTechnologyDao.getSimTechnologyByModelAndRegulatoryNo(modelNo, regulatoryNo);
    }

    public TblSimTechnology getRegulatoryModel(final String modelNo) {
        return tblSimTechnologyDao.getRegulatoryModelNo(modelNo);
    }

    /**
     * Returns the number of records in the table
     * @return no. of records
     */
    public int getRecordCount() {
        return tblSimTechnologyDao.getRecordCount();
    }
}
