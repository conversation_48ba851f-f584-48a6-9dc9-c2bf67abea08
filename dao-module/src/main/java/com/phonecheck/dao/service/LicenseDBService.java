package com.phonecheck.dao.service;

import com.phonecheck.dao.LicenseChargeDao;
import com.phonecheck.dao.model.LicenseCharge;
import com.phonecheck.model.device.DeviceType;
import com.phonecheck.model.ios.LicenseType;
import com.phonecheck.model.store.InMemoryStore;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Service class to call DAO methods for the table LicenseCharge
 */
@Service
@AllArgsConstructor
public class LicenseDBService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseDBService.class);
    private static final Logger LICENSE_LOGGER = LoggerFactory.getLogger("LicenseLogger");
    private InMemoryStore inMemoryStore;
    private LicenseChargeDao licenseChargeDao;

    public void createLicenseChargeInDB(final String deviceId, final String licenseIdentifier,
                                        final String imei, final LicenseType licenseType,
                                        final DeviceType deviceType, final String model) {
        LicenseCharge licenseCharge = LicenseCharge.builder()
                .deviceId(deviceId)
                .licenseIdentifier(licenseIdentifier)
                .imei(imei)
                .licenseType(licenseType.name())
                .deviceType(deviceType.name())
                .model(model)
                .userName(inMemoryStore.getUserName())
                .buildNo(inMemoryStore.getBuildNo())
                .testerId(inMemoryStore.getTesterId())
                .warehouseId(inMemoryStore.getWarehouseId())
                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                .build();

        LOGGER.info("Insert new license request in DB: {}", licenseCharge);
        LICENSE_LOGGER.info("Insert new license request in DB: {}", licenseCharge);
        licenseChargeDao.createRecord(licenseCharge);
    }

    public void updateLicenseCharge(final LicenseCharge licenseCharge) {
        licenseCharge.incrementRetries();
        LOGGER.info("Update old license request in DB: {}", licenseCharge);
        LICENSE_LOGGER.info("Update old license request in DB: {}", licenseCharge);
        licenseChargeDao.updateRecord(licenseCharge);
    }

    public List<LicenseCharge> getPendingLicenseCharges() {
        LOGGER.info("Fetch pending license requests from DB");
        LICENSE_LOGGER.info("Fetch pending license requests from DB");
        return licenseChargeDao.getPendingRecords();
    }

    public int deleteOldChargedLicenses() {
        LOGGER.info("Delete old successfully charged license requests from DB");
        LICENSE_LOGGER.info("Delete old successfully charged license requests from DB");
        return licenseChargeDao.deleteOldChargedRecords();
    }
}
