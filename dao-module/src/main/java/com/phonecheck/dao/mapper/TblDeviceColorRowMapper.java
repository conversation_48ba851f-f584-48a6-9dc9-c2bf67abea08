package com.phonecheck.dao.mapper;

import com.phonecheck.dao.model.TblDeviceColor;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class TblDeviceColorRowMapper implements RowMapper<TblDeviceColor> {
    @Override
    public TblDeviceColor mapRow(final ResultSet resultSet, final int rowNum) throws SQLException {
        return TblDeviceColor.builder().
                colorId(resultSet.getString("color_id")).
                deviceTitle(resultSet.getString("device_title")).
                make(resultSet.getString("make")).
                modelName(resultSet.getString("model_name")).
                modelNumber(resultSet.getString("model_number")).
                serialNumber(resultSet.getString("serial_number")).
                imei(resultSet.getString("imei")).
                color(resultSet.getString("color")).
                colorCode(resultSet.getString("color_code")).
                deleted(resultSet.getBoolean("Deleted")).build();
    }
}
