package com.phonecheck.dao.mapper;

import com.phonecheck.dao.model.TblApiRecordsVersions;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

public class TblApiRecordsVersionsMapper implements RowMapper<TblApiRecordsVersions> {
    @Override
    public TblApiRecordsVersions mapRow(final ResultSet resultSet, final int rowNum) throws SQLException {
        return TblApiRecordsVersions.builder()
                .name(resultSet.getString("Name"))
                .url(resultSet.getString("URL"))
                .version(resultSet.getFloat("Version"))
                .updatedDate((resultSet.getTimestamp("UpdatedDate")))
                .build();
    }
}
