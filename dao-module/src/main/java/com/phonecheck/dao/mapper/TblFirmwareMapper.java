package com.phonecheck.dao.mapper;

import com.phonecheck.dao.model.TblFirmwareInfo;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Mapper class for the table TblFirmwareInfo
 */
public class TblFirmwareMapper implements RowMapper<TblFirmwareInfo> {
    @Override
    public TblFirmwareInfo mapRow(final ResultSet resultSet, final int rowNum) throws SQLException {
        return TblFirmwareInfo.builder()
                .firmwareId(resultSet.getString("firmwareId"))
                .fileHash(resultSet.getString("fileHash"))
                .folderHash(resultSet.getString("folderHash"))
                .fileName(resultSet.getString("fileName"))
                .version(resultSet.getString("version"))
                .build();
    }
}
