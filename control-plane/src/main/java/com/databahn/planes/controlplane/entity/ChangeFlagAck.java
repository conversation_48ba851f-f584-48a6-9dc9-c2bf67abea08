package com.databahn.planes.controlplane.entity;

import com.databahn.planes.model.replay.ReplayStatus;
import jakarta.persistence.*;
import java.time.Instant;
import java.util.UUID;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UuidGenerator;
import org.hibernate.type.SqlTypes;

@Data
@Entity
@Table(name = "change_flag_acks", indexes = @Index(columnList = "process_status, timestamp"))
public class ChangeFlagAck {
  @Id
  @Column(name = "id")
  @UuidGenerator
  private UUID id;

  @Column(name = "request_id")
  private String requestId;

  @Column(name = "entity_id")
  private String entityId;

  @Column(name = "status")
  private String status;

  @Column(name = "entity_type")
  private String entityType;

  @Column(name = "entity_version")
  private String entityVersion;

  @Column(name = "service_name")
  private String serviceName;

  @Column(name = "tenant_id")
  private String tenantId;

  @Column(name = "action")
  private String action;

  @Column(name = "timestamp")
  private Instant timestamp;

  @Column(name = "error")
  public String error;

  @Column(name = "process_status")
  public String processStatus = "PENDING";

  @Column(name = "replay_status", columnDefinition = "json")
  @JdbcTypeCode(SqlTypes.JSON)
  public ReplayStatus replayStatus;
}
