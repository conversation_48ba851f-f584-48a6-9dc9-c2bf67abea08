package com.databahn.planes.controlplane.entity;

import com.databahn.planes.controlplane.common.ChangeFlagEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.Data;

@Data
public class DataReplayChangeFlag implements ChangeFlagEntity {

  private String id;

  @JsonProperty("tenant_id")
  private String tenantId;

  @JsonProperty("connector_id")
  private String connectorId;

  @JsonProperty("fleet_id")
  private String fleetId;

  private String action;
  private String source;
  private String destination;

  @JsonProperty("request_id")
  private String requestId;

  @JsonProperty("bucket_name")
  private String bucketName;

  @JsonProperty("bucket_prefix")
  private String bucketPrefix;

  @JsonProperty("access_key_id")
  private String accessKeyId;

  @JsonProperty("secret_access_key")
  private String secretAccessKey;

  private String region;

  @JsonProperty("file_name")
  private String fileName;

  @JsonProperty("device_type")
  private String deviceType;

  @JsonProperty("device_vendor")
  private String deviceVendor;

  @JsonProperty("log_type")
  private String logType;

  @JsonProperty("replay_type")
  private String replayType;

  @Override
  public UUID getSourceId() {
    return UUID.fromString(source);
  }

  @Override
  public boolean sendPipelineChangeFlag() {
    return false;
  }
}
