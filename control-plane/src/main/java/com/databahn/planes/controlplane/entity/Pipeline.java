package com.databahn.planes.controlplane.entity;

import com.databahn.planes.model.constants.Status;
import jakarta.persistence.*;
import java.util.Date;
import java.util.UUID;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.UuidGenerator;

@Data
@Entity
@NoArgsConstructor
@Table(name = "pipelines")
public class Pipeline {
  @Id
  @Column(name = "id", length = 36, unique = true, nullable = false)
  @UuidGenerator
  private UUID id;

  @Column(name = "name", length = 256)
  private String name;

  @Column(name = "description", length = 255)
  private String description;

  @Column(name = "device_type", length = 100)
  private String deviceType;

  @Column(name = "vendor", length = 100)
  private String vendor;

  @Column(name = "log_source_type", length = 100)
  private String logSourceType;

  @Column(name = "tenant_id", length = 36)
  private UUID tenantId;

  @Column(name = "customer_id", length = 36)
  private UUID customerId;

  @Column(name = "updated_at")
  @UpdateTimestamp
  private Date updatedAt;

  @Column(name = "created_at")
  @CreationTimestamp
  private Date createdAt;

  @Column(name = "status", length = 20)
  @Enumerated(EnumType.STRING)
  private Status status;
}
