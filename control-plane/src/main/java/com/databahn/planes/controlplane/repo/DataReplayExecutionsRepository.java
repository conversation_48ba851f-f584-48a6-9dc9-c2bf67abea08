package com.databahn.planes.controlplane.repo;

import com.databahn.planes.controlplane.entity.ReplayJobExecution;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface DataReplayExecutionsRepository extends JpaRepository<ReplayJobExecution, UUID> {
  Optional<ReplayJobExecution> findReplayJobExecutionByFileNameAndPartId(
      String fileName, UUID partId);

  @Modifying(clearAutomatically = true)
  @Query(
      "SELECT DISTINCT execution.job.id  FROM ReplayJobExecution execution WHERE execution.partId=:partId ")
  List<UUID> findDistinctJobIdByPartId(@Param("partId") UUID partId);

  @Query(
      "SELECT DISTINCT  execution.status , count (execution.status) from ReplayJobExecution execution  where execution.job.id =:jobId group by execution.status")
  List<Object[]> getStatusAndCountByJobId(@Param("jobId") UUID jobId);

  @Query(
      "SELECT  SUM(execution.lines) from ReplayJobExecution execution  where execution.job.id =:jobId")
  Optional<Object[]> getSumOfDataByJobId(@Param("jobId") UUID jobId);
}
