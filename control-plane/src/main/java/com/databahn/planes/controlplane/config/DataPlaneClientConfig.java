package com.databahn.planes.controlplane.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cloud.openfeign.security.OAuth2AccessTokenInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.security.oauth2.client.AuthorizedClientServiceOAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;

public class DataPlaneClientConfig {

  @Bean
  @ConditionalOnBean({OAuth2AuthorizedClientService.class, ClientRegistrationRepository.class})
  @ConditionalOnMissingBean
  OAuth2AuthorizedClientManager feignOAuth2AuthorizedClientManager(
      ClientRegistrationRepository clientRegistrationRepository,
      OAuth2AuthorizedClientService oAuth2AuthorizedClientService) {
    return new AuthorizedClientServiceOAuth2AuthorizedClientManager(
        clientRegistrationRepository, oAuth2AuthorizedClientService);
  }

  @Bean
  @ConditionalOnBean({OAuth2AuthorizedClientManager.class})
  public OAuth2AccessTokenInterceptor defaultOAuth2AccessTokenInterceptor(
      @Value("${spring.cloud.openfeign.oauth2.clientRegistrationId:}") String clientRegistrationId,
      OAuth2AuthorizedClientManager oAuth2AuthorizedClientManager) {
    return new OAuth2AccessTokenInterceptor(clientRegistrationId, oAuth2AuthorizedClientManager);
  }
}
