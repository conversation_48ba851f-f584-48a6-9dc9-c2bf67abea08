package com.databahn.planes.controlplane.controller;

import com.databahn.planes.controlplane.service.DiscoveryService;
import com.databahn.planes.model.discovery.Devices;
import com.databahn.planes.response.Response;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/v1/discovery")
public class DiscoveryController {

  private DiscoveryService discoveryService;

  @PostMapping()
  public Response<?> saveDiscoverDevices(@RequestBody Devices discovery) {
    if (discovery == null || discovery.discovery() == null) {
      return Response.Ok("Nothing to save");
    }
    discoveryService.postDiscoveryToKafka(discovery.discovery());
    return Response.Message("Devices saved");
  }
}
