package com.databahn.planes.controlplane.controller;

import com.databahn.planes.controlplane.service.AckService;
import com.databahn.planes.model.ack.Ack;
import com.databahn.planes.response.Response;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/v1/ack")
public class AckController {

  private final AckService ackService;

  @PostMapping()
  public Response<?> saveAck(@RequestBody List<Ack> ack) {
    if (CollectionUtils.isEmpty(ack)) {
      return Response.Message("Nothing to save");
    }
    ackService.saveAck(ack);
    return Response.Message("Ack saved " + ack.size() + " records");
  }
}
