package com.databahn.planes.controlplane.repo;

import com.databahn.planes.controlplane.entity.LookupIndexCacheRequest;
import com.databahn.planes.controlplane.entity.LookupIndexCacheRequestKey;
import com.databahn.planes.controlplane.model.LookupCacheRequestWithIndex;
import com.databahn.planes.model.constants.LookupRequestType;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface LookupIndexCacheRequestRepository
    extends JpaRepository<LookupIndexCacheRequest, LookupIndexCacheRequestKey> {

  @Query(
      "SELECT new com.databahn.planes.controlplane.model.LookupCacheRequestWithIndex(r, r.lookup.type) FROM LookupIndexCacheRequest r "
          + " WHERE r.lookup.tenantId = :tenantId AND r.type = 'CACHE' "
          + " AND EXISTS (SELECT 1 FROM LookupIndexCacheRequest ir WHERE ir.operationId = r.operationId AND ir.lookup.id = r.lookup.id AND ir.type = 'INDEX' AND ir.requestStatus = 'ACTIVE') "
          + " AND NOT EXISTS (SELECT 1 FROM LookupIndexCacheRequest ar WHERE ar.type = 'CACHE' AND ar.createdAt > r.createdAt AND ar.lookup.id = r.lookup.id and ar.type != r.type) "
          + " AND NOT EXISTS (SELECT 1 FROM LookupCacheRequestDataPlaneMapping mp WHERE mp.requestId = r.id AND mp.tenantId = :tenantId AND mp.dataPlaneId = :dataPlaneId AND mp.status = 'ACTIVE') "
          + " ORDER BY r.createdAt ASC")
  List<LookupCacheRequestWithIndex> findAllPendingRequests(
      @Param("tenantId") UUID tenantId, @Param("dataPlaneId") UUID dataPlaneId);

  List<LookupIndexCacheRequest> findByOperationIdAndType(UUID operationId, LookupRequestType type);

  List<LookupIndexCacheRequest> findById(UUID id);
}
