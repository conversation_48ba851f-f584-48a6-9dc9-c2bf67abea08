/* NSExecutableNotLoadableError */
"BundleErr3584" = "Не удалось загрузить пакет «%@», так как исполняемый файл недоступен для загрузки.";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "Исполняемый файл пакета недоступен для загрузки.";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "Попытайтесь переустановить пакет.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "Не удалось загрузить пакет «%@», так как он не содержит версии для существующей архитектуры.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "Пакет не содержит версии для существующей архитектуры.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "Попытайтесь установить универсальную версию пакета.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "Не удалось загрузить пакет «%@», так как он несовместим с существующей архитектурой.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "Пакет несовместим с этой программой.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "Попытайтесь установить более новую версию пакета.";

/* NSExecutableLoadError */
"BundleErr3587" = "Не удалось загрузить пакет «%@», так как он поврежден или отсутствуют необходимые ресурсы.";

/* NSExecutableLoadError */
"BundleErr3587-C" = "Пакет поврежден или отсутствуют необходимые ресурсы.";

/* NSExecutableLoadError */
"BundleErr3587-R" = "Попытайтесь переустановить пакет.";

/* NSExecutableLinkError */
"BundleErr3588" = "Не удалось загрузить пакет «%@».";

/* NSExecutableLinkError */
"BundleErr3588-C" = "Не удалось загрузить пакет.";

/* NSExecutableLinkError */
"BundleErr3588-R" = "Попытайтесь переустановить пакет.";

/* NSFileNoSuchFileError */
"BundleErr4" = "Не удалось загрузить пакет «%@», так как не найден исполняемый файл.";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "Исполняемый файл пакета не найден.";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "Попытайтесь переустановить пакет.";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Мах";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "Не удалось завершить операцию. %@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "Не удалось завершить операцию. (%1$@, ошибка %2$ld - %3$@)";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "Не удалось завершить операцию. (%1$@, ошибка %2$ld)";

