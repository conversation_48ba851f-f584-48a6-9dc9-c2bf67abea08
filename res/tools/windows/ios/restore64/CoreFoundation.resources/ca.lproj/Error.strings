/* NSExecutableNotLoadableError */
"BundleErr3584" = "El paquet “%@” no s’ha pogut carregar perquè el seu executable no és carregable.";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "L’executable del paquet no es pot carregar.";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "Proveu de tornar a instal·lar el paquet.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "El paquet “%@” no s’ha pogut carregar perquè no conté cap versió per a l’arquitectura actual.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "El paquet no conté cap versió per a l’arquitectura actual.";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "Proveu d’instal·lar una versió universal del paquet.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "El paquet “%@” no s’ha pogut carregar perquè no és compatible amb l’aplicació actual.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "El paquet no és compatible amb aquesta aplicació.";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "Proveu d’instal·lar una versió més recent del paquet.";

/* NSExecutableLoadError */
"BundleErr3587" = "El paquet “%@” no s’ha pogut carregar perquè està malmès o hi falten recursos necessaris.";

/* NSExecutableLoadError */
"BundleErr3587-C" = "El paquet està malmès o hi falten recursos necessaris.";

/* NSExecutableLoadError */
"BundleErr3587-R" = "Proveu de tornar a instal·lar el paquet.";

/* NSExecutableLinkError */
"BundleErr3588" = "No s’ha pogut carregar el paquet “%@”.";

/* NSExecutableLinkError */
"BundleErr3588-C" = "No s’ha pogut carregar el paquet.";

/* NSExecutableLinkError */
"BundleErr3588-R" = "Proveu de tornar a instal·lar el paquet.";

/* NSFileNoSuchFileError */
"BundleErr4" = "El paquet “%@” no s’ha pogut carregar perquè no s’ha pogut trobar el seu executable.";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "No s’ha trobat l’executable del paquet.";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "Proveu de tornar a instal·lar el paquet.";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Mach";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "No s’ha pogut completar l’operació. %@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "No s’ha pogut completar l’operació. (Error %1$@ %2$ld - %3$@)";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "No s’ha pogut completar l’operació. (Error %1$@ %2$ld.)";

