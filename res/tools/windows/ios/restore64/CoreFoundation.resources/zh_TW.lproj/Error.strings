/* NSExecutableNotLoadableError */
"BundleErr3584" = "無法載入套件「%@」，因為套件的執行檔無法載入。";

/* NSExecutableNotLoadableError */
"BundleErr3584-C" = "無法載入套件的執行檔。";

/* NSExecutableNotLoadableError */
"BundleErr3584-R" = "請試著重新安裝此套件。";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585" = "無法載入套件「%@」，因為此套件沒有目前架構的版本。";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-C" = "此套件沒有目前架構的版本。";

/* NSExecutableArchitectureMismatchError */
"BundleErr3585-R" = "請試著安裝此套件的通用版本。";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586" = "無法載入套件「%@」，因為此套件與現有的應用程式不相容。";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-C" = "套件與此應用程式不相容。";

/* NSExecutableRuntimeMismatchError */
"BundleErr3586-R" = "請試著安裝此套件較新的版本。";

/* NSExecutableLoadError */
"BundleErr3587" = "無法載入套件「%@」，因為此套件已損毀或遺失必要的資源。";

/* NSExecutableLoadError */
"BundleErr3587-C" = "此套件已損毀或遺失必要的資源。";

/* NSExecutableLoadError */
"BundleErr3587-R" = "請試著重新安裝此套件。";

/* NSExecutableLinkError */
"BundleErr3588" = "無法載入套件「%@」。";

/* NSExecutableLinkError */
"BundleErr3588-C" = "無法載入套件。";

/* NSExecutableLinkError */
"BundleErr3588-R" = "請試著重新安裝此套件。";

/* NSFileNoSuchFileError */
"BundleErr4" = "無法載入套件「%@」，因為找不到套件的執行檔。";

/* NSFileNoSuchFileError */
"BundleErr4-C" = "找不到套件的執行檔。";

/* NSFileNoSuchFileError */
"BundleErr4-R" = "請試著重新安裝此套件。";

/* Name of the 'Cocoa' error domain when showing to user. Very likely this will not get localized. */
"NSCocoaErrorDomain" = "Cocoa";

/* Name of the 'Core Foundation' error domain when showing to user. Very likely this will not get localized differently in other languages. */
"NSCoreFoundationErrorDomain" = "Core Foundation";

/* Name of the 'Mach' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'Mach' in the language. */
"NSMachErrorDomain" = "Mach";

/* Name of the 'OSStatus' error domain when showing to user. Very likely this will not get localized. */
"NSOSStatusErrorDomain" = "OSStatus";

/* Name of the 'POSIX' error domain when showing to user. This probably will not get localized, unless there is a generally recognized phrase for 'POSIX' in the language. */
"NSPOSIXErrorDomain" = "POSIX";

/* A generic error string indicating there was a problem. The %@ will be replaced by a second sentence which indicates why the operation failed. */
"The operation couldn\\U2019t be completed. %@" = "無法完成作業。%@";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain, code, and a description when there is no other way to present an error to the user. The first %@ indicates the error domain, %ld indicates the error code, and the second %@ indicates the description; so this might become '(Mach error 42 - Server error.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld - %@)" = "無法完成作業。（%1$@ 錯誤 %2$ld - %3$@）";

/* A generic error string indicating there was a problem, followed by a parenthetical sentence which indicates error domain and code when there is no other way to present an error to the user. The %@ indicates the error domain while %ld indicates the error code; so this might become '(Mach error 42.)' for instance. */
"The operation couldn\\U2019t be completed. (%@ error %ld.)" = "無法完成作業。（%1$@ 錯誤 %2$ld 。）";

