/* This computer's Internet connection appears to be offline. */
"CONNECTION_DOWN" = "נראה שהחיבור של מחשב זה לאינטרנט אינו מקוון.";

/* This computer's Internet connection may be offline. */
"CONNECTION_INDETERMINATE" = "ייתכן שהחיבור של מחשב זה לאינטרנט אינו מקוון.";

/* This computer's Internet connection appears to be online. */
"CONNECTION_UP" = "החיבור של מחשב זה לאינטרנט כנראה אינו מקוון.";

/* This computer's DNS server is not responding. */
"NAMESERVER_DOWN" = "שרת ה-DNS של מחשב זה אינו מגיב.";

/* The DNS server could not find the server name. */
"NAMELOOKUP_FAILED" = "שרת ה-DNS לא מצא את שם השרת.";

/* This computer's router is not responding. */
"ROUTER_DOWN" = "הנתב של מחשב זה אינו מגיב.";

/* The server this computer is attempting to connect to is not responding. */
"SERVER_DOWN" = "השרת שמחשב זה מנסה להתחבר אליו אינו מגיב.";

/* This computer's Internet connection appears to be online. */
"SERVER_UP" = "החיבור של מחשב זה לאינטרנט כנראה אינו מקוון.";

/* Format string for label of a saved password in Keychain: URL (USERNAME) */
"KEYCHAIN_LABEL_FORMAT" = "%@ (%@)";

/* Kind string used for keychain items from forms-based logins */
"KEYCHAIN_WEB_FORM_PASSWORD" = "סיסמת טופס מקוון";

/* HTTP result code string */
"accepted" = "התקבל";

/* HTTP result code string */
"bad gateway" = "שער שאינו תקין";

/* HTTP result code string */
"bad request" = "‏בקשה שאינה תקינה";

/* HTTP result code string */
"client error" = "שגיאת לקוח";

/* HTTP result code string */
"conflict" = "התנגשות";

/* HTTP result code string */
"continue" = "המשך";

/* HTTP result code string */
"created" = "נוצר";

/* HTTP result code string */
"expectation failed" = "הציפייה נכשלה";

/* HTTP result code string */
"forbidden" = "אסור";

/* HTTP result code string */
"found" = "נמצא";

/* HTTP result code string */
"gateway timed out" = "אין מענה מצד השער";

/* HTTP result code string */
"informational" = "לידיעה";

/* HTTP result code string */
"internal server error" = "שגיאת שרת פנימית";

/* HTTP result code string */
"length required" = "נדרש אורך מפורש";

/* HTTP result code string */
"method not allowed" = "שיטה שאינה מותרת";

/* HTTP result code string */
"moved permanently" = "הועתק באופן קבוע";

/* HTTP result code string */
"multiple choices" = "אפשרויות מרובות";

/* HTTP result code string */
"needs proxy" = "זקוק לפרוקסי";

/* HTTP result code string */
"no content" = "אין תוכן";

/* HTTP result code string */
"no error" = "אין שגיאה";

/* HTTP result code string */
"non-authoritative information" = "מידע שאינו סמכותי";

/* HTTP result code string */
"not found" = "לא נמצא";

/* HTTP result code string */
"not modified" = "לא שונה";

/* HTTP result code string */
"partial content" = "תוכן חלקי";

/* HTTP result code string */
"payment required" = "כרוך בתשלום";

/* HTTP result code string */
"precondition failed" = "תנאי-הקדם לא התקיים";

/* HTTP result code string */
"proxy authentication required" = "דרוש אימות פרוקסי";

/* HTTP result code string */
"redirected" = "הופנה מחדש";

/* HTTP result code string */
"no longer exists" = "אינו קיים עוד";

/* HTTP result code string */
"request timed out" = "הבקשה נתקלה בחוסר מענה";

/* HTTP result code string */
"request too large" = "הבקשה גדולה מדי";

/* HTTP result code string */
"requested URL too long" = "כתובת האינטרנט המבוקשת ארוכה מדי";

/* HTTP result code string */
"requested range not satisfiable" = "לא ניתן לעמוד בטווח המבוקש";

/* HTTP result code string */
"reset content" = "התוכן אופס";

/* HTTP result code string */
"see other" = "ראה אחר";

/* HTTP result code string */
"server error" = "שגיאת שרת";

/* HTTP result code string */
"service unavailable" = "השירות אינו זמין";

/* HTTP result code string */
"success" = "הצלחה";

/* HTTP result code string */
"switching protocols" = "מחליף פרוטוקול";

/* HTTP result code string */
"temporarily redirected" = "הופנה זמנית";

/* HTTP result code string */
"unacceptable" = "לא קביל";

/* HTTP result code string */
"unauthorized" = "לא מורשה";

/* HTTP result code string */
"unimplemented" = "לא מיושם";

/* HTTP result code string */
"unsupported media type" = "סוג מדיה שאינו נתמך";

/* HTTP result code string */
"unsupported version" = "גירסה שאינה נתמכת";

/* kCFErrorHTTPProxyConnectionFailure description */
"Err306" = "ארעה בעיה בתקשורת עם שרת הפרוקסי של האינטרנט (HTTP).";

/* kCFErrorHTTPSProxyConnectionFailure description */
"Err310" = "ארעה בעיה בתקשורת עם שרת הפרוקסי המאובטח של האינטרנט (HTTPS).";

/* WebFoundationErrorUnknown description */
"Err-998" = "שגיאה לא ידועה";

/* WebFoundationErrorCancelled description */
"Err-999" = "בוטל";

/* WebFoundationErrorBadURLError description */
"Err-1000" = "כתובת אינטרנט שגויה";

/* WebFoundationErrorTimedOut description */
"Err-1001" = "תם הזמן שהוקצה לבקשה.";

/* WebFoundationErrorUnsupportedURL description */
"Err-1002" = "כתובת אינטרנט שאינה נתמכת";

/* WebFoundationErrorCannotFindHostError description */
"Err-1003" = "לא נמצא שרת עם שם המארח שצוין.";

/* WebFoundationErrorCannotConnectToHostError description */
"Err-1004" = "לא ניתן להתחבר לשרת.";

/* WebFoundationErrorNetworkConnectionLostError description */
"Err-1005" = "חיבור הרשת נותק.";

/* WebFoundationErrorDNSLookupError description */
"Err-1006" = "שגיאת חיפוש ב-DNS";

/* WebFoundationErrorHTTPTooManyRedirectsError description */
"Err-1007" = "יותר מדי הפניות של HTTP";

/* WebFoundationErrorResourceUnavailableError description */
"Err-1008" = "המשאב אינו זמין";

/* WebFoundationErrorNotConnectedToInternetError description */
"Err-1009" = "נראה כי חיבור האינטרנט מנותק.";

/* WebFoundationErrorRedirectToNonExistentLocation description */
"Err-1010" = "בוצעה הפניה ללא יעד";

/* WebFoundationErrorBadServerResponseError description */
"Err-1011" = "התקבלה תגובה שגויה מהשרת.";

/* WebFoundationErrorZeroByteResourceError description */
"Err-1014" = "משאב בגודל אפס בייטים";

/* WebFoundationErrorCannotDecodeRawData description */
"Err-1015" = "לא ניתן לפענח את הנתונים הגולמיים";

/* WebFoundationErrorCannotDecodeContentData description */
"Err-1016" = "לא ניתן לפענח את נתוני התוכן";

/* WebFoundationErrorCannotParseResponse description */
"Err-1017" = "לא ניתן לנתח את התגובה";

/* kCFURLErrorInternationalRoamingOff description */
"Err-1018" = "נדידה בינלאומית מבוטלת כעת.";

/* kCFURLErrorCallIsActive description */
"Err-1019" = "לא ניתן ליצור חיבור נתונים מאחר שיש קריאה פעילה כעת.";

/* kCFURLErrorDataNotAllowed description */
"Err-1020" = "חיבור נתונים אינו מותר כעת.";

/* kCFURLErrorRequestBodyStreamExhausted description.  Should never be seen by an end user. */
"Err-1021" = "הזרמת גוף בקשה נוצלה במלואה";
	
/* WebFoundationErrorFileDoesNotExist description */
"Err-1100" = "הכתובת המבוקשת לא נמצאה בשרת זה.";

/* WebFoundationErrorFileIsDirectory description */
"Err-1101" = "הקובץ הוא ספריה";

/* WebFoundationErrorNoPermissionsToReadFile description */
"Err-1102" = "אין לך הרשאה לגשת אל המשאב המבוקש.";

/* WebFoundationErrorDataLengthExceedsMaximum description */
"Err-1103" = "המשאב חורג מהגודל המרבי";

/* WebFoundationErrorSecureConnectionFailed description */
"Err-1200" = "ארעה שגיאת SSL ולא ניתן להתחבר לשרת באופן מאובטח.";

/* WebFoundationErrorServerCertificateHasBadDate description */
"Err-1201" = "פג תוקף האישור של שרת זה.";

/* WebFoundationErrorServerCertificateHasBadDate description */
"Err-1201.w" = "פג תוקף האישור של שרת זה. יתכן שהינך מתחבר/ת לשרת שמתחזה ל-״%@״, דבר העלול לסכן את הנתונים האישיים שלך.";

/* WebFoundationErrorServerCertificateUntrusted description */
"Err-1202" = "אישורו של שרת זה אינו תקין.";

/* WebFoundationErrorServerCertificateUntrusted description */
"Err-1202.w" = "אישורו של שרת זה אינו תקין. יתכן שהינך מתחבר/ת לשרת שמתחזה ל-״%@״, דבר העלול לסכן את הנתונים האישיים שלך.";

/* WebFoundationErrorServerCertificateHasUnknownRoot description */
"Err-1203" = "אישורו של שרת זה נחתם על-ידי רשות אישורים שאינה מוכרת.";

/* WebFoundationErrorServerCertificateHasUnknownRoot description */
"Err-1203.w" = "אישורו של שרת זה נחתם על-ידי רשות אישורים שאינה מוכרת. יתכן שהינך מתחבר/ת לשרת שמתחזה ל-״%@״, דבר העלול לסכן את הנתונים האישיים שלך.";

/* WebFoundationErrorServerCertificateNotYetValid description */
"Err-1204" = "אישורו של שרת זה אינו תקין עדיין.";

/* WebFoundationErrorServerCertificateNotYetValid description */
"Err-1204.w" = "אישורו של שרת זה אינו תקין עדיין. יתכן שהינך מתחבר/ת לשרת שמתחזה ל-״%@״, דבר העלול לסכן את הנתונים האישיים שלך.";

/* WebFoundationErrorClientCertificateRejected description */
"Err-1205" = "השרת לא קיבל את האישור.";

/* WebFoundationErrorClientCertificateRejected description */
"Err-1205.w" = "השרת ״%@״ לא קיבל את האישור.";

/* WebFoundationErrorClientCertificateRequired description */
"Err-1206" = "השרת מבקש אישור לקוח.";

/* WebFoundationErrorClientCertificateRequired description */
"Err-1206.w" = "השרת ״%@‎״ מבקש אישור לקוח.";

/* WebFoundationErrorCannotLoadFromNetworkError description */
"Err-2000" = "לא ניתן לבצע טעינה מהרשת";

/* WebFoundationErrorCannotCreateFile description */
"Err-3000" = "לא ניתן ליצור את הקובץ";

/* WebFoundationErrorCannotOpenFile description */
"Err-3001" = "לא ניתן לפתוח את הקובץ";

/* WebFoundationErrorCannotCloseFile description */
"Err-3002" = "ארע כשל בעת סגירת הקובץ";

/* WebFoundationErrorCannotWriteToFile description */
"Err-3003" = "לא ניתן לכתוב את הקובץ";

/* WebFoundationErrorCannotRemoveFile description */
"Err-3004" = "לא ניתן להסיר את הקובץ";

/* WebFoundationErrorCannotMoveFile description */
"Err-3005" = "לא ניתן להעביר את הקובץ";

/* WebFoundationErrorDownloadDecodingFailedMidStream description */
"Err-3006" = "פענוח ההורדה נכשל";

/* WebFoundationErrorDownloadDecodingFailedToComplete description */
"Err-3007" = "פענוח ההורדה נכשל";

/* Recovery suggestion */
"Would you like to connect to the server anyway?" = "האם ברצונך להתחבר לשרת בכל זאת?";

/* Recovery suggestion */
"To change your proxy settings, open Network preferences, click Advanced, and then click Proxies. For help with this problem, contact your system administrator." = "על-מנת לשנות את הגדרות הפרוקסי שלך, פתח את העדפות הרשת, לחץ על ״מתקדם״ ולאחר מכן על ״שרתי פרוקסי״. לקבלת עזרה עם בעיה זו, פנה אל מנהל המערכת שלך.";

/* AuthBrokerAgent proxy credential user notification - alert header */
"Proxy Authentication Required" = "נדרש אימות פרוקסי";

/* AuthBrokerAgent proxy credential user notification - alert message, args are 1) proxy type, 2) host name, 3) port number, e.g.:  Authentication for HTTP proxy csd3.apple.com:8080 */
"Authentication for %@ proxy\n%@:%d" = "אימות עבור %@ פרוקסי\n%@:%d";

/* AuthBrokerAgent proxy credential user notification - Cancel button title */
"Cancel" = "ביטול";

/* AuthBrokerAgent proxy credential user notification - Username title */
"Username" = "שם משתמש";

/* AuthBrokerAgent proxy credential user notification - Password title */
"Password" = "סיסמה";
