/* This computer's Internet connection appears to be offline. */
"CONNECTION_DOWN" = "此電腦的 Internet 連線已斷開。";

/* This computer's Internet connection may be offline. */
"CONNECTION_INDETERMINATE" = "此電腦的 Internet 連線可能已斷開。";

/* This computer's Internet connection appears to be online. */
"CONNECTION_UP" = "此電腦的 Internet 連線已連接。";

/* This computer's DNS server is not responding. */
"NAMESERVER_DOWN" = "此電腦的 DNS 伺服器無回應。";

/* The DNS server could not find the server name. */
"NAMELOOKUP_FAILED" = "DNS 伺服器無法找到伺服器名稱。";

/* This computer's router is not responding. */
"ROUTER_DOWN" = "此電腦的路由器無回應。";

/* The server this computer is attempting to connect to is not responding. */
"SERVER_DOWN" = "此電腦嘗試連接的伺服器無回應。";

/* This computer's Internet connection appears to be online. */
"SERVER_UP" = "此電腦的 Internet 連線已連接。";

/* Format string for label of a saved password in Keychain: URL (USERNAME) */
"KEYCHAIN_LABEL_FORMAT" = "%@（%@）";

/* Kind string used for keychain items from forms-based logins */
"KEYCHAIN_WEB_FORM_PASSWORD" = "網頁表單密碼";

/* HTTP result code string */
"accepted" = "已接受";

/* HTTP result code string */
"bad gateway" = "閘道器錯誤";

/* HTTP result code string */
"bad request" = "要求錯誤";

/* HTTP result code string */
"client error" = "用戶端錯誤";

/* HTTP result code string */
"conflict" = "衝突";

/* HTTP result code string */
"continue" = "繼續";

/* HTTP result code string */
"created" = "已建立";

/* HTTP result code string */
"expectation failed" = "預期情況失敗";

/* HTTP result code string */
"forbidden" = "已禁止";

/* HTTP result code string */
"found" = "已找到";

/* HTTP result code string */
"gateway timed out" = "閘道已逾時";

/* HTTP result code string */
"informational" = "資訊性";

/* HTTP result code string */
"internal server error" = "內部伺服器錯誤";

/* HTTP result code string */
"length required" = "要求的長度";

/* HTTP result code string */
"method not allowed" = "不允許的方式";

/* HTTP result code string */
"moved permanently" = "永久移除";

/* HTTP result code string */
"multiple choices" = "多項選擇";

/* HTTP result code string */
"needs proxy" = "需要代理伺服器";

/* HTTP result code string */
"no content" = "無內容";

/* HTTP result code string */
"no error" = "無錯誤";

/* HTTP result code string */
"non-authoritative information" = "非授權資訊";

/* HTTP result code string */
"not found" = "找不到";

/* HTTP result code string */
"not modified" = "未修改";

/* HTTP result code string */
"partial content" = "部分內容";

/* HTTP result code string */
"payment required" = "需要付費";

/* HTTP result code string */
"precondition failed" = "預設條件失敗";

/* HTTP result code string */
"proxy authentication required" = "所需代理伺服器授權";

/* HTTP result code string */
"redirected" = "已轉址";

/* HTTP result code string */
"no longer exists" = "已不存在";

/* HTTP result code string */
"request timed out" = "要求逾時";

/* HTTP result code string */
"request too large" = "要求太大";

/* HTTP result code string */
"requested URL too long" = "要求的 URL 太長";

/* HTTP result code string */
"requested range not satisfiable" = "不符合要求範圍";

/* HTTP result code string */
"reset content" = "重置內容";

/* HTTP result code string */
"see other" = "請見其他";

/* HTTP result code string */
"server error" = "伺服器錯誤";

/* HTTP result code string */
"service unavailable" = "無法取用的服務";

/* HTTP result code string */
"success" = "成功";

/* HTTP result code string */
"switching protocols" = "切換通訊協定";

/* HTTP result code string */
"temporarily redirected" = "暫時性轉址";

/* HTTP result code string */
"unacceptable" = "無法接受";

/* HTTP result code string */
"unauthorized" = "未經授權的";

/* HTTP result code string */
"unimplemented" = "未實行的";

/* HTTP result code string */
"unsupported media type" = "不支援的媒體類型";

/* HTTP result code string */
"unsupported version" = "不支援的版本";

/* kCFErrorHTTPProxyConnectionFailure description */
"Err306" = "與網頁代理伺服器（HTTP）通訊時發生問題。";

/* kCFErrorHTTPSProxyConnectionFailure description */
"Err310" = "與安全網頁代理伺服器（HTTPS）通訊時發生問題。";

/* WebFoundationErrorUnknown description */
"Err-998" = "未知的錯誤";

/* WebFoundationErrorCancelled description */
"Err-999" = "已取消";

/* WebFoundationErrorBadURLError description */
"Err-1000" = "不正確的 URL";

/* WebFoundationErrorTimedOut description */
"Err-1001" = "要求逾時。";

/* WebFoundationErrorUnsupportedURL description */
"Err-1002" = "不支援的 URL";

/* WebFoundationErrorCannotFindHostError description */
"Err-1003" = "無法找到具有此指定主機名稱的伺服器。";

/* WebFoundationErrorCannotConnectToHostError description */
"Err-1004" = "無法連接伺服器。";

/* WebFoundationErrorNetworkConnectionLostError description */
"Err-1005" = "網路連線中斷。";

/* WebFoundationErrorDNSLookupError description */
"Err-1006" = "DNS 查詢錯誤";

/* WebFoundationErrorHTTPTooManyRedirectsError description */
"Err-1007" = "太多 HTTP 轉址";

/* WebFoundationErrorResourceUnavailableError description */
"Err-1008" = "無法取得資源";

/* WebFoundationErrorNotConnectedToInternetError description */
"Err-1009" = "Internet 連線似乎為離線狀態。";

/* WebFoundationErrorRedirectToNonExistentLocation description */
"Err-1010" = "並未轉址到任何地方";

/* WebFoundationErrorBadServerResponseError description */
"Err-1011" = "從伺服器傳來錯誤的回應。";

/* WebFoundationErrorZeroByteResourceError description */
"Err-1014" = "0 byte 資源";

/* WebFoundationErrorCannotDecodeRawData description */
"Err-1015" = "無法解碼原始資料";

/* WebFoundationErrorCannotDecodeContentData description */
"Err-1016" = "無法解碼內容資料";

/* WebFoundationErrorCannotParseResponse description */
"Err-1017" = "無法剖析回應";

/* kCFURLErrorInternationalRoamingOff description */
"Err-1018" = "國際漫遊目前已停用。";

/* kCFURLErrorCallIsActive description */
"Err-1019" = "無法建立連線，因為某個呼叫正在作用中。";

/* kCFURLErrorDataNotAllowed description */
"Err-1020" = "目前不允許資料連線。";

/* kCFURLErrorRequestBodyStreamExhausted description.  Should never be seen by an end user. */
"Err-1021" = "要求的主體串流已耗盡";
	
/* WebFoundationErrorFileDoesNotExist description */
"Err-1100" = "在此伺服器上找不到要求的 URL。";

/* WebFoundationErrorFileIsDirectory description */
"Err-1101" = "檔案為目錄";

/* WebFoundationErrorNoPermissionsToReadFile description */
"Err-1102" = "您沒有權限存取所要求的資源。";

/* WebFoundationErrorDataLengthExceedsMaximum description */
"Err-1103" = "資源大小超過最大值";

/* WebFoundationErrorSecureConnectionFailed description */
"Err-1200" = "發生 SSL 錯誤，無法建立與伺服器的安全連線。";

/* WebFoundationErrorServerCertificateHasBadDate description */
"Err-1201" = "伺服器的憑證已過期。";

/* WebFoundationErrorServerCertificateHasBadDate description */
"Err-1201.w" = "此伺服器的憑證已過期。您所連接的伺服器可能是冒用「%@」的網站，並且可能會對您的私密資訊造成風險。";

/* WebFoundationErrorServerCertificateUntrusted description */
"Err-1202" = "此伺服器的憑證無效。";

/* WebFoundationErrorServerCertificateUntrusted description */
"Err-1202.w" = "此伺服器的憑證無效。您所連接的伺服器可能是冒用「%@」的網站，並且可能會對您的私密資訊造成風險。";

/* WebFoundationErrorServerCertificateHasUnknownRoot description */
"Err-1203" = "此伺服器的憑證是由未知的憑證授權所簽署。";

/* WebFoundationErrorServerCertificateHasUnknownRoot description */
"Err-1203.w" = "此伺服器的憑證是由未知的憑證授權所簽署。您所連接的伺服器可能是冒用「%@」的網站，並且可能會對您的私密資訊造成風險。";

/* WebFoundationErrorServerCertificateNotYetValid description */
"Err-1204" = "此伺服器的憑證尚未生效。";

/* WebFoundationErrorServerCertificateNotYetValid description */
"Err-1204.w" = "此伺服器的憑證尚未生效。您所連接的伺服器可能是冒用「%@」的網站，並且可能會對您的私密資訊造成風險。";

/* WebFoundationErrorClientCertificateRejected description */
"Err-1205" = "伺服器尚未接受憑證。";

/* WebFoundationErrorClientCertificateRejected description */
"Err-1205.w" = "伺服器「%@」尚未接受憑證。";

/* WebFoundationErrorClientCertificateRequired description */
"Err-1206" = "伺服器要求用戶端憑證。";

/* WebFoundationErrorClientCertificateRequired description */
"Err-1206.w" = "伺服器「%@」要求用戶端憑證。";

/* WebFoundationErrorCannotLoadFromNetworkError description */
"Err-2000" = "無法由網路載入";

/* WebFoundationErrorCannotCreateFile description */
"Err-3000" = "無法製作檔案";

/* WebFoundationErrorCannotOpenFile description */
"Err-3001" = "無法開啟檔案";

/* WebFoundationErrorCannotCloseFile description */
"Err-3002" = "關閉檔案時發生錯誤";

/* WebFoundationErrorCannotWriteToFile description */
"Err-3003" = "無法寫入檔案";

/* WebFoundationErrorCannotRemoveFile description */
"Err-3004" = "無法移除檔案";

/* WebFoundationErrorCannotMoveFile description */
"Err-3005" = "無法移動檔案";

/* WebFoundationErrorDownloadDecodingFailedMidStream description */
"Err-3006" = "下載解碼失敗";

/* WebFoundationErrorDownloadDecodingFailedToComplete description */
"Err-3007" = "下載解碼失敗";

/* Recovery suggestion */
"Would you like to connect to the server anyway?" = "您仍然要連接伺服器嗎？";

/* Recovery suggestion */
"To change your proxy settings, open Network preferences, click Advanced, and then click Proxies. For help with this problem, contact your system administrator." = "若要更改代理伺服器設定，請開啟 [網路] 喜好設定，按 [進階] 一下，然後按 [代理伺服器] 一下。如需取得此問題的協助，請聯絡系統管理員。";

/* AuthBrokerAgent proxy credential user notification - alert header */
"Proxy Authentication Required" = "所需代理伺服器授權";

/* AuthBrokerAgent proxy credential user notification - alert message, args are 1) proxy type, 2) host name, 3) port number, e.g.:  Authentication for HTTP proxy csd3.apple.com:8080 */
"Authentication for %@ proxy\n%@:%d" = "%@ 代理程式的授權\n%@：%d";

/* AuthBrokerAgent proxy credential user notification - Cancel button title */
"Cancel" = "取消";

/* AuthBrokerAgent proxy credential user notification - Username title */
"Username" = "使用者名稱";

/* AuthBrokerAgent proxy credential user notification - Password title */
"Password" = "密碼";
