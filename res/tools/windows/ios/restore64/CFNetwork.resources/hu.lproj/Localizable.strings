/* This computer's Internet connection appears to be offline. */
"CONNECTION_DOWN" = "A számítógép internetkapcsolata látszólag kapcsolat nélküli módban van.";

/* This computer's Internet connection may be offline. */
"CONNECTION_INDETERMINATE" = "Lehetséges, hogy a számítógép internetkapcsolata kapcsolat nélküli módban van.";

/* This computer's Internet connection appears to be online. */
"CONNECTION_UP" = "Úgy tűnik, hogy a számítógép csatlakozik az internethez.";

/* This computer's DNS server is not responding. */
"NAMESERVER_DOWN" = "A számítógép DNS kiszolgálója nem válaszol.";

/* The DNS server could not find the server name. */
"NAMELOOKUP_FAILED" = "A DNS kiszolgáló nem találja a kiszolgálónevet.";

/* This computer's router is not responding. */
"ROUTER_DOWN" = "A számítógép routere nem válaszol.";

/* The server this computer is attempting to connect to is not responding. */
"SERVER_DOWN" = "A kiszolgáló, amihez a számítógép csatlakozni próbál, nem válaszol.";

/* This computer's Internet connection appears to be online. */
"SERVER_UP" = "Úgy tűnik, hogy a számítógép csatlakozik az internethez.";

/* Format string for label of a saved password in Keychain: URL (USERNAME) */
"KEYCHAIN_LABEL_FORMAT" = "%@ (%@)";

/* Kind string used for keychain items from forms-based logins */
"KEYCHAIN_WEB_FORM_PASSWORD" = "Webes űrlap jelszava";

/* HTTP result code string */
"accepted" = "elfogadva";

/* HTTP result code string */
"bad gateway" = "hibás átjáró";

/* HTTP result code string */
"bad request" = "hibás kérés";

/* HTTP result code string */
"client error" = "ügyfélhiba";

/* HTTP result code string */
"conflict" = "ütközés";

/* HTTP result code string */
"continue" = "folytatás";

/* HTTP result code string */
"created" = "létrehozva";

/* HTTP result code string */
"expectation failed" = "várakozás sikertelen";

/* HTTP result code string */
"forbidden" = "tiltott";

/* HTTP result code string */
"found" = "találat";

/* HTTP result code string */
"gateway timed out" = "az átjáró időkorlátja lejárt";

/* HTTP result code string */
"informational" = "információs";

/* HTTP result code string */
"internal server error" = "belső kiszolgálóhiba";

/* HTTP result code string */
"length required" = "hossz szükséges";

/* HTTP result code string */
"method not allowed" = "módszer nem engedélyezett";

/* HTTP result code string */
"moved permanently" = "tartósan áthelyezve";

/* HTTP result code string */
"multiple choices" = "többszörös választás";

/* HTTP result code string */
"needs proxy" = "proxy szükséges";

/* HTTP result code string */
"no content" = "nincs tartalom";

/* HTTP result code string */
"no error" = "nincs hiba";

/* HTTP result code string */
"non-authoritative information" = "nem mérvadó információ";

/* HTTP result code string */
"not found" = "nem található";

/* HTTP result code string */
"not modified" = "nincs módosítva";

/* HTTP result code string */
"partial content" = "részleges tartalom";

/* HTTP result code string */
"payment required" = "fizetés szükséges";

/* HTTP result code string */
"precondition failed" = "előfeltétel nem teljesül";

/* HTTP result code string */
"proxy authentication required" = "proxyhitelesítés szükséges";

/* HTTP result code string */
"redirected" = "átirányítva";

/* HTTP result code string */
"no longer exists" = "többé nem létezik";

/* HTTP result code string */
"request timed out" = "kérés időkorlátja lejárt";

/* HTTP result code string */
"request too large" = "a kérés túl nagy";

/* HTTP result code string */
"requested URL too long" = "a kért URL-cím túl hosszú";

/* HTTP result code string */
"requested range not satisfiable" = "a kért tartomány nem teljesíthető";

/* HTTP result code string */
"reset content" = "tartalom visszaállítása";

/* HTTP result code string */
"see other" = "lásd a többit";

/* HTTP result code string */
"server error" = "kiszolgálóhiba";

/* HTTP result code string */
"service unavailable" = "a szolgáltatás nem érhető el";

/* HTTP result code string */
"success" = "sikeres művelet";

/* HTTP result code string */
"switching protocols" = "protokollváltás";

/* HTTP result code string */
"temporarily redirected" = "ideiglenesen átirányítva";

/* HTTP result code string */
"unacceptable" = "elfogadhatatlan";

/* HTTP result code string */
"unauthorized" = "nem jogosult";

/* HTTP result code string */
"unimplemented" = "nem megvalósított";

/* HTTP result code string */
"unsupported media type" = "nem támogatott adathordozó-típus";

/* HTTP result code string */
"unsupported version" = "nem támogatott verzió";

/* kCFErrorHTTPProxyConnectionFailure description */
"Err306" = "A webes proxykiszolgálóval (HTTP) való kommunikáció során hiba lépett fel.";

/* kCFErrorHTTPSProxyConnectionFailure description */
"Err310" = "A biztonságos webes proxykiszolgálóval (HTTP) való kommunikáció során hiba lépett fel.";

/* WebFoundationErrorUnknown description */
"Err-998" = "ismeretlen hiba";

/* WebFoundationErrorCancelled description */
"Err-999" = "visszavonva";

/* WebFoundationErrorBadURLError description */
"Err-1000" = "hibás URL-cím";

/* WebFoundationErrorTimedOut description */
"Err-1001" = "A kérelem időkorlátja lejárt.";

/* WebFoundationErrorUnsupportedURL description */
"Err-1002" = "nem támogatott URL-cím";

/* WebFoundationErrorCannotFindHostError description */
"Err-1003" = "A megadott állomásnévvel nem található kiszolgáló.";

/* WebFoundationErrorCannotConnectToHostError description */
"Err-1004" = "Nem sikerült a kiszolgálóhoz kapcsolódni.";

/* WebFoundationErrorNetworkConnectionLostError description */
"Err-1005" = "A hálózati kapcsolat megszakadt.";

/* WebFoundationErrorDNSLookupError description */
"Err-1006" = "DNS keresési hiba";

/* WebFoundationErrorHTTPTooManyRedirectsError description */
"Err-1007" = "túl sok HTTP átirányítás";

/* WebFoundationErrorResourceUnavailableError description */
"Err-1008" = "az erőforrás nem érhető el";

/* WebFoundationErrorNotConnectedToInternetError description */
"Err-1009" = "Úgy tűnik, hogy az internetkapcsolat kapcsolat nélküli módban van.";

/* WebFoundationErrorRedirectToNonExistentLocation description */
"Err-1010" = "nem létező átirányítási hely";

/* WebFoundationErrorBadServerResponseError description */
"Err-1011" = "Hibás válasz érkezett a kiszolgálótól.";

/* WebFoundationErrorZeroByteResourceError description */
"Err-1014" = "zéróbájtos erőforrás";

/* WebFoundationErrorCannotDecodeRawData description */
"Err-1015" = "a nyers adat nem dekódolható";

/* WebFoundationErrorCannotDecodeContentData description */
"Err-1016" = "a tartalom adatai nem dekódolhatók";

/* WebFoundationErrorCannotParseResponse description */
"Err-1017" = "a válasz nem elemezhető";

/* kCFURLErrorInternationalRoamingOff description */
"Err-1018" = "A nemzetközi barangolás jelenleg ki van kapcsolva.";

/* kCFURLErrorCallIsActive description */
"Err-1019" = "Nem hozható létre adatkapcsolat, mert egy hívás jelenleg folyamatban van.";

/* kCFURLErrorDataNotAllowed description */
"Err-1020" = "Az adatkapcsolat jelenleg nem engedélyezett.";

/* kCFURLErrorRequestBodyStreamExhausted description.  Should never be seen by an end user. */
"Err-1021" = "a kérelem törzsének adatfolyama kiürült";
	
/* WebFoundationErrorFileDoesNotExist description */
"Err-1100" = "A kért URL-cím nem található ezen a kiszolgálón.";

/* WebFoundationErrorFileIsDirectory description */
"Err-1101" = "a fájl egy könyvtár";

/* WebFoundationErrorNoPermissionsToReadFile description */
"Err-1102" = "Nincs jogosultsága a kért erőforrás elérésére.";

/* WebFoundationErrorDataLengthExceedsMaximum description */
"Err-1103" = "az erőforrás meghaladja a maximális méretet";

/* WebFoundationErrorSecureConnectionFailed description */
"Err-1200" = "SSL-hiba lépett fel, és a szerverrel nem építhető ki biztonságos kapcsolat.";

/* WebFoundationErrorServerCertificateHasBadDate description */
"Err-1201" = "A kiszolgáló tanúsítványa lejárt.";

/* WebFoundationErrorServerCertificateHasBadDate description */
"Err-1201.w" = "A kiszolgáló tanúsítványa lejárt. Előfordulhat, hogy egy olyan kiszolgálóhoz próbál csatlakozni, amely „%@” néven próbálja feltüntetni magát, ezáltal veszélyt jelenthet a bizalmas adataira.";

/* WebFoundationErrorServerCertificateUntrusted description */
"Err-1202" = "A kiszolgáló tanúsítványa érvénytelen.";

/* WebFoundationErrorServerCertificateUntrusted description */
"Err-1202.w" = "A kiszolgáló tanúsítványa érvénytelen. Előfordulhat, hogy egy olyan kiszolgálóhoz próbál csatlakozni, amely „%@” néven próbálja feltüntetni magát, ezáltal veszélyt jelenthet a bizalmas adataira.";

/* WebFoundationErrorServerCertificateHasUnknownRoot description */
"Err-1203" = "A kiszolgáló tanúsítványa ismeretlen hitelesítésszolgáltató által lett aláírva.";

/* WebFoundationErrorServerCertificateHasUnknownRoot description */
"Err-1203.w" = "A kiszolgáló tanúsítványa ismeretlen hitelesítésszolgáltató által lett aláírva. Előfordulhat, hogy egy olyan kiszolgálóhoz próbál csatlakozni, amely „%@” néven próbálja feltüntetni magát, ezáltal veszélyt jelenthet a bizalmas adataira.";

/* WebFoundationErrorServerCertificateNotYetValid description */
"Err-1204" = "A kiszolgáló tanúsítványa még nem érvényes.";

/* WebFoundationErrorServerCertificateNotYetValid description */
"Err-1204.w" = "A kiszolgáló tanúsítványa még nem érvényes. Előfordulhat, hogy egy olyan kiszolgálóhoz próbál csatlakozni, amely „%@” néven próbálja feltüntetni magát, ezáltal veszélyt jelenthet a bizalmas adataira.";

/* WebFoundationErrorClientCertificateRejected description */
"Err-1205" = "A kiszolgáló nem fogadta el a tanúsítványt.";

/* WebFoundationErrorClientCertificateRejected description */
"Err-1205.w" = "A(z) „%@” kiszolgáló nem fogadta el a tanúsítványt.";

/* WebFoundationErrorClientCertificateRequired description */
"Err-1206" = "A kiszolgáló ügyféltanúsítványt igényel.";

/* WebFoundationErrorClientCertificateRequired description */
"Err-1206.w" = "A(z) „%@” kiszolgáló ügyféltanúsítványt igényel.";

/* WebFoundationErrorCannotLoadFromNetworkError description */
"Err-2000" = "a betöltés a hálózatról nem lehetséges";

/* WebFoundationErrorCannotCreateFile description */
"Err-3000" = "A fájl létrehozása nem lehetséges";

/* WebFoundationErrorCannotOpenFile description */
"Err-3001" = "A fájl megnyitása nem lehetséges";

/* WebFoundationErrorCannotCloseFile description */
"Err-3002" = "Hiba történt a fájl bezárása közben";

/* WebFoundationErrorCannotWriteToFile description */
"Err-3003" = "A fájl nem írható";

/* WebFoundationErrorCannotRemoveFile description */
"Err-3004" = "A fájl nem távolítható el";

/* WebFoundationErrorCannotMoveFile description */
"Err-3005" = "A fájl nem helyezhető át";

/* WebFoundationErrorDownloadDecodingFailedMidStream description */
"Err-3006" = "A letöltés dekódolása sikertelen";

/* WebFoundationErrorDownloadDecodingFailedToComplete description */
"Err-3007" = "A letöltés dekódolása sikertelen";

/* Recovery suggestion */
"Would you like to connect to the server anyway?" = "Mindenképpen csatlakozni szeretne a kiszolgálóhoz?";

/* Recovery suggestion */
"To change your proxy settings, open Network preferences, click Advanced, and then click Proxies. For help with this problem, contact your system administrator." = "A proxybeállítások módosításához nyissa meg a Hálózati beállításokat, kettintson a Speciális lehetőségre, majd a Proxyk lehetőségre. Ha segítségre van szüksége a probléma megoldásához, lépjen kapcsolatba a rendszergazdával.";

/* AuthBrokerAgent proxy credential user notification - alert header */
"Proxy Authentication Required" = "Proxy hitelesítés szükséges";

/* AuthBrokerAgent proxy credential user notification - alert message, args are 1) proxy type, 2) host name, 3) port number, e.g.:  Authentication for HTTP proxy csd3.apple.com:8080 */
"Authentication for %@ proxy\n%@:%d" = "Hitelesítés a(z) %@ proxy számára\n%@:%d";

/* AuthBrokerAgent proxy credential user notification - Cancel button title */
"Cancel" = "Mégsem";

/* AuthBrokerAgent proxy credential user notification - Username title */
"Username" = "Felhasználói név";

/* AuthBrokerAgent proxy credential user notification - Password title */
"Password" = "Jelszó";
