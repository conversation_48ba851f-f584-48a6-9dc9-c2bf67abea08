<?xml version='1.0'?>
<!DOCTYPE signatures SYSTEM "file://localhost/System/Library/DTDs/BridgeSupport.dtd">
<signatures version='1.0'>
<struct name='CFAllocatorContext' type64='{_CFAllocatorContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?&quot;allocate&quot;^?&quot;reallocate&quot;^?&quot;deallocate&quot;^?&quot;preferredSize&quot;^?}'/>
<struct name='CFArrayCallBacks' type64='{_CFArrayCallBacks=&quot;version&quot;q&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?&quot;equal&quot;^?}'/>
<struct name='CFBagCallBacks' type64='{_CFBagCallBacks=&quot;version&quot;q&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?&quot;equal&quot;^?&quot;hash&quot;^?}'/>
<struct name='CFBinaryHeapCallBacks' type64='{_CFBinaryHeapCallBacks=&quot;version&quot;q&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?&quot;compare&quot;^?}'/>
<struct name='CFBinaryHeapCompareContext' type64='{_CFBinaryHeapCompareContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?}'/>
<struct name='CFDictionaryKeyCallBacks' type64='{_CFDictionaryKeyCallBacks=&quot;version&quot;q&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?&quot;equal&quot;^?&quot;hash&quot;^?}'/>
<struct name='CFDictionaryValueCallBacks' type64='{_CFDictionaryValueCallBacks=&quot;version&quot;q&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?&quot;equal&quot;^?}'/>
<struct name='CFFileDescriptorContext' type64='{_CFFileDescriptorContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?}'/>
<struct name='CFGregorianDate' type64='{_CFGregorianDate=&quot;year&quot;i&quot;month&quot;c&quot;day&quot;c&quot;hour&quot;c&quot;minute&quot;c&quot;second&quot;d}'/>
<struct name='CFGregorianUnits' type64='{_CFGregorianUnits=&quot;years&quot;i&quot;months&quot;i&quot;days&quot;i&quot;hours&quot;i&quot;minutes&quot;i&quot;seconds&quot;d}'/>
<struct name='CFMachPortContext' type64='{_CFMachPortContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?}'/>
<struct name='CFMessagePortContext' type64='{_CFMessagePortContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?}'/>
<struct name='CFRange' type64='{_CFRange=&quot;location&quot;q&quot;length&quot;q}'/>
<struct name='CFRunLoopObserverContext' type64='{_CFRunLoopObserverContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?}'/>
<struct name='CFRunLoopSourceContext' type64='{_CFRunLoopSourceContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?&quot;equal&quot;^?&quot;hash&quot;^?&quot;schedule&quot;^?&quot;cancel&quot;^?&quot;perform&quot;^?}'/>
<struct name='CFRunLoopSourceContext1' type64='{_CFRunLoopSourceContext1=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?&quot;equal&quot;^?&quot;hash&quot;^?&quot;getPort&quot;^?&quot;perform&quot;^?}'/>
<struct name='CFRunLoopTimerContext' type64='{_CFRunLoopTimerContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?}'/>
<struct name='CFSetCallBacks' type64='{_CFSetCallBacks=&quot;version&quot;q&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?&quot;equal&quot;^?&quot;hash&quot;^?}'/>
<struct name='CFSocketContext' type64='{_CFSocketContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?}'/>
<struct name='CFSocketSignature' type64='{_CFSocketSignature=&quot;protocolFamily&quot;i&quot;socketType&quot;i&quot;protocol&quot;i&quot;address&quot;^{__CFData}}'/>
<struct name='CFStreamClientContext' type64='{_CFStreamClientContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?}'/>
<struct name='CFStreamError' type64='{_CFStreamError=&quot;domain&quot;q&quot;error&quot;i}'/>
<struct name='CFStringInlineBuffer' type64='{_CFStringInlineBuffer=&quot;buffer&quot;[64S]&quot;theString&quot;^{__CFString}&quot;directUniCharBuffer&quot;^S&quot;directCStringBuffer&quot;*&quot;rangeToBuffer&quot;{_CFRange=&quot;location&quot;q&quot;length&quot;q}&quot;bufferedRangeStart&quot;q&quot;bufferedRangeEnd&quot;q}'/>
<struct name='CFSwappedFloat32' type64='{_CFSwappedFloat32=&quot;v&quot;I}'/>
<struct name='CFSwappedFloat64' type64='{_CFSwappedFloat64=&quot;v&quot;Q}'/>
<struct name='CFTreeContext' type64='{_CFTreeContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?}'/>
<struct name='CFUUIDBytes' type64='{_CFUUIDBytes=&quot;byte0&quot;C&quot;byte1&quot;C&quot;byte2&quot;C&quot;byte3&quot;C&quot;byte4&quot;C&quot;byte5&quot;C&quot;byte6&quot;C&quot;byte7&quot;C&quot;byte8&quot;C&quot;byte9&quot;C&quot;byte10&quot;C&quot;byte11&quot;C&quot;byte12&quot;C&quot;byte13&quot;C&quot;byte14&quot;C&quot;byte15&quot;C}'/>
<struct name='CFXMLAttributeDeclarationInfo' type64='{_CFXMLAttributeDeclarationInfo=&quot;attributeName&quot;^{__CFString}&quot;typeString&quot;^{__CFString}&quot;defaultString&quot;^{__CFString}}'/>
<struct name='CFXMLAttributeListDeclarationInfo' type64='{_CFXMLAttributeListDeclarationInfo=&quot;numberOfAttributes&quot;q&quot;attributes&quot;^{?}}'/>
<struct name='CFXMLDocumentInfo' type64='{_CFXMLDocumentInfo=&quot;sourceURL&quot;^{__CFURL}&quot;encoding&quot;I}'/>
<struct name='CFXMLDocumentTypeInfo' type64='{_CFXMLDocumentTypeInfo=&quot;externalID&quot;{_CFXMLExternalID=&quot;systemID&quot;^{__CFURL}&quot;publicID&quot;^{__CFString}}}'/>
<struct name='CFXMLElementInfo' type64='{_CFXMLElementInfo=&quot;attributes&quot;^{__CFDictionary}&quot;attributeOrder&quot;^{__CFArray}&quot;isEmpty&quot;B&quot;_reserved&quot;[3c]}'/>
<struct name='CFXMLElementTypeDeclarationInfo' type64='{_CFXMLElementTypeDeclarationInfo=&quot;contentDescription&quot;^{__CFString}}'/>
<struct name='CFXMLEntityInfo' type64='{_CFXMLEntityInfo=&quot;entityType&quot;q&quot;replacementText&quot;^{__CFString}&quot;entityID&quot;{_CFXMLExternalID=&quot;systemID&quot;^{__CFURL}&quot;publicID&quot;^{__CFString}}&quot;notationName&quot;^{__CFString}}'/>
<struct name='CFXMLEntityReferenceInfo' type64='{_CFXMLEntityReferenceInfo=&quot;entityType&quot;q}'/>
<struct name='CFXMLExternalID' type64='{_CFXMLExternalID=&quot;systemID&quot;^{__CFURL}&quot;publicID&quot;^{__CFString}}'/>
<struct name='CFXMLNotationInfo' type64='{_CFXMLNotationInfo=&quot;externalID&quot;{_CFXMLExternalID=&quot;systemID&quot;^{__CFURL}&quot;publicID&quot;^{__CFString}}}'/>
<struct name='CFXMLParserCallBacks' type64='{_CFXMLParserCallBacks=&quot;version&quot;q&quot;createXMLStructure&quot;^?&quot;addChild&quot;^?&quot;endXMLStructure&quot;^?&quot;resolveExternalEntity&quot;^?&quot;handleError&quot;^?}'/>
<struct name='CFXMLParserContext' type64='{_CFXMLParserContext=&quot;version&quot;q&quot;info&quot;^v&quot;retain&quot;^?&quot;release&quot;^?&quot;copyDescription&quot;^?}'/>
<struct name='CFXMLProcessingInstructionInfo' type64='{_CFXMLProcessingInstructionInfo=&quot;dataString&quot;^{__CFString}}'/>
<struct name='IUnknownVTbl' type64='{IUnknownVTbl=&quot;_reserved&quot;^v&quot;QueryInterface&quot;^?&quot;AddRef&quot;^?&quot;Release&quot;^?}'/>
<struct name='REFIID' type64='{_REFIID=&quot;byte0&quot;C&quot;byte1&quot;C&quot;byte2&quot;C&quot;byte3&quot;C&quot;byte4&quot;C&quot;byte5&quot;C&quot;byte6&quot;C&quot;byte7&quot;C&quot;byte8&quot;C&quot;byte9&quot;C&quot;byte10&quot;C&quot;byte11&quot;C&quot;byte12&quot;C&quot;byte13&quot;C&quot;byte14&quot;C&quot;byte15&quot;C}'/>
<cftype gettypeid_func='CFAllocatorGetTypeID' name='CFAllocatorRef' tollfree='__NSCFType' type64='^{__CFAllocator=}'/>
<cftype gettypeid_func='CFArrayGetTypeID' name='CFArrayRef' tollfree='__NSCFArray' type64='^{__CFArray=}'/>
<cftype gettypeid_func='CFAttributedStringGetTypeID' name='CFAttributedStringRef' tollfree='__NSCFAttributedString' type64='^{__CFAttributedString=}'/>
<cftype gettypeid_func='CFBagGetTypeID' name='CFBagRef' tollfree='__NSCFType' type64='^{__CFBag=}'/>
<cftype gettypeid_func='CFBinaryHeapGetTypeID' name='CFBinaryHeapRef' tollfree='__NSCFType' type64='^{__CFBinaryHeap=}'/>
<cftype gettypeid_func='CFBitVectorGetTypeID' name='CFBitVectorRef' tollfree='__NSCFType' type64='^{__CFBitVector=}'/>
<cftype gettypeid_func='CFBooleanGetTypeID' name='CFBooleanRef' tollfree='__NSCFBoolean' type64='^{__CFBoolean=}'/>
<cftype gettypeid_func='CFBundleGetTypeID' name='CFBundleRef' tollfree='__NSCFType' type64='^{__CFBundle=}'/>
<cftype gettypeid_func='CFCalendarGetTypeID' name='CFCalendarRef' tollfree='__NSCFCalendar' type64='^{__CFCalendar=}'/>
<cftype gettypeid_func='CFCharacterSetGetTypeID' name='CFCharacterSetRef' tollfree='__NSCFCharacterSet' type64='^{__CFCharacterSet=}'/>
<cftype gettypeid_func='CFDataGetTypeID' name='CFDataRef' tollfree='__NSCFData' type64='^{__CFData=}'/>
<cftype gettypeid_func='CFDateFormatterGetTypeID' name='CFDateFormatterRef' tollfree='__NSCFType' type64='^{__CFDateFormatter=}'/>
<cftype gettypeid_func='CFDateGetTypeID' name='CFDateRef' tollfree='__NSCFType' type64='^{__CFDate=}'/>
<cftype gettypeid_func='CFDictionaryGetTypeID' name='CFDictionaryRef' tollfree='__NSCFDictionary' type64='^{__CFDictionary=}'/>
<cftype gettypeid_func='CFErrorGetTypeID' name='CFErrorRef' tollfree='__NSCFError' type64='^{__CFError=}'/>
<cftype gettypeid_func='CFFileDescriptorGetTypeID' name='CFFileDescriptorRef' tollfree='__NSCFType' type64='^{__CFFileDescriptor=}'/>
<cftype gettypeid_func='CFFileSecurityGetTypeID' name='CFFileSecurityRef' tollfree='__NSCFType' type64='^{__CFFileSecurity=}'/>
<cftype gettypeid_func='CFLocaleGetTypeID' name='CFLocaleRef' tollfree='__NSCFLocale' type64='^{__CFLocale=}'/>
<cftype gettypeid_func='CFMachPortGetTypeID' name='CFMachPortRef' tollfree='NSMachPort' type64='^{__CFMachPort=}'/>
<cftype gettypeid_func='CFMessagePortGetTypeID' name='CFMessagePortRef' tollfree='__NSCFType' type64='^{__CFMessagePort=}'/>
<cftype gettypeid_func='CFArrayGetTypeID' name='CFMutableArrayRef' tollfree='__NSCFArray' type64='^{__CFArray=}'/>
<cftype gettypeid_func='CFAttributedStringGetTypeID' name='CFMutableAttributedStringRef' tollfree='__NSCFAttributedString' type64='^{__CFAttributedString=}'/>
<cftype gettypeid_func='CFBagGetTypeID' name='CFMutableBagRef' tollfree='__NSCFType' type64='^{__CFBag=}'/>
<cftype gettypeid_func='CFBitVectorGetTypeID' name='CFMutableBitVectorRef' tollfree='__NSCFType' type64='^{__CFBitVector=}'/>
<cftype gettypeid_func='CFCharacterSetGetTypeID' name='CFMutableCharacterSetRef' tollfree='__NSCFCharacterSet' type64='^{__CFCharacterSet=}'/>
<cftype gettypeid_func='CFDataGetTypeID' name='CFMutableDataRef' tollfree='__NSCFData' type64='^{__CFData=}'/>
<cftype gettypeid_func='CFDictionaryGetTypeID' name='CFMutableDictionaryRef' tollfree='__NSCFDictionary' type64='^{__CFDictionary=}'/>
<cftype gettypeid_func='CFSetGetTypeID' name='CFMutableSetRef' tollfree='__NSCFSet' type64='^{__CFSet=}'/>
<cftype gettypeid_func='CFStringGetTypeID' name='CFMutableStringRef' tollfree='__NSCFString' type64='^{__CFString=}'/>
<cftype gettypeid_func='CFNotificationCenterGetTypeID' name='CFNotificationCenterRef' tollfree='__NSCFType' type64='^{__CFNotificationCenter=}'/>
<cftype gettypeid_func='CFNullGetTypeID' name='CFNullRef' tollfree='NSNull' type64='^{__CFNull=}'/>
<cftype gettypeid_func='CFNumberFormatterGetTypeID' name='CFNumberFormatterRef' tollfree='__NSCFType' type64='^{__CFNumberFormatter=}'/>
<cftype gettypeid_func='CFNumberGetTypeID' name='CFNumberRef' tollfree='__NSCFNumber' type64='^{__CFNumber=}'/>
<cftype gettypeid_func='CFPlugInInstanceGetTypeID' name='CFPlugInInstanceRef' tollfree='__NSCFType' type64='^{__CFPlugInInstance=}'/>
<cftype gettypeid_func='CFPlugInGetTypeID' name='CFPlugInRef' tollfree='__NSCFType' type64='^{__CFBundle=}'/>
<cftype gettypeid_func='CFReadStreamGetTypeID' name='CFReadStreamRef' tollfree='__NSCFInputStream' type64='^{__CFReadStream=}'/>
<cftype gettypeid_func='CFRunLoopObserverGetTypeID' name='CFRunLoopObserverRef' tollfree='__NSCFType' type64='^{__CFRunLoopObserver=}'/>
<cftype gettypeid_func='CFRunLoopGetTypeID' name='CFRunLoopRef' tollfree='__NSCFType' type64='^{__CFRunLoop=}'/>
<cftype gettypeid_func='CFRunLoopSourceGetTypeID' name='CFRunLoopSourceRef' tollfree='__NSCFType' type64='^{__CFRunLoopSource=}'/>
<cftype gettypeid_func='CFRunLoopTimerGetTypeID' name='CFRunLoopTimerRef' tollfree='__NSCFTimer' type64='^{__CFRunLoopTimer=}'/>
<cftype gettypeid_func='CFSetGetTypeID' name='CFSetRef' tollfree='__NSCFSet' type64='^{__CFSet=}'/>
<cftype gettypeid_func='CFSocketGetTypeID' name='CFSocketRef' tollfree='__NSCFType' type64='^{__CFSocket=}'/>
<cftype gettypeid_func='CFStringGetTypeID' name='CFStringRef' tollfree='__NSCFString' type64='^{__CFString=}'/>
<cftype gettypeid_func='CFStringTokenizerGetTypeID' name='CFStringTokenizerRef' tollfree='__NSCFType' type64='^{__CFStringTokenizer=}'/>
<cftype gettypeid_func='CFTimeZoneGetTypeID' name='CFTimeZoneRef' tollfree='__NSCFType' type64='^{__CFTimeZone=}'/>
<cftype gettypeid_func='CFTreeGetTypeID' name='CFTreeRef' tollfree='__NSCFType' type64='^{__CFTree=}'/>
<cftype gettypeid_func='CFURLEnumeratorGetTypeID' name='CFURLEnumeratorRef' tollfree='__NSCFType' type64='^{__CFURLEnumerator=}'/>
<cftype gettypeid_func='CFURLGetTypeID' name='CFURLRef' tollfree='NSURL' type64='^{__CFURL=}'/>
<cftype gettypeid_func='CFUUIDGetTypeID' name='CFUUIDRef' tollfree='__NSCFType' type64='^{__CFUUID=}'/>
<cftype gettypeid_func='CFUserNotificationGetTypeID' name='CFUserNotificationRef' tollfree='__NSCFType' type64='^{__CFUserNotification=}'/>
<cftype gettypeid_func='CFWriteStreamGetTypeID' name='CFWriteStreamRef' tollfree='__NSCFOutputStream' type64='^{__CFWriteStream=}'/>
<cftype gettypeid_func='CFXMLNodeGetTypeID' name='CFXMLNodeRef' tollfree='__NSCFType' type64='^{__CFXMLNode=}'/>
<cftype gettypeid_func='CFXMLParserGetTypeID' name='CFXMLParserRef' tollfree='__NSCFType' type64='^{__CFXMLParser=}'/>
<opaque name='CFTypeRef' type64='^v'/>
<constant name='kCFAbsoluteTimeIntervalSince1904' type64='d'/>
<constant name='kCFAbsoluteTimeIntervalSince1970' type64='d'/>
<constant name='kCFAllocatorDefault' type64='^{__CFAllocator=}'/>
<constant name='kCFAllocatorMalloc' type64='^{__CFAllocator=}'/>
<constant name='kCFAllocatorMallocZone' type64='^{__CFAllocator=}'/>
<constant name='kCFAllocatorNull' type64='^{__CFAllocator=}'/>
<constant name='kCFAllocatorSystemDefault' type64='^{__CFAllocator=}'/>
<constant magic_cookie='true' name='kCFAllocatorUseContext' type64='^{__CFAllocator=}'/>
<constant name='kCFBooleanFalse' type64='^{__CFBoolean=}'/>
<constant name='kCFBooleanTrue' type64='^{__CFBoolean=}'/>
<constant name='kCFBuddhistCalendar' type64='^{__CFString=}'/>
<constant name='kCFBundleDevelopmentRegionKey' type64='^{__CFString=}'/>
<constant name='kCFBundleExecutableKey' type64='^{__CFString=}'/>
<constant name='kCFBundleIdentifierKey' type64='^{__CFString=}'/>
<constant name='kCFBundleInfoDictionaryVersionKey' type64='^{__CFString=}'/>
<constant name='kCFBundleLocalizationsKey' type64='^{__CFString=}'/>
<constant name='kCFBundleNameKey' type64='^{__CFString=}'/>
<constant name='kCFBundleVersionKey' type64='^{__CFString=}'/>
<constant name='kCFChineseCalendar' type64='^{__CFString=}'/>
<constant name='kCFCopyStringBagCallBacks' type64='{_CFBagCallBacks=q^?^?^?^?^?}'/>
<constant name='kCFCopyStringDictionaryKeyCallBacks' type64='{_CFDictionaryKeyCallBacks=q^?^?^?^?^?}'/>
<constant name='kCFCopyStringSetCallBacks' type64='{_CFSetCallBacks=q^?^?^?^?^?}'/>
<constant name='kCFCoreFoundationVersionNumber' type64='d'/>
<constant name='kCFDateFormatterAMSymbol' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterCalendar' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterCalendarName' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterDefaultDate' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterDefaultFormat' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterDoesRelativeDateFormattingKey' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterEraSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterGregorianStartDate' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterIsLenient' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterLongEraSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterMonthSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterPMSymbol' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterQuarterSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterShortMonthSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterShortQuarterSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterShortStandaloneMonthSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterShortStandaloneQuarterSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterShortStandaloneWeekdaySymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterShortWeekdaySymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterStandaloneMonthSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterStandaloneQuarterSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterStandaloneWeekdaySymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterTimeZone' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterTwoDigitStartDate' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterVeryShortMonthSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterVeryShortStandaloneMonthSymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterVeryShortStandaloneWeekdaySymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterVeryShortWeekdaySymbols' type64='^{__CFString=}'/>
<constant name='kCFDateFormatterWeekdaySymbols' type64='^{__CFString=}'/>
<constant name='kCFErrorDescriptionKey' type64='^{__CFString=}'/>
<constant name='kCFErrorDomainCocoa' type64='^{__CFString=}'/>
<constant name='kCFErrorDomainMach' type64='^{__CFString=}'/>
<constant name='kCFErrorDomainOSStatus' type64='^{__CFString=}'/>
<constant name='kCFErrorDomainPOSIX' type64='^{__CFString=}'/>
<constant name='kCFErrorFilePathKey' type64='^{__CFString=}'/>
<constant name='kCFErrorLocalizedDescriptionKey' type64='^{__CFString=}'/>
<constant name='kCFErrorLocalizedFailureKey' type64='^{__CFString=}'/>
<constant name='kCFErrorLocalizedFailureReasonKey' type64='^{__CFString=}'/>
<constant name='kCFErrorLocalizedRecoverySuggestionKey' type64='^{__CFString=}'/>
<constant name='kCFErrorURLKey' type64='^{__CFString=}'/>
<constant name='kCFErrorUnderlyingErrorKey' type64='^{__CFString=}'/>
<constant name='kCFGregorianCalendar' type64='^{__CFString=}'/>
<constant name='kCFHebrewCalendar' type64='^{__CFString=}'/>
<constant name='kCFISO8601Calendar' type64='^{__CFString=}'/>
<constant name='kCFIndianCalendar' type64='^{__CFString=}'/>
<constant name='kCFIslamicCalendar' type64='^{__CFString=}'/>
<constant name='kCFIslamicCivilCalendar' type64='^{__CFString=}'/>
<constant name='kCFIslamicTabularCalendar' type64='^{__CFString=}'/>
<constant name='kCFIslamicUmmAlQuraCalendar' type64='^{__CFString=}'/>
<constant name='kCFJapaneseCalendar' type64='^{__CFString=}'/>
<constant name='kCFLocaleAlternateQuotationBeginDelimiterKey' type64='^{__CFString=}'/>
<constant name='kCFLocaleAlternateQuotationEndDelimiterKey' type64='^{__CFString=}'/>
<constant name='kCFLocaleCalendar' type64='^{__CFString=}'/>
<constant name='kCFLocaleCalendarIdentifier' type64='^{__CFString=}'/>
<constant name='kCFLocaleCollationIdentifier' type64='^{__CFString=}'/>
<constant name='kCFLocaleCollatorIdentifier' type64='^{__CFString=}'/>
<constant name='kCFLocaleCountryCode' type64='^{__CFString=}'/>
<constant name='kCFLocaleCurrencyCode' type64='^{__CFString=}'/>
<constant name='kCFLocaleCurrencySymbol' type64='^{__CFString=}'/>
<constant name='kCFLocaleCurrentLocaleDidChangeNotification' type64='^{__CFString=}'/>
<constant name='kCFLocaleDecimalSeparator' type64='^{__CFString=}'/>
<constant name='kCFLocaleExemplarCharacterSet' type64='^{__CFString=}'/>
<constant name='kCFLocaleGroupingSeparator' type64='^{__CFString=}'/>
<constant name='kCFLocaleIdentifier' type64='^{__CFString=}'/>
<constant name='kCFLocaleLanguageCode' type64='^{__CFString=}'/>
<constant name='kCFLocaleMeasurementSystem' type64='^{__CFString=}'/>
<constant name='kCFLocaleQuotationBeginDelimiterKey' type64='^{__CFString=}'/>
<constant name='kCFLocaleQuotationEndDelimiterKey' type64='^{__CFString=}'/>
<constant name='kCFLocaleScriptCode' type64='^{__CFString=}'/>
<constant name='kCFLocaleUsesMetricSystem' type64='^{__CFString=}'/>
<constant name='kCFLocaleVariantCode' type64='^{__CFString=}'/>
<constant name='kCFNull' type64='^{__CFNull=}'/>
<constant name='kCFNumberFormatterAlwaysShowDecimalSeparator' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterCurrencyCode' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterCurrencyDecimalSeparator' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterCurrencyGroupingSeparator' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterCurrencySymbol' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterDecimalSeparator' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterDefaultFormat' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterExponentSymbol' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterFormatWidth' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterGroupingSeparator' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterGroupingSize' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterInfinitySymbol' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterInternationalCurrencySymbol' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterIsLenient' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterMaxFractionDigits' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterMaxIntegerDigits' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterMaxSignificantDigits' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterMinFractionDigits' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterMinIntegerDigits' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterMinSignificantDigits' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterMinusSign' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterMultiplier' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterNaNSymbol' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterNegativePrefix' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterNegativeSuffix' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterPaddingCharacter' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterPaddingPosition' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterPerMillSymbol' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterPercentSymbol' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterPlusSign' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterPositivePrefix' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterPositiveSuffix' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterRoundingIncrement' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterRoundingMode' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterSecondaryGroupingSize' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterUseGroupingSeparator' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterUseSignificantDigits' type64='^{__CFString=}'/>
<constant name='kCFNumberFormatterZeroSymbol' type64='^{__CFString=}'/>
<constant name='kCFNumberNaN' type64='^{__CFNumber=}'/>
<constant name='kCFNumberNegativeInfinity' type64='^{__CFNumber=}'/>
<constant name='kCFNumberPositiveInfinity' type64='^{__CFNumber=}'/>
<constant name='kCFPersianCalendar' type64='^{__CFString=}'/>
<constant name='kCFPlugInDynamicRegisterFunctionKey' type64='^{__CFString=}'/>
<constant name='kCFPlugInDynamicRegistrationKey' type64='^{__CFString=}'/>
<constant name='kCFPlugInFactoriesKey' type64='^{__CFString=}'/>
<constant name='kCFPlugInTypesKey' type64='^{__CFString=}'/>
<constant name='kCFPlugInUnloadFunctionKey' type64='^{__CFString=}'/>
<constant name='kCFPreferencesAnyApplication' type64='^{__CFString=}'/>
<constant name='kCFPreferencesAnyHost' type64='^{__CFString=}'/>
<constant name='kCFPreferencesAnyUser' type64='^{__CFString=}'/>
<constant name='kCFPreferencesCurrentApplication' type64='^{__CFString=}'/>
<constant name='kCFPreferencesCurrentHost' type64='^{__CFString=}'/>
<constant name='kCFPreferencesCurrentUser' type64='^{__CFString=}'/>
<constant name='kCFRepublicOfChinaCalendar' type64='^{__CFString=}'/>
<constant name='kCFRunLoopCommonModes' type64='^{__CFString=}'/>
<constant name='kCFRunLoopDefaultMode' type64='^{__CFString=}'/>
<constant name='kCFSocketCommandKey' type64='^{__CFString=}'/>
<constant name='kCFSocketErrorKey' type64='^{__CFString=}'/>
<constant name='kCFSocketNameKey' type64='^{__CFString=}'/>
<constant name='kCFSocketRegisterCommand' type64='^{__CFString=}'/>
<constant name='kCFSocketResultKey' type64='^{__CFString=}'/>
<constant name='kCFSocketRetrieveCommand' type64='^{__CFString=}'/>
<constant name='kCFSocketValueKey' type64='^{__CFString=}'/>
<constant name='kCFStreamErrorDomainSOCKS' type64='i'/>
<constant name='kCFStreamErrorDomainSSL' type64='i'/>
<constant name='kCFStreamPropertyAppendToFile' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertyDataWritten' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertyFileCurrentOffset' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertySOCKSPassword' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertySOCKSProxy' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertySOCKSProxyHost' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertySOCKSProxyPort' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertySOCKSUser' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertySOCKSVersion' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertyShouldCloseNativeSocket' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertySocketNativeHandle' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertySocketRemoteHostName' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertySocketRemotePortNumber' type64='^{__CFString=}'/>
<constant name='kCFStreamPropertySocketSecurityLevel' type64='^{__CFString=}'/>
<constant name='kCFStreamSocketSOCKSVersion4' type64='^{__CFString=}'/>
<constant name='kCFStreamSocketSOCKSVersion5' type64='^{__CFString=}'/>
<constant name='kCFStreamSocketSecurityLevelNegotiatedSSL' type64='^{__CFString=}'/>
<constant name='kCFStreamSocketSecurityLevelNone' type64='^{__CFString=}'/>
<constant name='kCFStreamSocketSecurityLevelSSLv2' type64='^{__CFString=}'/>
<constant name='kCFStreamSocketSecurityLevelSSLv3' type64='^{__CFString=}'/>
<constant name='kCFStreamSocketSecurityLevelTLSv1' type64='^{__CFString=}'/>
<constant name='kCFStringBinaryHeapCallBacks' type64='{_CFBinaryHeapCallBacks=q^?^?^?^?}'/>
<constant name='kCFStringTransformFullwidthHalfwidth' type64='^{__CFString=}'/>
<constant name='kCFStringTransformHiraganaKatakana' type64='^{__CFString=}'/>
<constant name='kCFStringTransformLatinArabic' type64='^{__CFString=}'/>
<constant name='kCFStringTransformLatinCyrillic' type64='^{__CFString=}'/>
<constant name='kCFStringTransformLatinGreek' type64='^{__CFString=}'/>
<constant name='kCFStringTransformLatinHangul' type64='^{__CFString=}'/>
<constant name='kCFStringTransformLatinHebrew' type64='^{__CFString=}'/>
<constant name='kCFStringTransformLatinHiragana' type64='^{__CFString=}'/>
<constant name='kCFStringTransformLatinKatakana' type64='^{__CFString=}'/>
<constant name='kCFStringTransformLatinThai' type64='^{__CFString=}'/>
<constant name='kCFStringTransformMandarinLatin' type64='^{__CFString=}'/>
<constant name='kCFStringTransformStripCombiningMarks' type64='^{__CFString=}'/>
<constant name='kCFStringTransformStripDiacritics' type64='^{__CFString=}'/>
<constant name='kCFStringTransformToLatin' type64='^{__CFString=}'/>
<constant name='kCFStringTransformToUnicodeName' type64='^{__CFString=}'/>
<constant name='kCFStringTransformToXMLHex' type64='^{__CFString=}'/>
<constant name='kCFTimeZoneSystemTimeZoneDidChangeNotification' type64='^{__CFString=}'/>
<constant name='kCFTypeArrayCallBacks' type64='{_CFArrayCallBacks=q^?^?^?^?}'/>
<constant name='kCFTypeBagCallBacks' type64='{_CFBagCallBacks=q^?^?^?^?^?}'/>
<constant name='kCFTypeDictionaryKeyCallBacks' type64='{_CFDictionaryKeyCallBacks=q^?^?^?^?^?}'/>
<constant name='kCFTypeDictionaryValueCallBacks' type64='{_CFDictionaryValueCallBacks=q^?^?^?^?}'/>
<constant name='kCFTypeSetCallBacks' type64='{_CFSetCallBacks=q^?^?^?^?^?}'/>
<constant name='kCFURLAddedToDirectoryDateKey' type64='^{__CFString=}'/>
<constant name='kCFURLApplicationIsScriptableKey' type64='^{__CFString=}'/>
<constant name='kCFURLAttributeModificationDateKey' type64='^{__CFString=}'/>
<constant name='kCFURLCanonicalPathKey' type64='^{__CFString=}'/>
<constant name='kCFURLContentAccessDateKey' type64='^{__CFString=}'/>
<constant name='kCFURLContentModificationDateKey' type64='^{__CFString=}'/>
<constant name='kCFURLCreationDateKey' type64='^{__CFString=}'/>
<constant name='kCFURLCustomIconKey' type64='^{__CFString=}'/>
<constant name='kCFURLDocumentIdentifierKey' type64='^{__CFString=}'/>
<constant name='kCFURLEffectiveIconKey' type64='^{__CFString=}'/>
<constant name='kCFURLFileAllocatedSizeKey' type64='^{__CFString=}'/>
<constant name='kCFURLFileContentIdentifierKey' type64='^{__CFString=}'/>
<constant name='kCFURLFileDirectoryContents' type64='^{__CFString=}'/>
<constant name='kCFURLFileExists' type64='^{__CFString=}'/>
<constant name='kCFURLFileLastModificationTime' type64='^{__CFString=}'/>
<constant name='kCFURLFileLength' type64='^{__CFString=}'/>
<constant name='kCFURLFileOwnerID' type64='^{__CFString=}'/>
<constant name='kCFURLFilePOSIXMode' type64='^{__CFString=}'/>
<constant name='kCFURLFileProtectionComplete' type64='^{__CFString=}'/>
<constant name='kCFURLFileProtectionCompleteUnlessOpen' type64='^{__CFString=}'/>
<constant name='kCFURLFileProtectionCompleteUntilFirstUserAuthentication' type64='^{__CFString=}'/>
<constant name='kCFURLFileProtectionKey' type64='^{__CFString=}'/>
<constant name='kCFURLFileProtectionNone' type64='^{__CFString=}'/>
<constant name='kCFURLFileResourceIdentifierKey' type64='^{__CFString=}'/>
<constant name='kCFURLFileResourceTypeBlockSpecial' type64='^{__CFString=}'/>
<constant name='kCFURLFileResourceTypeCharacterSpecial' type64='^{__CFString=}'/>
<constant name='kCFURLFileResourceTypeDirectory' type64='^{__CFString=}'/>
<constant name='kCFURLFileResourceTypeKey' type64='^{__CFString=}'/>
<constant name='kCFURLFileResourceTypeNamedPipe' type64='^{__CFString=}'/>
<constant name='kCFURLFileResourceTypeRegular' type64='^{__CFString=}'/>
<constant name='kCFURLFileResourceTypeSocket' type64='^{__CFString=}'/>
<constant name='kCFURLFileResourceTypeSymbolicLink' type64='^{__CFString=}'/>
<constant name='kCFURLFileResourceTypeUnknown' type64='^{__CFString=}'/>
<constant name='kCFURLFileSecurityKey' type64='^{__CFString=}'/>
<constant name='kCFURLFileSizeKey' type64='^{__CFString=}'/>
<constant name='kCFURLGenerationIdentifierKey' type64='^{__CFString=}'/>
<constant name='kCFURLHTTPStatusCode' type64='^{__CFString=}'/>
<constant name='kCFURLHTTPStatusLine' type64='^{__CFString=}'/>
<constant name='kCFURLHasHiddenExtensionKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsAliasFileKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsApplicationKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsDirectoryKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsExcludedFromBackupKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsExecutableKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsHiddenKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsMountTriggerKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsPackageKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsPurgeableKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsReadableKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsRegularFileKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsSparseKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsSymbolicLinkKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsSystemImmutableKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsUbiquitousItemKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsUserImmutableKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsVolumeKey' type64='^{__CFString=}'/>
<constant name='kCFURLIsWritableKey' type64='^{__CFString=}'/>
<constant name='kCFURLKeysOfUnsetValuesKey' type64='^{__CFString=}'/>
<constant name='kCFURLLabelColorKey' type64='^{__CFString=}'/>
<constant name='kCFURLLabelNumberKey' type64='^{__CFString=}'/>
<constant name='kCFURLLinkCountKey' type64='^{__CFString=}'/>
<constant name='kCFURLLocalizedLabelKey' type64='^{__CFString=}'/>
<constant name='kCFURLLocalizedNameKey' type64='^{__CFString=}'/>
<constant name='kCFURLLocalizedTypeDescriptionKey' type64='^{__CFString=}'/>
<constant name='kCFURLMayHaveExtendedAttributesKey' type64='^{__CFString=}'/>
<constant name='kCFURLMayShareFileContentKey' type64='^{__CFString=}'/>
<constant name='kCFURLNameKey' type64='^{__CFString=}'/>
<constant name='kCFURLParentDirectoryURLKey' type64='^{__CFString=}'/>
<constant name='kCFURLPathKey' type64='^{__CFString=}'/>
<constant name='kCFURLPreferredIOBlockSizeKey' type64='^{__CFString=}'/>
<constant name='kCFURLQuarantinePropertiesKey' type64='^{__CFString=}'/>
<constant name='kCFURLTagNamesKey' type64='^{__CFString=}'/>
<constant name='kCFURLTotalFileAllocatedSizeKey' type64='^{__CFString=}'/>
<constant name='kCFURLTotalFileSizeKey' type64='^{__CFString=}'/>
<constant name='kCFURLTypeIdentifierKey' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemDownloadingErrorKey' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemDownloadingStatusCurrent' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemDownloadingStatusDownloaded' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemDownloadingStatusKey' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemDownloadingStatusNotDownloaded' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemHasUnresolvedConflictsKey' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemIsDownloadedKey' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemIsDownloadingKey' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemIsUploadedKey' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemIsUploadingKey' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemPercentDownloadedKey' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemPercentUploadedKey' type64='^{__CFString=}'/>
<constant name='kCFURLUbiquitousItemUploadingErrorKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeAvailableCapacityForImportantUsageKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeAvailableCapacityForOpportunisticUsageKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeAvailableCapacityKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeCreationDateKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIdentifierKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIsAutomountedKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIsBrowsableKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIsEjectableKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIsEncryptedKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIsInternalKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIsJournalingKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIsLocalKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIsReadOnlyKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIsRemovableKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeIsRootFileSystemKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeLocalizedFormatDescriptionKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeLocalizedNameKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeMaximumFileSizeKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeNameKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeResourceCountKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsAccessPermissionsKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsAdvisoryFileLockingKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsCasePreservedNamesKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsCaseSensitiveNamesKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsCompressionKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsExclusiveRenamingKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsExtendedSecurityKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsFileCloningKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsFileProtectionKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsHardLinksKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsImmutableFilesKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsJournalingKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsPersistentIDsKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsRenamingKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsRootDirectoryDatesKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsSparseFilesKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsSwapRenamingKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsSymbolicLinksKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsVolumeSizesKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeSupportsZeroRunsKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeTotalCapacityKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeURLForRemountingKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeURLKey' type64='^{__CFString=}'/>
<constant name='kCFURLVolumeUUIDStringKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationAlertHeaderKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationAlertMessageKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationAlertTopMostKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationAlternateButtonTitleKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationCheckBoxTitlesKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationDefaultButtonTitleKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationIconURLKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationKeyboardTypesKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationLocalizationURLKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationOtherButtonTitleKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationPopUpSelectionKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationPopUpTitlesKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationProgressIndicatorValueKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationSoundURLKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationTextFieldTitlesKey' type64='^{__CFString=}'/>
<constant name='kCFUserNotificationTextFieldValuesKey' type64='^{__CFString=}'/>
<constant name='kCFXMLTreeErrorDescription' type64='^{__CFString=}'/>
<constant name='kCFXMLTreeErrorLineNumber' type64='^{__CFString=}'/>
<constant name='kCFXMLTreeErrorLocation' type64='^{__CFString=}'/>
<constant name='kCFXMLTreeErrorStatusCode' type64='^{__CFString=}'/>
<enum name='CFByteOrderBigEndian' value64='2'/>
<enum name='CFByteOrderLittleEndian' value64='1'/>
<enum name='CFByteOrderUnknown' value64='0'/>
<enum name='CFNotificationSuspensionBehaviorCoalesce' value64='2'/>
<enum name='CFNotificationSuspensionBehaviorDeliverImmediately' value64='4'/>
<enum name='CFNotificationSuspensionBehaviorDrop' value64='1'/>
<enum name='CFNotificationSuspensionBehaviorHold' value64='3'/>
<enum name='CF_USE_OSBYTEORDER_H' value64='1'/>
<enum name='COREFOUNDATION_CFPLUGINCOM_SEPARATE' value64='1'/>
<enum name='E_ABORT' value64='-2147483641'/>
<enum name='E_ACCESSDENIED' value64='-2147483639'/>
<enum name='E_FAIL' value64='-2147483640'/>
<enum name='E_HANDLE' value64='-2147483642'/>
<enum name='E_INVALIDARG' value64='-2147483645'/>
<enum name='E_NOINTERFACE' value64='-2147483644'/>
<enum name='E_NOTIMPL' value64='-2147483647'/>
<enum name='E_OUTOFMEMORY' value64='-2147483646'/>
<enum name='E_POINTER' value64='-2147483643'/>
<enum name='E_UNEXPECTED' value64='-2147418113'/>
<enum name='FALSE' value64='0'/>
<enum name='ISA_PTRAUTH_DISCRIMINATOR' value64='27361'/>
<enum name='SEVERITY_ERROR' value64='1'/>
<enum name='SEVERITY_SUCCESS' value64='0'/>
<enum name='S_FALSE' value64='1'/>
<enum name='S_OK' value64='0'/>
<enum name='TRUE' value64='1'/>
<enum name='kCFBookmarkResolutionWithoutMountingMask' value64='512'/>
<enum name='kCFBookmarkResolutionWithoutUIMask' value64='256'/>
<enum name='kCFBundleExecutableArchitectureARM64' value64='16777228'/>
<enum name='kCFBundleExecutableArchitectureI386' value64='7'/>
<enum name='kCFBundleExecutableArchitecturePPC' value64='18'/>
<enum name='kCFBundleExecutableArchitecturePPC64' value64='16777234'/>
<enum name='kCFBundleExecutableArchitectureX86_64' value64='16777223'/>
<enum name='kCFCalendarComponentsWrap' value64='1'/>
<enum name='kCFCalendarUnitDay' value64='16'/>
<enum name='kCFCalendarUnitEra' value64='2'/>
<enum name='kCFCalendarUnitHour' value64='32'/>
<enum name='kCFCalendarUnitMinute' value64='64'/>
<enum name='kCFCalendarUnitMonth' value64='8'/>
<enum name='kCFCalendarUnitQuarter' value64='2048'/>
<enum name='kCFCalendarUnitSecond' value64='128'/>
<enum name='kCFCalendarUnitWeek' value64='256'/>
<enum name='kCFCalendarUnitWeekOfMonth' value64='4096'/>
<enum name='kCFCalendarUnitWeekOfYear' value64='8192'/>
<enum name='kCFCalendarUnitWeekday' value64='512'/>
<enum name='kCFCalendarUnitWeekdayOrdinal' value64='1024'/>
<enum name='kCFCalendarUnitYear' value64='4'/>
<enum name='kCFCalendarUnitYearForWeekOfYear' value64='16384'/>
<enum name='kCFCharacterSetAlphaNumeric' value64='10'/>
<enum name='kCFCharacterSetCapitalizedLetter' value64='13'/>
<enum name='kCFCharacterSetControl' value64='1'/>
<enum name='kCFCharacterSetDecimalDigit' value64='4'/>
<enum name='kCFCharacterSetDecomposable' value64='9'/>
<enum name='kCFCharacterSetIllegal' value64='12'/>
<enum name='kCFCharacterSetLetter' value64='5'/>
<enum name='kCFCharacterSetLowercaseLetter' value64='6'/>
<enum name='kCFCharacterSetNewline' value64='15'/>
<enum name='kCFCharacterSetNonBase' value64='8'/>
<enum name='kCFCharacterSetPunctuation' value64='11'/>
<enum name='kCFCharacterSetSymbol' value64='14'/>
<enum name='kCFCharacterSetUppercaseLetter' value64='7'/>
<enum name='kCFCharacterSetWhitespace' value64='2'/>
<enum name='kCFCharacterSetWhitespaceAndNewline' value64='3'/>
<enum name='kCFCompareAnchored' value64='8'/>
<enum name='kCFCompareBackwards' value64='4'/>
<enum name='kCFCompareCaseInsensitive' value64='1'/>
<enum name='kCFCompareDiacriticInsensitive' value64='128'/>
<enum name='kCFCompareEqualTo' value64='0'/>
<enum name='kCFCompareForcedOrdering' value64='512'/>
<enum name='kCFCompareGreaterThan' value64='1'/>
<enum name='kCFCompareLessThan' value64='-1'/>
<enum name='kCFCompareLocalized' value64='32'/>
<enum name='kCFCompareNonliteral' value64='16'/>
<enum name='kCFCompareNumerically' value64='64'/>
<enum name='kCFCompareWidthInsensitive' value64='256'/>
<enum name='kCFCoreFoundationVersionNumber10_0' value64='196.400000'/>
<enum name='kCFCoreFoundationVersionNumber10_0_3' value64='196.500000'/>
<enum name='kCFCoreFoundationVersionNumber10_1' value64='226.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_10' value64='1151.160000'/>
<enum name='kCFCoreFoundationVersionNumber10_10_1' value64='1151.160000'/>
<enum name='kCFCoreFoundationVersionNumber10_10_2' value64='1152'/>
<enum name='kCFCoreFoundationVersionNumber10_10_3' value64='1153.180000'/>
<enum name='kCFCoreFoundationVersionNumber10_10_4' value64='1153.180000'/>
<enum name='kCFCoreFoundationVersionNumber10_10_5' value64='1153.180000'/>
<enum name='kCFCoreFoundationVersionNumber10_10_Max' value64='1199'/>
<enum name='kCFCoreFoundationVersionNumber10_11' value64='1253'/>
<enum name='kCFCoreFoundationVersionNumber10_11_1' value64='1255.100000'/>
<enum name='kCFCoreFoundationVersionNumber10_11_2' value64='1256.140000'/>
<enum name='kCFCoreFoundationVersionNumber10_11_3' value64='1256.140000'/>
<enum name='kCFCoreFoundationVersionNumber10_11_4' value64='1258.100000'/>
<enum name='kCFCoreFoundationVersionNumber10_11_Max' value64='1299'/>
<enum name='kCFCoreFoundationVersionNumber10_1_1' value64='226.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_1_2' value64='227.200000'/>
<enum name='kCFCoreFoundationVersionNumber10_1_3' value64='227.200000'/>
<enum name='kCFCoreFoundationVersionNumber10_1_4' value64='227.300000'/>
<enum name='kCFCoreFoundationVersionNumber10_2' value64='263.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_2_1' value64='263.100000'/>
<enum name='kCFCoreFoundationVersionNumber10_2_2' value64='263.100000'/>
<enum name='kCFCoreFoundationVersionNumber10_2_3' value64='263.300000'/>
<enum name='kCFCoreFoundationVersionNumber10_2_4' value64='263.300000'/>
<enum name='kCFCoreFoundationVersionNumber10_2_5' value64='263.500000'/>
<enum name='kCFCoreFoundationVersionNumber10_2_6' value64='263.500000'/>
<enum name='kCFCoreFoundationVersionNumber10_2_7' value64='263.500000'/>
<enum name='kCFCoreFoundationVersionNumber10_2_8' value64='263.500000'/>
<enum name='kCFCoreFoundationVersionNumber10_3' value64='299.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_3_1' value64='299.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_3_2' value64='299.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_3_3' value64='299.300000'/>
<enum name='kCFCoreFoundationVersionNumber10_3_4' value64='299.310000'/>
<enum name='kCFCoreFoundationVersionNumber10_3_5' value64='299.310000'/>
<enum name='kCFCoreFoundationVersionNumber10_3_6' value64='299.320000'/>
<enum name='kCFCoreFoundationVersionNumber10_3_7' value64='299.330000'/>
<enum name='kCFCoreFoundationVersionNumber10_3_8' value64='299.330000'/>
<enum name='kCFCoreFoundationVersionNumber10_3_9' value64='299.350000'/>
<enum name='kCFCoreFoundationVersionNumber10_4' value64='368.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_1' value64='368.100000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_10' value64='368.280000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_11' value64='368.310000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_2' value64='368.110000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_3' value64='368.180000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_4_Intel' value64='368.260000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_4_PowerPC' value64='368.250000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_5_Intel' value64='368.260000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_5_PowerPC' value64='368.250000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_6_Intel' value64='368.260000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_6_PowerPC' value64='368.250000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_7' value64='368.270000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_8' value64='368.270000'/>
<enum name='kCFCoreFoundationVersionNumber10_4_9' value64='368.280000'/>
<enum name='kCFCoreFoundationVersionNumber10_5' value64='476.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_5_1' value64='476.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_5_2' value64='476.100000'/>
<enum name='kCFCoreFoundationVersionNumber10_5_3' value64='476.130000'/>
<enum name='kCFCoreFoundationVersionNumber10_5_4' value64='476.140000'/>
<enum name='kCFCoreFoundationVersionNumber10_5_5' value64='476.150000'/>
<enum name='kCFCoreFoundationVersionNumber10_5_6' value64='476.170000'/>
<enum name='kCFCoreFoundationVersionNumber10_5_7' value64='476.180000'/>
<enum name='kCFCoreFoundationVersionNumber10_5_8' value64='476.190000'/>
<enum name='kCFCoreFoundationVersionNumber10_6' value64='550.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_6_1' value64='550.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_6_2' value64='550.130000'/>
<enum name='kCFCoreFoundationVersionNumber10_6_3' value64='550.190000'/>
<enum name='kCFCoreFoundationVersionNumber10_6_4' value64='550.290000'/>
<enum name='kCFCoreFoundationVersionNumber10_6_5' value64='550.420000'/>
<enum name='kCFCoreFoundationVersionNumber10_6_6' value64='550.420000'/>
<enum name='kCFCoreFoundationVersionNumber10_6_7' value64='550.420000'/>
<enum name='kCFCoreFoundationVersionNumber10_6_8' value64='550.430000'/>
<enum name='kCFCoreFoundationVersionNumber10_7' value64='635.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_7_1' value64='635.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_7_2' value64='635.150000'/>
<enum name='kCFCoreFoundationVersionNumber10_7_3' value64='635.190000'/>
<enum name='kCFCoreFoundationVersionNumber10_7_4' value64='635.210000'/>
<enum name='kCFCoreFoundationVersionNumber10_7_5' value64='635.210000'/>
<enum name='kCFCoreFoundationVersionNumber10_8' value64='744.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_8_1' value64='744.000000'/>
<enum name='kCFCoreFoundationVersionNumber10_8_2' value64='744.120000'/>
<enum name='kCFCoreFoundationVersionNumber10_8_3' value64='744.180000'/>
<enum name='kCFCoreFoundationVersionNumber10_8_4' value64='744.190000'/>
<enum name='kCFCoreFoundationVersionNumber10_9' value64='855.110000'/>
<enum name='kCFCoreFoundationVersionNumber10_9_1' value64='855.110000'/>
<enum name='kCFCoreFoundationVersionNumber10_9_2' value64='855.140000'/>
<enum name='kCFDataSearchAnchored' value64='2'/>
<enum name='kCFDataSearchBackwards' value64='1'/>
<enum name='kCFDateFormatterFullStyle' value64='4'/>
<enum name='kCFDateFormatterLongStyle' value64='3'/>
<enum name='kCFDateFormatterMediumStyle' value64='2'/>
<enum name='kCFDateFormatterNoStyle' value64='0'/>
<enum name='kCFDateFormatterShortStyle' value64='1'/>
<enum name='kCFFileDescriptorReadCallBack' value64='1'/>
<enum name='kCFFileDescriptorWriteCallBack' value64='2'/>
<enum name='kCFFileSecurityClearAccessControlList' value64='32'/>
<enum name='kCFFileSecurityClearGroup' value64='2'/>
<enum name='kCFFileSecurityClearGroupUUID' value64='16'/>
<enum name='kCFFileSecurityClearMode' value64='4'/>
<enum name='kCFFileSecurityClearOwner' value64='1'/>
<enum name='kCFFileSecurityClearOwnerUUID' value64='8'/>
<enum name='kCFGregorianAllUnits' value64='16777215'/>
<enum name='kCFGregorianUnitsDays' value64='4'/>
<enum name='kCFGregorianUnitsHours' value64='8'/>
<enum name='kCFGregorianUnitsMinutes' value64='16'/>
<enum name='kCFGregorianUnitsMonths' value64='2'/>
<enum name='kCFGregorianUnitsSeconds' value64='32'/>
<enum name='kCFGregorianUnitsYears' value64='1'/>
<enum name='kCFISO8601DateFormatWithColonSeparatorInTime' value64='512'/>
<enum name='kCFISO8601DateFormatWithColonSeparatorInTimeZone' value64='1024'/>
<enum name='kCFISO8601DateFormatWithDashSeparatorInDate' value64='256'/>
<enum name='kCFISO8601DateFormatWithDay' value64='16'/>
<enum name='kCFISO8601DateFormatWithFractionalSeconds' value64='2048'/>
<enum name='kCFISO8601DateFormatWithFullDate' value64='275'/>
<enum name='kCFISO8601DateFormatWithFullTime' value64='1632'/>
<enum name='kCFISO8601DateFormatWithInternetDateTime' value64='1907'/>
<enum name='kCFISO8601DateFormatWithMonth' value64='2'/>
<enum name='kCFISO8601DateFormatWithSpaceBetweenDateAndTime' value64='128'/>
<enum name='kCFISO8601DateFormatWithTime' value64='32'/>
<enum name='kCFISO8601DateFormatWithTimeZone' value64='64'/>
<enum name='kCFISO8601DateFormatWithWeekOfYear' value64='4'/>
<enum name='kCFISO8601DateFormatWithYear' value64='1'/>
<enum name='kCFLocaleLanguageDirectionBottomToTop' value64='4'/>
<enum name='kCFLocaleLanguageDirectionLeftToRight' value64='1'/>
<enum name='kCFLocaleLanguageDirectionRightToLeft' value64='2'/>
<enum name='kCFLocaleLanguageDirectionTopToBottom' value64='3'/>
<enum name='kCFLocaleLanguageDirectionUnknown' value64='0'/>
<enum name='kCFMessagePortBecameInvalidError' value64='-5'/>
<enum name='kCFMessagePortIsInvalid' value64='-3'/>
<enum name='kCFMessagePortReceiveTimeout' value64='-2'/>
<enum name='kCFMessagePortSendTimeout' value64='-1'/>
<enum name='kCFMessagePortSuccess' value64='0'/>
<enum name='kCFMessagePortTransportError' value64='-4'/>
<enum name='kCFNotFound' value64='-1'/>
<enum name='kCFNotificationDeliverImmediately' value64='1'/>
<enum name='kCFNotificationPostToAllSessions' value64='2'/>
<enum name='kCFNumberCFIndexType' value64='14'/>
<enum name='kCFNumberCGFloatType' value64='16'/>
<enum name='kCFNumberCharType' value64='7'/>
<enum name='kCFNumberDoubleType' value64='13'/>
<enum name='kCFNumberFloat32Type' value64='5'/>
<enum name='kCFNumberFloat64Type' value64='6'/>
<enum name='kCFNumberFloatType' value64='12'/>
<enum name='kCFNumberFormatterCurrencyAccountingStyle' value64='10'/>
<enum name='kCFNumberFormatterCurrencyISOCodeStyle' value64='8'/>
<enum name='kCFNumberFormatterCurrencyPluralStyle' value64='9'/>
<enum name='kCFNumberFormatterCurrencyStyle' value64='2'/>
<enum name='kCFNumberFormatterDecimalStyle' value64='1'/>
<enum name='kCFNumberFormatterNoStyle' value64='0'/>
<enum name='kCFNumberFormatterOrdinalStyle' value64='6'/>
<enum name='kCFNumberFormatterPadAfterPrefix' value64='1'/>
<enum name='kCFNumberFormatterPadAfterSuffix' value64='3'/>
<enum name='kCFNumberFormatterPadBeforePrefix' value64='0'/>
<enum name='kCFNumberFormatterPadBeforeSuffix' value64='2'/>
<enum name='kCFNumberFormatterParseIntegersOnly' value64='1'/>
<enum name='kCFNumberFormatterPercentStyle' value64='3'/>
<enum name='kCFNumberFormatterRoundCeiling' value64='0'/>
<enum name='kCFNumberFormatterRoundDown' value64='2'/>
<enum name='kCFNumberFormatterRoundFloor' value64='1'/>
<enum name='kCFNumberFormatterRoundHalfDown' value64='5'/>
<enum name='kCFNumberFormatterRoundHalfEven' value64='4'/>
<enum name='kCFNumberFormatterRoundHalfUp' value64='6'/>
<enum name='kCFNumberFormatterRoundUp' value64='3'/>
<enum name='kCFNumberFormatterScientificStyle' value64='4'/>
<enum name='kCFNumberFormatterSpellOutStyle' value64='5'/>
<enum name='kCFNumberIntType' value64='9'/>
<enum name='kCFNumberLongLongType' value64='11'/>
<enum name='kCFNumberLongType' value64='10'/>
<enum name='kCFNumberMaxType' value64='16'/>
<enum name='kCFNumberNSIntegerType' value64='15'/>
<enum name='kCFNumberSInt16Type' value64='2'/>
<enum name='kCFNumberSInt32Type' value64='3'/>
<enum name='kCFNumberSInt64Type' value64='4'/>
<enum name='kCFNumberSInt8Type' value64='1'/>
<enum name='kCFNumberShortType' value64='8'/>
<enum name='kCFPropertyListBinaryFormat_v1_0' value64='200'/>
<enum name='kCFPropertyListImmutable' value64='0'/>
<enum name='kCFPropertyListMutableContainers' value64='1'/>
<enum name='kCFPropertyListMutableContainersAndLeaves' value64='2'/>
<enum name='kCFPropertyListOpenStepFormat' value64='1'/>
<enum name='kCFPropertyListReadCorruptError' value64='3840'/>
<enum name='kCFPropertyListReadStreamError' value64='3842'/>
<enum name='kCFPropertyListReadUnknownVersionError' value64='3841'/>
<enum name='kCFPropertyListWriteStreamError' value64='3851'/>
<enum name='kCFPropertyListXMLFormat_v1_0' value64='100'/>
<enum name='kCFRunLoopAfterWaiting' value64='64'/>
<enum name='kCFRunLoopAllActivities' value64='268435455'/>
<enum name='kCFRunLoopBeforeSources' value64='4'/>
<enum name='kCFRunLoopBeforeTimers' value64='2'/>
<enum name='kCFRunLoopBeforeWaiting' value64='32'/>
<enum name='kCFRunLoopEntry' value64='1'/>
<enum name='kCFRunLoopExit' value64='128'/>
<enum name='kCFRunLoopRunFinished' value64='1'/>
<enum name='kCFRunLoopRunHandledSource' value64='4'/>
<enum name='kCFRunLoopRunStopped' value64='2'/>
<enum name='kCFRunLoopRunTimedOut' value64='3'/>
<enum name='kCFSocketAcceptCallBack' value64='2'/>
<enum name='kCFSocketAutomaticallyReenableAcceptCallBack' value64='2'/>
<enum name='kCFSocketAutomaticallyReenableDataCallBack' value64='3'/>
<enum name='kCFSocketAutomaticallyReenableReadCallBack' value64='1'/>
<enum name='kCFSocketAutomaticallyReenableWriteCallBack' value64='8'/>
<enum name='kCFSocketCloseOnInvalidate' value64='128'/>
<enum name='kCFSocketConnectCallBack' value64='4'/>
<enum name='kCFSocketDataCallBack' value64='3'/>
<enum name='kCFSocketError' value64='-1'/>
<enum name='kCFSocketLeaveErrors' value64='64'/>
<enum name='kCFSocketNoCallBack' value64='0'/>
<enum name='kCFSocketReadCallBack' value64='1'/>
<enum name='kCFSocketSuccess' value64='0'/>
<enum name='kCFSocketTimeout' value64='-2'/>
<enum name='kCFSocketWriteCallBack' value64='8'/>
<enum name='kCFStreamErrorDomainCustom' value64='-1'/>
<enum name='kCFStreamErrorDomainMacOSStatus' value64='2'/>
<enum name='kCFStreamErrorDomainPOSIX' value64='1'/>
<enum name='kCFStreamEventCanAcceptBytes' value64='4'/>
<enum name='kCFStreamEventEndEncountered' value64='16'/>
<enum name='kCFStreamEventErrorOccurred' value64='8'/>
<enum name='kCFStreamEventHasBytesAvailable' value64='2'/>
<enum name='kCFStreamEventNone' value64='0'/>
<enum name='kCFStreamEventOpenCompleted' value64='1'/>
<enum name='kCFStreamStatusAtEnd' value64='5'/>
<enum name='kCFStreamStatusClosed' value64='6'/>
<enum name='kCFStreamStatusError' value64='7'/>
<enum name='kCFStreamStatusNotOpen' value64='0'/>
<enum name='kCFStreamStatusOpen' value64='2'/>
<enum name='kCFStreamStatusOpening' value64='1'/>
<enum name='kCFStreamStatusReading' value64='3'/>
<enum name='kCFStreamStatusWriting' value64='4'/>
<enum name='kCFStringEncodingANSEL' value64='1537'/>
<enum name='kCFStringEncodingASCII' value64='1536'/>
<enum name='kCFStringEncodingBig5' value64='2563'/>
<enum name='kCFStringEncodingBig5_E' value64='2569'/>
<enum name='kCFStringEncodingBig5_HKSCS_1999' value64='2566'/>
<enum name='kCFStringEncodingCNS_11643_92_P1' value64='1617'/>
<enum name='kCFStringEncodingCNS_11643_92_P2' value64='1618'/>
<enum name='kCFStringEncodingCNS_11643_92_P3' value64='1619'/>
<enum name='kCFStringEncodingDOSArabic' value64='1049'/>
<enum name='kCFStringEncodingDOSBalticRim' value64='1030'/>
<enum name='kCFStringEncodingDOSCanadianFrench' value64='1048'/>
<enum name='kCFStringEncodingDOSChineseSimplif' value64='1057'/>
<enum name='kCFStringEncodingDOSChineseTrad' value64='1059'/>
<enum name='kCFStringEncodingDOSCyrillic' value64='1043'/>
<enum name='kCFStringEncodingDOSGreek' value64='1029'/>
<enum name='kCFStringEncodingDOSGreek1' value64='1041'/>
<enum name='kCFStringEncodingDOSGreek2' value64='1052'/>
<enum name='kCFStringEncodingDOSHebrew' value64='1047'/>
<enum name='kCFStringEncodingDOSIcelandic' value64='1046'/>
<enum name='kCFStringEncodingDOSJapanese' value64='1056'/>
<enum name='kCFStringEncodingDOSKorean' value64='1058'/>
<enum name='kCFStringEncodingDOSLatin1' value64='1040'/>
<enum name='kCFStringEncodingDOSLatin2' value64='1042'/>
<enum name='kCFStringEncodingDOSLatinUS' value64='1024'/>
<enum name='kCFStringEncodingDOSNordic' value64='1050'/>
<enum name='kCFStringEncodingDOSPortuguese' value64='1045'/>
<enum name='kCFStringEncodingDOSRussian' value64='1051'/>
<enum name='kCFStringEncodingDOSThai' value64='1053'/>
<enum name='kCFStringEncodingDOSTurkish' value64='1044'/>
<enum name='kCFStringEncodingEBCDIC_CP037' value64='3074'/>
<enum name='kCFStringEncodingEBCDIC_US' value64='3073'/>
<enum name='kCFStringEncodingEUC_CN' value64='2352'/>
<enum name='kCFStringEncodingEUC_JP' value64='2336'/>
<enum name='kCFStringEncodingEUC_KR' value64='2368'/>
<enum name='kCFStringEncodingEUC_TW' value64='2353'/>
<enum name='kCFStringEncodingGBK_95' value64='1585'/>
<enum name='kCFStringEncodingGB_18030_2000' value64='1586'/>
<enum name='kCFStringEncodingGB_2312_80' value64='1584'/>
<enum name='kCFStringEncodingHZ_GB_2312' value64='2565'/>
<enum name='kCFStringEncodingISOLatin1' value64='513'/>
<enum name='kCFStringEncodingISOLatin10' value64='528'/>
<enum name='kCFStringEncodingISOLatin2' value64='514'/>
<enum name='kCFStringEncodingISOLatin3' value64='515'/>
<enum name='kCFStringEncodingISOLatin4' value64='516'/>
<enum name='kCFStringEncodingISOLatin5' value64='521'/>
<enum name='kCFStringEncodingISOLatin6' value64='522'/>
<enum name='kCFStringEncodingISOLatin7' value64='525'/>
<enum name='kCFStringEncodingISOLatin8' value64='526'/>
<enum name='kCFStringEncodingISOLatin9' value64='527'/>
<enum name='kCFStringEncodingISOLatinArabic' value64='518'/>
<enum name='kCFStringEncodingISOLatinCyrillic' value64='517'/>
<enum name='kCFStringEncodingISOLatinGreek' value64='519'/>
<enum name='kCFStringEncodingISOLatinHebrew' value64='520'/>
<enum name='kCFStringEncodingISOLatinThai' value64='523'/>
<enum name='kCFStringEncodingISO_2022_CN' value64='2096'/>
<enum name='kCFStringEncodingISO_2022_CN_EXT' value64='2097'/>
<enum name='kCFStringEncodingISO_2022_JP' value64='2080'/>
<enum name='kCFStringEncodingISO_2022_JP_1' value64='2082'/>
<enum name='kCFStringEncodingISO_2022_JP_2' value64='2081'/>
<enum name='kCFStringEncodingISO_2022_JP_3' value64='2083'/>
<enum name='kCFStringEncodingISO_2022_KR' value64='2112'/>
<enum name='kCFStringEncodingInvalidId' value64='-1'/>
<enum name='kCFStringEncodingJIS_C6226_78' value64='1572'/>
<enum name='kCFStringEncodingJIS_X0201_76' value64='1568'/>
<enum name='kCFStringEncodingJIS_X0208_83' value64='1569'/>
<enum name='kCFStringEncodingJIS_X0208_90' value64='1570'/>
<enum name='kCFStringEncodingJIS_X0212_90' value64='1571'/>
<enum name='kCFStringEncodingKOI8_R' value64='2562'/>
<enum name='kCFStringEncodingKOI8_U' value64='2568'/>
<enum name='kCFStringEncodingKSC_5601_87' value64='1600'/>
<enum name='kCFStringEncodingKSC_5601_92_Johab' value64='1601'/>
<enum name='kCFStringEncodingMacArabic' value64='4'/>
<enum name='kCFStringEncodingMacArmenian' value64='24'/>
<enum name='kCFStringEncodingMacBengali' value64='13'/>
<enum name='kCFStringEncodingMacBurmese' value64='19'/>
<enum name='kCFStringEncodingMacCeltic' value64='39'/>
<enum name='kCFStringEncodingMacCentralEurRoman' value64='29'/>
<enum name='kCFStringEncodingMacChineseSimp' value64='25'/>
<enum name='kCFStringEncodingMacChineseTrad' value64='2'/>
<enum name='kCFStringEncodingMacCroatian' value64='36'/>
<enum name='kCFStringEncodingMacCyrillic' value64='7'/>
<enum name='kCFStringEncodingMacDevanagari' value64='9'/>
<enum name='kCFStringEncodingMacDingbats' value64='34'/>
<enum name='kCFStringEncodingMacEthiopic' value64='28'/>
<enum name='kCFStringEncodingMacExtArabic' value64='31'/>
<enum name='kCFStringEncodingMacFarsi' value64='140'/>
<enum name='kCFStringEncodingMacGaelic' value64='40'/>
<enum name='kCFStringEncodingMacGeorgian' value64='23'/>
<enum name='kCFStringEncodingMacGreek' value64='6'/>
<enum name='kCFStringEncodingMacGujarati' value64='11'/>
<enum name='kCFStringEncodingMacGurmukhi' value64='10'/>
<enum name='kCFStringEncodingMacHFS' value64='255'/>
<enum name='kCFStringEncodingMacHebrew' value64='5'/>
<enum name='kCFStringEncodingMacIcelandic' value64='37'/>
<enum name='kCFStringEncodingMacInuit' value64='236'/>
<enum name='kCFStringEncodingMacJapanese' value64='1'/>
<enum name='kCFStringEncodingMacKannada' value64='16'/>
<enum name='kCFStringEncodingMacKhmer' value64='20'/>
<enum name='kCFStringEncodingMacKorean' value64='3'/>
<enum name='kCFStringEncodingMacLaotian' value64='22'/>
<enum name='kCFStringEncodingMacMalayalam' value64='17'/>
<enum name='kCFStringEncodingMacMongolian' value64='27'/>
<enum name='kCFStringEncodingMacOriya' value64='12'/>
<enum name='kCFStringEncodingMacRoman' value64='0'/>
<enum name='kCFStringEncodingMacRomanLatin1' value64='2564'/>
<enum name='kCFStringEncodingMacRomanian' value64='38'/>
<enum name='kCFStringEncodingMacSinhalese' value64='18'/>
<enum name='kCFStringEncodingMacSymbol' value64='33'/>
<enum name='kCFStringEncodingMacTamil' value64='14'/>
<enum name='kCFStringEncodingMacTelugu' value64='15'/>
<enum name='kCFStringEncodingMacThai' value64='21'/>
<enum name='kCFStringEncodingMacTibetan' value64='26'/>
<enum name='kCFStringEncodingMacTurkish' value64='35'/>
<enum name='kCFStringEncodingMacUkrainian' value64='152'/>
<enum name='kCFStringEncodingMacVT100' value64='252'/>
<enum name='kCFStringEncodingMacVietnamese' value64='30'/>
<enum name='kCFStringEncodingNextStepJapanese' value64='2818'/>
<enum name='kCFStringEncodingNextStepLatin' value64='2817'/>
<enum name='kCFStringEncodingNonLossyASCII' value64='3071'/>
<enum name='kCFStringEncodingShiftJIS' value64='2561'/>
<enum name='kCFStringEncodingShiftJIS_X0213' value64='1576'/>
<enum name='kCFStringEncodingShiftJIS_X0213_00' value64='1576'/>
<enum name='kCFStringEncodingShiftJIS_X0213_MenKuTen' value64='1577'/>
<enum name='kCFStringEncodingUTF16' value64='256'/>
<enum name='kCFStringEncodingUTF16BE' value64='268435712'/>
<enum name='kCFStringEncodingUTF16LE' value64='335544576'/>
<enum name='kCFStringEncodingUTF32' value64='201326848'/>
<enum name='kCFStringEncodingUTF32BE' value64='402653440'/>
<enum name='kCFStringEncodingUTF32LE' value64='469762304'/>
<enum name='kCFStringEncodingUTF7' value64='67109120'/>
<enum name='kCFStringEncodingUTF7_IMAP' value64='2576'/>
<enum name='kCFStringEncodingUTF8' value64='134217984'/>
<enum name='kCFStringEncodingUnicode' value64='256'/>
<enum name='kCFStringEncodingVISCII' value64='2567'/>
<enum name='kCFStringEncodingWindowsArabic' value64='1286'/>
<enum name='kCFStringEncodingWindowsBalticRim' value64='1287'/>
<enum name='kCFStringEncodingWindowsCyrillic' value64='1282'/>
<enum name='kCFStringEncodingWindowsGreek' value64='1283'/>
<enum name='kCFStringEncodingWindowsHebrew' value64='1285'/>
<enum name='kCFStringEncodingWindowsKoreanJohab' value64='1296'/>
<enum name='kCFStringEncodingWindowsLatin1' value64='1280'/>
<enum name='kCFStringEncodingWindowsLatin2' value64='1281'/>
<enum name='kCFStringEncodingWindowsLatin5' value64='1284'/>
<enum name='kCFStringEncodingWindowsVietnamese' value64='1288'/>
<enum name='kCFStringNormalizationFormC' value64='2'/>
<enum name='kCFStringNormalizationFormD' value64='0'/>
<enum name='kCFStringNormalizationFormKC' value64='3'/>
<enum name='kCFStringNormalizationFormKD' value64='1'/>
<enum name='kCFStringTokenizerAttributeLanguage' value64='131072'/>
<enum name='kCFStringTokenizerAttributeLatinTranscription' value64='65536'/>
<enum name='kCFStringTokenizerTokenHasDerivedSubTokensMask' value64='4'/>
<enum name='kCFStringTokenizerTokenHasHasNumbersMask' value64='8'/>
<enum name='kCFStringTokenizerTokenHasNonLettersMask' value64='16'/>
<enum name='kCFStringTokenizerTokenHasSubTokensMask' value64='2'/>
<enum name='kCFStringTokenizerTokenIsCJWordMask' value64='32'/>
<enum name='kCFStringTokenizerTokenNone' value64='0'/>
<enum name='kCFStringTokenizerTokenNormal' value64='1'/>
<enum name='kCFStringTokenizerUnitLineBreak' value64='3'/>
<enum name='kCFStringTokenizerUnitParagraph' value64='2'/>
<enum name='kCFStringTokenizerUnitSentence' value64='1'/>
<enum name='kCFStringTokenizerUnitWord' value64='0'/>
<enum name='kCFStringTokenizerUnitWordBoundary' value64='4'/>
<enum name='kCFTimeZoneNameStyleDaylightSaving' value64='2'/>
<enum name='kCFTimeZoneNameStyleGeneric' value64='4'/>
<enum name='kCFTimeZoneNameStyleShortDaylightSaving' value64='3'/>
<enum name='kCFTimeZoneNameStyleShortGeneric' value64='5'/>
<enum name='kCFTimeZoneNameStyleShortStandard' value64='1'/>
<enum name='kCFTimeZoneNameStyleStandard' value64='0'/>
<enum name='kCFURLBookmarkCreationMinimalBookmarkMask' value64='512'/>
<enum name='kCFURLBookmarkCreationPreferFileIDResolutionMask' value64='256'/>
<enum name='kCFURLBookmarkCreationSecurityScopeAllowOnlyReadAccess' value64='4096'/>
<enum name='kCFURLBookmarkCreationSuitableForBookmarkFile' value64='1024'/>
<enum name='kCFURLBookmarkCreationWithSecurityScope' value64='2048'/>
<enum name='kCFURLBookmarkResolutionWithSecurityScope' value64='1024'/>
<enum name='kCFURLBookmarkResolutionWithoutMountingMask' value64='512'/>
<enum name='kCFURLBookmarkResolutionWithoutUIMask' value64='256'/>
<enum name='kCFURLComponentFragment' value64='12'/>
<enum name='kCFURLComponentHost' value64='8'/>
<enum name='kCFURLComponentNetLocation' value64='2'/>
<enum name='kCFURLComponentParameterString' value64='10'/>
<enum name='kCFURLComponentPassword' value64='6'/>
<enum name='kCFURLComponentPath' value64='3'/>
<enum name='kCFURLComponentPort' value64='9'/>
<enum name='kCFURLComponentQuery' value64='11'/>
<enum name='kCFURLComponentResourceSpecifier' value64='4'/>
<enum name='kCFURLComponentScheme' value64='1'/>
<enum name='kCFURLComponentUser' value64='5'/>
<enum name='kCFURLComponentUserInfo' value64='7'/>
<enum name='kCFURLEnumeratorDefaultBehavior' value64='0'/>
<enum name='kCFURLEnumeratorDescendRecursively' value64='1'/>
<enum name='kCFURLEnumeratorDirectoryPostOrderSuccess' value64='4'/>
<enum name='kCFURLEnumeratorEnd' value64='2'/>
<enum name='kCFURLEnumeratorError' value64='3'/>
<enum name='kCFURLEnumeratorGenerateFileReferenceURLs' value64='4'/>
<enum name='kCFURLEnumeratorGenerateRelativePathURLs' value64='64'/>
<enum name='kCFURLEnumeratorIncludeDirectoriesPostOrder' value64='32'/>
<enum name='kCFURLEnumeratorIncludeDirectoriesPreOrder' value64='16'/>
<enum name='kCFURLEnumeratorSkipInvisibles' value64='2'/>
<enum name='kCFURLEnumeratorSkipPackageContents' value64='8'/>
<enum name='kCFURLEnumeratorSuccess' value64='1'/>
<enum name='kCFURLHFSPathStyle' value64='1'/>
<enum name='kCFURLImproperArgumentsError' value64='-15'/>
<enum name='kCFURLPOSIXPathStyle' value64='0'/>
<enum name='kCFURLPropertyKeyUnavailableError' value64='-17'/>
<enum name='kCFURLRemoteHostUnavailableError' value64='-14'/>
<enum name='kCFURLResourceAccessViolationError' value64='-13'/>
<enum name='kCFURLResourceNotFoundError' value64='-12'/>
<enum name='kCFURLTimeoutError' value64='-18'/>
<enum name='kCFURLUnknownError' value64='-10'/>
<enum name='kCFURLUnknownPropertyKeyError' value64='-16'/>
<enum name='kCFURLUnknownSchemeError' value64='-11'/>
<enum name='kCFURLWindowsPathStyle' value64='2'/>
<enum name='kCFUserNotificationAlternateResponse' value64='1'/>
<enum name='kCFUserNotificationCancelResponse' value64='3'/>
<enum name='kCFUserNotificationCautionAlertLevel' value64='2'/>
<enum name='kCFUserNotificationDefaultResponse' value64='0'/>
<enum name='kCFUserNotificationNoDefaultButtonFlag' value64='32'/>
<enum name='kCFUserNotificationNoteAlertLevel' value64='1'/>
<enum name='kCFUserNotificationOtherResponse' value64='2'/>
<enum name='kCFUserNotificationPlainAlertLevel' value64='3'/>
<enum name='kCFUserNotificationStopAlertLevel' value64='0'/>
<enum name='kCFUserNotificationUseRadioButtonsFlag' value64='64'/>
<enum name='kCFXMLEntityTypeCharacter' value64='4'/>
<enum name='kCFXMLEntityTypeParameter' value64='0'/>
<enum name='kCFXMLEntityTypeParsedExternal' value64='2'/>
<enum name='kCFXMLEntityTypeParsedInternal' value64='1'/>
<enum name='kCFXMLEntityTypeUnparsed' value64='3'/>
<enum name='kCFXMLErrorElementlessDocument' value64='11'/>
<enum name='kCFXMLErrorEncodingConversionFailure' value64='3'/>
<enum name='kCFXMLErrorMalformedCDSect' value64='7'/>
<enum name='kCFXMLErrorMalformedCharacterReference' value64='13'/>
<enum name='kCFXMLErrorMalformedCloseTag' value64='8'/>
<enum name='kCFXMLErrorMalformedComment' value64='12'/>
<enum name='kCFXMLErrorMalformedDTD' value64='5'/>
<enum name='kCFXMLErrorMalformedDocument' value64='10'/>
<enum name='kCFXMLErrorMalformedName' value64='6'/>
<enum name='kCFXMLErrorMalformedParsedCharacterData' value64='14'/>
<enum name='kCFXMLErrorMalformedProcessingInstruction' value64='4'/>
<enum name='kCFXMLErrorMalformedStartTag' value64='9'/>
<enum name='kCFXMLErrorNoData' value64='15'/>
<enum name='kCFXMLErrorUnexpectedEOF' value64='1'/>
<enum name='kCFXMLErrorUnknownEncoding' value64='2'/>
<enum name='kCFXMLNodeCurrentVersion' value64='1'/>
<enum name='kCFXMLNodeTypeAttribute' value64='3'/>
<enum name='kCFXMLNodeTypeAttributeListDeclaration' value64='15'/>
<enum name='kCFXMLNodeTypeCDATASection' value64='7'/>
<enum name='kCFXMLNodeTypeComment' value64='5'/>
<enum name='kCFXMLNodeTypeDocument' value64='1'/>
<enum name='kCFXMLNodeTypeDocumentFragment' value64='8'/>
<enum name='kCFXMLNodeTypeDocumentType' value64='11'/>
<enum name='kCFXMLNodeTypeElement' value64='2'/>
<enum name='kCFXMLNodeTypeElementTypeDeclaration' value64='14'/>
<enum name='kCFXMLNodeTypeEntity' value64='9'/>
<enum name='kCFXMLNodeTypeEntityReference' value64='10'/>
<enum name='kCFXMLNodeTypeNotation' value64='13'/>
<enum name='kCFXMLNodeTypeProcessingInstruction' value64='4'/>
<enum name='kCFXMLNodeTypeText' value64='6'/>
<enum name='kCFXMLNodeTypeWhitespace' value64='12'/>
<enum name='kCFXMLParserAddImpliedAttributes' value64='32'/>
<enum name='kCFXMLParserAllOptions' value64='16777215'/>
<enum name='kCFXMLParserNoOptions' value64='0'/>
<enum name='kCFXMLParserReplacePhysicalEntities' value64='4'/>
<enum name='kCFXMLParserResolveExternalEntities' value64='16'/>
<enum name='kCFXMLParserSkipMetaData' value64='2'/>
<enum name='kCFXMLParserSkipWhitespace' value64='8'/>
<enum name='kCFXMLParserValidateDocument' value64='1'/>
<enum name='kCFXMLStatusParseInProgress' value64='-1'/>
<enum name='kCFXMLStatusParseNotBegun' value64='-2'/>
<enum name='kCFXMLStatusParseSuccessful' value64='0'/>
<function name='CFAbsoluteTimeAddGregorianUnits'>
<arg type64='d'/>
<arg type64='^{__CFTimeZone=}'/>
<arg type64='{_CFGregorianUnits=iiiiid}'/>
<retval type64='d'/>
</function>
<function name='CFAbsoluteTimeGetCurrent'>
<retval type64='d'/>
</function>
<function name='CFAbsoluteTimeGetDayOfWeek'>
<arg type64='d'/>
<arg type64='^{__CFTimeZone=}'/>
<retval type64='i'/>
</function>
<function name='CFAbsoluteTimeGetDayOfYear'>
<arg type64='d'/>
<arg type64='^{__CFTimeZone=}'/>
<retval type64='i'/>
</function>
<function name='CFAbsoluteTimeGetDifferenceAsGregorianUnits'>
<arg type64='d'/>
<arg type64='d'/>
<arg type64='^{__CFTimeZone=}'/>
<arg type64='Q'/>
<retval type64='{_CFGregorianUnits=iiiiid}'/>
</function>
<function name='CFAbsoluteTimeGetGregorianDate'>
<arg type64='d'/>
<arg type64='^{__CFTimeZone=}'/>
<retval type64='{_CFGregorianDate=iccccd}'/>
</function>
<function name='CFAbsoluteTimeGetWeekOfYear'>
<arg type64='d'/>
<arg type64='^{__CFTimeZone=}'/>
<retval type64='i'/>
</function>
<function name='CFAllocatorAllocate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='Q'/>
<retval type64='^v'/>
</function>
<function name='CFAllocatorCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{_CFAllocatorContext=q^v^?^?^?^?^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFAllocator=}'/>
</function>
<function name='CFAllocatorDeallocate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFAllocatorGetContext'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{_CFAllocatorContext=q^v^?^?^?^?^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFAllocatorGetDefault'>
<retval type64='^{__CFAllocator=}'/>
</function>
<function name='CFAllocatorGetPreferredSizeForSize'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='Q'/>
<retval type64='q'/>
</function>
<function name='CFAllocatorGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFAllocatorReallocate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='q'/>
<arg type64='Q'/>
<retval type64='^v'/>
</function>
<function name='CFAllocatorSetDefault'>
<arg type64='^{__CFAllocator=}'/>
<retval type64='v'/>
</function>
<function name='CFArrayAppendArray'>
<arg type64='^{__CFArray=}'/>
<arg type64='^{__CFArray=}'/>
<arg type64='{_CFRange=qq}'/>
<retval type64='v'/>
</function>
<function name='CFArrayAppendValue'>
<arg type64='^{__CFArray=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFArrayApplyFunction'>
<arg type64='^{__CFArray=}'/>
<arg type64='{_CFRange=qq}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFArrayBSearchValues'>
<arg type64='^{__CFArray=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^v' type_modifier='n'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^v'/>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='q'/>
</arg>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFArrayContainsValue'>
<arg type64='^{__CFArray=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='B'/>
</function>
<function name='CFArrayCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_of_fixed_length='2' type64='^^v' type_modifier='n'/>
<arg type64='q'/>
<arg type64='^{_CFArrayCallBacks=q^?^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFArrayCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFArray=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFArrayCreateMutable'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{_CFArrayCallBacks=q^?^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFArrayCreateMutableCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{__CFArray=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFArrayExchangeValuesAtIndices'>
<arg type64='^{__CFArray=}'/>
<arg type64='q'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFArrayGetCount'>
<arg type64='^{__CFArray=}'/>
<retval type64='q'/>
</function>
<function name='CFArrayGetCountOfValue'>
<arg type64='^{__CFArray=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFArrayGetFirstIndexOfValue'>
<arg type64='^{__CFArray=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFArrayGetLastIndexOfValue'>
<arg type64='^{__CFArray=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFArrayGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFArrayGetValueAtIndex'>
<arg type64='^{__CFArray=}'/>
<arg type64='q'/>
<retval type64='^v'/>
</function>
<function name='CFArrayGetValues'>
<arg type64='^{__CFArray=}'/>
<arg type64='{_CFRange=qq}'/>
<arg c_array_of_variable_length='true' type64='^^v' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFArrayInsertValueAtIndex'>
<arg type64='^{__CFArray=}'/>
<arg type64='q'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFArrayRemoveAllValues'>
<arg type64='^{__CFArray=}'/>
<retval type64='v'/>
</function>
<function name='CFArrayRemoveValueAtIndex'>
<arg type64='^{__CFArray=}'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFArrayReplaceValues'>
<arg type64='^{__CFArray=}'/>
<arg type64='{_CFRange=qq}'/>
<arg c_array_length_in_arg='3' type64='^^v' type_modifier='n'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFArraySetValueAtIndex'>
<arg type64='^{__CFArray=}'/>
<arg type64='q'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFArraySortValues'>
<arg type64='^{__CFArray=}'/>
<arg type64='{_CFRange=qq}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^v'/>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='q'/>
</arg>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFAttributedStringBeginEditing'>
<arg type64='^{__CFAttributedString=}'/>
<retval type64='v'/>
</function>
<function name='CFAttributedStringCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFDictionary=}'/>
<retval already_retained='true' type64='^{__CFAttributedString=}'/>
</function>
<function name='CFAttributedStringCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFAttributedString=}'/>
<retval already_retained='true' type64='^{__CFAttributedString=}'/>
</function>
<function name='CFAttributedStringCreateMutable'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFAttributedString=}'/>
</function>
<function name='CFAttributedStringCreateMutableCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{__CFAttributedString=}'/>
<retval already_retained='true' type64='^{__CFAttributedString=}'/>
</function>
<function name='CFAttributedStringCreateWithSubstring'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFAttributedString=}'/>
<arg type64='{_CFRange=qq}'/>
<retval already_retained='true' type64='^{__CFAttributedString=}'/>
</function>
<function name='CFAttributedStringEndEditing'>
<arg type64='^{__CFAttributedString=}'/>
<retval type64='v'/>
</function>
<function name='CFAttributedStringGetAttribute'>
<arg type64='^{__CFAttributedString=}'/>
<arg type64='q'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<retval type64='@'/>
</function>
<function name='CFAttributedStringGetAttributeAndLongestEffectiveRange'>
<arg type64='^{__CFAttributedString=}'/>
<arg type64='q'/>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<retval type64='@'/>
</function>
<function name='CFAttributedStringGetAttributes'>
<arg type64='^{__CFAttributedString=}'/>
<arg type64='q'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<retval type64='^{__CFDictionary=}'/>
</function>
<function name='CFAttributedStringGetAttributesAndLongestEffectiveRange'>
<arg type64='^{__CFAttributedString=}'/>
<arg type64='q'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<retval type64='^{__CFDictionary=}'/>
</function>
<function name='CFAttributedStringGetLength'>
<arg type64='^{__CFAttributedString=}'/>
<retval type64='q'/>
</function>
<function name='CFAttributedStringGetMutableString'>
<arg type64='^{__CFAttributedString=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFAttributedStringGetString'>
<arg type64='^{__CFAttributedString=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFAttributedStringGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFAttributedStringRemoveAttribute'>
<arg type64='^{__CFAttributedString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFAttributedStringReplaceAttributedString'>
<arg type64='^{__CFAttributedString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^{__CFAttributedString=}'/>
<retval type64='v'/>
</function>
<function name='CFAttributedStringReplaceString'>
<arg type64='^{__CFAttributedString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFAttributedStringSetAttribute'>
<arg type64='^{__CFAttributedString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^{__CFString=}'/>
<arg type64='@'/>
<retval type64='v'/>
</function>
<function name='CFAttributedStringSetAttributes'>
<arg type64='^{__CFAttributedString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^{__CFDictionary=}'/>
<arg type64='B'/>
<retval type64='v'/>
</function>
<function name='CFAutorelease'>
<arg type64='@'/>
<retval type64='@'/>
</function>
<function name='CFBagAddValue'>
<arg type64='^{__CFBag=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFBagApplyFunction'>
<arg type64='^{__CFBag=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFBagContainsValue'>
<arg type64='^{__CFBag=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='B'/>
</function>
<function name='CFBagCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_of_fixed_length='2' type64='^^v' type_modifier='n'/>
<arg type64='q'/>
<arg type64='^{_CFBagCallBacks=q^?^?^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFBag=}'/>
</function>
<function name='CFBagCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFBag=}'/>
<retval already_retained='true' type64='^{__CFBag=}'/>
</function>
<function name='CFBagCreateMutable'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{_CFBagCallBacks=q^?^?^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFBag=}'/>
</function>
<function name='CFBagCreateMutableCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{__CFBag=}'/>
<retval already_retained='true' type64='^{__CFBag=}'/>
</function>
<function name='CFBagGetCount'>
<arg type64='^{__CFBag=}'/>
<retval type64='q'/>
</function>
<function name='CFBagGetCountOfValue'>
<arg type64='^{__CFBag=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFBagGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFBagGetValue'>
<arg type64='^{__CFBag=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='^v'/>
</function>
<function name='CFBagGetValueIfPresent'>
<arg type64='^{__CFBag=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='^^v' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFBagGetValues'>
<arg type64='^{__CFBag=}'/>
<arg c_array_of_variable_length='true' type64='^^v' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFBagRemoveAllValues'>
<arg type64='^{__CFBag=}'/>
<retval type64='v'/>
</function>
<function name='CFBagRemoveValue'>
<arg type64='^{__CFBag=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFBagReplaceValue'>
<arg type64='^{__CFBag=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFBagSetValue'>
<arg type64='^{__CFBag=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFBinaryHeapAddValue'>
<arg type64='^{__CFBinaryHeap=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFBinaryHeapApplyFunction'>
<arg type64='^{__CFBinaryHeap=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFBinaryHeapContainsValue'>
<arg type64='^{__CFBinaryHeap=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='B'/>
</function>
<function name='CFBinaryHeapCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{_CFBinaryHeapCallBacks=q^?^?^?^?}' type_modifier='n'/>
<arg type64='^{_CFBinaryHeapCompareContext=q^v^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFBinaryHeap=}'/>
</function>
<function name='CFBinaryHeapCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{__CFBinaryHeap=}'/>
<retval already_retained='true' type64='^{__CFBinaryHeap=}'/>
</function>
<function name='CFBinaryHeapGetCount'>
<arg type64='^{__CFBinaryHeap=}'/>
<retval type64='q'/>
</function>
<function name='CFBinaryHeapGetCountOfValue'>
<arg type64='^{__CFBinaryHeap=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFBinaryHeapGetMinimum'>
<arg type64='^{__CFBinaryHeap=}'/>
<retval type64='^v'/>
</function>
<function name='CFBinaryHeapGetMinimumIfPresent'>
<arg type64='^{__CFBinaryHeap=}'/>
<arg type64='^^v' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFBinaryHeapGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFBinaryHeapGetValues'>
<arg type64='^{__CFBinaryHeap=}'/>
<arg c_array_of_variable_length='true' type64='^^v' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFBinaryHeapRemoveAllValues'>
<arg type64='^{__CFBinaryHeap=}'/>
<retval type64='v'/>
</function>
<function name='CFBinaryHeapRemoveMinimumValue'>
<arg type64='^{__CFBinaryHeap=}'/>
<retval type64='v'/>
</function>
<function name='CFBitVectorContainsBit'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='I'/>
<retval type64='B'/>
</function>
<function name='CFBitVectorCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_of_fixed_length='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFBitVector=}'/>
</function>
<function name='CFBitVectorCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFBitVector=}'/>
<retval already_retained='true' type64='^{__CFBitVector=}'/>
</function>
<function name='CFBitVectorCreateMutable'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFBitVector=}'/>
</function>
<function name='CFBitVectorCreateMutableCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{__CFBitVector=}'/>
<retval already_retained='true' type64='^{__CFBitVector=}'/>
</function>
<function name='CFBitVectorFlipBitAtIndex'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFBitVectorFlipBits'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='{_CFRange=qq}'/>
<retval type64='v'/>
</function>
<function name='CFBitVectorGetBitAtIndex'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='q'/>
<retval type64='I'/>
</function>
<function name='CFBitVectorGetBits'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='{_CFRange=qq}'/>
<arg c_array_of_variable_length='true' type64='*' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFBitVectorGetCount'>
<arg type64='^{__CFBitVector=}'/>
<retval type64='q'/>
</function>
<function name='CFBitVectorGetCountOfBit'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='I'/>
<retval type64='q'/>
</function>
<function name='CFBitVectorGetFirstIndexOfBit'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='I'/>
<retval type64='q'/>
</function>
<function name='CFBitVectorGetLastIndexOfBit'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='I'/>
<retval type64='q'/>
</function>
<function name='CFBitVectorGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFBitVectorSetAllBits'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='I'/>
<retval type64='v'/>
</function>
<function name='CFBitVectorSetBitAtIndex'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='q'/>
<arg type64='I'/>
<retval type64='v'/>
</function>
<function name='CFBitVectorSetBits'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='I'/>
<retval type64='v'/>
</function>
<function name='CFBitVectorSetCount'>
<arg type64='^{__CFBitVector=}'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFBooleanGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFBooleanGetValue'>
<arg type64='^{__CFBoolean=}'/>
<retval type64='B'/>
</function>
<function name='CFBundleCloseBundleResourceMap'>
<arg type64='^{__CFBundle=}'/>
<arg type64='i'/>
<retval type64='v'/>
</function>
<function name='CFBundleCopyAuxiliaryExecutableURL'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopyBuiltInPlugInsURL'>
<arg type64='^{__CFBundle=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopyBundleLocalizations'>
<arg type64='^{__CFBundle=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFBundleCopyBundleURL'>
<arg type64='^{__CFBundle=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopyExecutableArchitectures'>
<arg type64='^{__CFBundle=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFBundleCopyExecutableArchitecturesForURL'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFBundleCopyExecutableURL'>
<arg type64='^{__CFBundle=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopyInfoDictionaryForURL'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFBundleCopyInfoDictionaryInDirectory'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFBundleCopyLocalizationsForPreferences'>
<arg type64='^{__CFArray=}'/>
<arg type64='^{__CFArray=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFBundleCopyLocalizationsForURL'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFBundleCopyLocalizedString'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFBundleCopyPreferredLocalizationsFromArray'>
<arg type64='^{__CFArray=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFBundleCopyPrivateFrameworksURL'>
<arg type64='^{__CFBundle=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopyResourceURL'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopyResourceURLForLocalization'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopyResourceURLInDirectory'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopyResourceURLsOfType'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFBundleCopyResourceURLsOfTypeForLocalization'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFBundleCopyResourceURLsOfTypeInDirectory'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFBundleCopyResourcesDirectoryURL'>
<arg type64='^{__CFBundle=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopySharedFrameworksURL'>
<arg type64='^{__CFBundle=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopySharedSupportURL'>
<arg type64='^{__CFBundle=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCopySupportFilesDirectoryURL'>
<arg type64='^{__CFBundle=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFBundleCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFBundle=}'/>
</function>
<function name='CFBundleCreateBundlesFromDirectory'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFBundleGetAllBundles'>
<retval type64='^{__CFArray=}'/>
</function>
<function name='CFBundleGetBundleWithIdentifier'>
<arg type64='^{__CFString=}'/>
<retval type64='^{__CFBundle=}'/>
</function>
<function name='CFBundleGetDataPointerForName'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='^v'/>
</function>
<function name='CFBundleGetDataPointersForNames'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFArray=}'/>
<arg c_array_of_variable_length='true' type64='^^v' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFBundleGetDevelopmentRegion'>
<arg type64='^{__CFBundle=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFBundleGetFunctionPointerForName'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='^v'/>
</function>
<function name='CFBundleGetFunctionPointersForNames'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFArray=}'/>
<arg c_array_of_variable_length='true' type64='^^v' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFBundleGetIdentifier'>
<arg type64='^{__CFBundle=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFBundleGetInfoDictionary'>
<arg type64='^{__CFBundle=}'/>
<retval type64='^{__CFDictionary=}'/>
</function>
<function name='CFBundleGetLocalInfoDictionary'>
<arg type64='^{__CFBundle=}'/>
<retval type64='^{__CFDictionary=}'/>
</function>
<function name='CFBundleGetMainBundle'>
<retval type64='^{__CFBundle=}'/>
</function>
<function name='CFBundleGetPackageInfo'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^I' type_modifier='o'/>
<arg type64='^I' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFBundleGetPackageInfoInDirectory'>
<arg type64='^{__CFURL=}'/>
<arg type64='^I' type_modifier='o'/>
<arg type64='^I' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFBundleGetPlugIn'>
<arg type64='^{__CFBundle=}'/>
<retval type64='^{__CFBundle=}'/>
</function>
<function name='CFBundleGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFBundleGetValueForInfoDictionaryKey'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='@'/>
</function>
<function name='CFBundleGetVersionNumber'>
<arg type64='^{__CFBundle=}'/>
<retval type64='I'/>
</function>
<function name='CFBundleIsArchitectureLoadable'>
<arg type64='i'/>
<retval type64='B'/>
</function>
<function name='CFBundleIsExecutableLoadable'>
<arg type64='^{__CFBundle=}'/>
<retval type64='B'/>
</function>
<function name='CFBundleIsExecutableLoadableForURL'>
<arg type64='^{__CFURL=}'/>
<retval type64='B'/>
</function>
<function name='CFBundleIsExecutableLoaded'>
<arg type64='^{__CFBundle=}'/>
<retval type64='B'/>
</function>
<function name='CFBundleLoadExecutable'>
<arg type64='^{__CFBundle=}'/>
<retval type64='B'/>
</function>
<function name='CFBundleLoadExecutableAndReturnError'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^^{__CFError}' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFBundleOpenBundleResourceFiles'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^i' type_modifier='o'/>
<arg type64='^i' type_modifier='o'/>
<retval type64='i'/>
</function>
<function name='CFBundleOpenBundleResourceMap'>
<arg type64='^{__CFBundle=}'/>
<retval type64='i'/>
</function>
<function name='CFBundlePreflightExecutable'>
<arg type64='^{__CFBundle=}'/>
<arg type64='^^{__CFError}' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFBundleUnloadExecutable'>
<arg type64='^{__CFBundle=}'/>
<retval type64='v'/>
</function>
<function inline='true' name='CFByteOrderGetCurrent'>
<retval type64='q'/>
</function>
<function name='CFCalendarAddComponents' variadic='true'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='^d' type_modifier='N'/>
<arg type64='Q'/>
<arg type64='*'/>
<retval type64='B'/>
</function>
<function name='CFCalendarComposeAbsoluteTime' variadic='true'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='^d' type_modifier='o'/>
<arg type64='*'/>
<retval type64='B'/>
</function>
<function name='CFCalendarCopyCurrent'>
<retval already_retained='true' type64='^{__CFCalendar=}'/>
</function>
<function name='CFCalendarCopyLocale'>
<arg type64='^{__CFCalendar=}'/>
<retval already_retained='true' type64='^{__CFLocale=}'/>
</function>
<function name='CFCalendarCopyTimeZone'>
<arg type64='^{__CFCalendar=}'/>
<retval already_retained='true' type64='^{__CFTimeZone=}'/>
</function>
<function name='CFCalendarCreateWithIdentifier'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFCalendar=}'/>
</function>
<function name='CFCalendarDecomposeAbsoluteTime' variadic='true'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='d'/>
<arg type64='*'/>
<retval type64='B'/>
</function>
<function name='CFCalendarGetComponentDifference' variadic='true'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='d'/>
<arg type64='d'/>
<arg type64='Q'/>
<arg type64='*'/>
<retval type64='B'/>
</function>
<function name='CFCalendarGetFirstWeekday'>
<arg type64='^{__CFCalendar=}'/>
<retval type64='q'/>
</function>
<function name='CFCalendarGetIdentifier'>
<arg type64='^{__CFCalendar=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFCalendarGetMaximumRangeOfUnit'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='Q'/>
<retval type64='{_CFRange=qq}'/>
</function>
<function name='CFCalendarGetMinimumDaysInFirstWeek'>
<arg type64='^{__CFCalendar=}'/>
<retval type64='q'/>
</function>
<function name='CFCalendarGetMinimumRangeOfUnit'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='Q'/>
<retval type64='{_CFRange=qq}'/>
</function>
<function name='CFCalendarGetOrdinalityOfUnit'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='Q'/>
<arg type64='Q'/>
<arg type64='d'/>
<retval type64='q'/>
</function>
<function name='CFCalendarGetRangeOfUnit'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='Q'/>
<arg type64='Q'/>
<arg type64='d'/>
<retval type64='{_CFRange=qq}'/>
</function>
<function name='CFCalendarGetTimeRangeOfUnit'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='Q'/>
<arg type64='d'/>
<arg type64='^d' type_modifier='o'/>
<arg type64='^d' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFCalendarGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFCalendarSetFirstWeekday'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFCalendarSetLocale'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='^{__CFLocale=}'/>
<retval type64='v'/>
</function>
<function name='CFCalendarSetMinimumDaysInFirstWeek'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFCalendarSetTimeZone'>
<arg type64='^{__CFCalendar=}'/>
<arg type64='^{__CFTimeZone=}'/>
<retval type64='v'/>
</function>
<function name='CFCharacterSetAddCharactersInRange'>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='{_CFRange=qq}'/>
<retval type64='v'/>
</function>
<function name='CFCharacterSetAddCharactersInString'>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFCharacterSetCreateBitmapRepresentation'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFCharacterSet=}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFCharacterSetCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFCharacterSet=}'/>
<retval already_retained='true' type64='^{__CFCharacterSet=}'/>
</function>
<function name='CFCharacterSetCreateInvertedSet'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFCharacterSet=}'/>
<retval already_retained='true' type64='^{__CFCharacterSet=}'/>
</function>
<function name='CFCharacterSetCreateMutable'>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFCharacterSet=}'/>
</function>
<function name='CFCharacterSetCreateMutableCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFCharacterSet=}'/>
<retval already_retained='true' type64='^{__CFCharacterSet=}'/>
</function>
<function name='CFCharacterSetCreateWithBitmapRepresentation'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFData=}'/>
<retval already_retained='true' type64='^{__CFCharacterSet=}'/>
</function>
<function name='CFCharacterSetCreateWithCharactersInRange'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='{_CFRange=qq}'/>
<retval already_retained='true' type64='^{__CFCharacterSet=}'/>
</function>
<function name='CFCharacterSetCreateWithCharactersInString'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFCharacterSet=}'/>
</function>
<function name='CFCharacterSetGetPredefined'>
<arg type64='q'/>
<retval type64='^{__CFCharacterSet=}'/>
</function>
<function name='CFCharacterSetGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFCharacterSetHasMemberInPlane'>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='q'/>
<retval type64='B'/>
</function>
<function name='CFCharacterSetIntersect'>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='^{__CFCharacterSet=}'/>
<retval type64='v'/>
</function>
<function name='CFCharacterSetInvert'>
<arg type64='^{__CFCharacterSet=}'/>
<retval type64='v'/>
</function>
<function name='CFCharacterSetIsCharacterMember'>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='S'/>
<retval type64='B'/>
</function>
<function name='CFCharacterSetIsLongCharacterMember'>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='I'/>
<retval type64='B'/>
</function>
<function name='CFCharacterSetIsSupersetOfSet'>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='^{__CFCharacterSet=}'/>
<retval type64='B'/>
</function>
<function name='CFCharacterSetRemoveCharactersInRange'>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='{_CFRange=qq}'/>
<retval type64='v'/>
</function>
<function name='CFCharacterSetRemoveCharactersInString'>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFCharacterSetUnion'>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='^{__CFCharacterSet=}'/>
<retval type64='v'/>
</function>
<function inline='true' name='CFConvertDoubleHostToSwapped'>
<arg type64='d'/>
<retval type64='{_CFSwappedFloat64=Q}'/>
</function>
<function inline='true' name='CFConvertDoubleSwappedToHost'>
<arg type64='{_CFSwappedFloat64=Q}'/>
<retval type64='d'/>
</function>
<function inline='true' name='CFConvertFloat32HostToSwapped'>
<arg type64='f'/>
<retval type64='{_CFSwappedFloat32=I}'/>
</function>
<function inline='true' name='CFConvertFloat32SwappedToHost'>
<arg type64='{_CFSwappedFloat32=I}'/>
<retval type64='f'/>
</function>
<function inline='true' name='CFConvertFloat64HostToSwapped'>
<arg type64='d'/>
<retval type64='{_CFSwappedFloat64=Q}'/>
</function>
<function inline='true' name='CFConvertFloat64SwappedToHost'>
<arg type64='{_CFSwappedFloat64=Q}'/>
<retval type64='d'/>
</function>
<function inline='true' name='CFConvertFloatHostToSwapped'>
<arg type64='f'/>
<retval type64='{_CFSwappedFloat32=I}'/>
</function>
<function inline='true' name='CFConvertFloatSwappedToHost'>
<arg type64='{_CFSwappedFloat32=I}'/>
<retval type64='f'/>
</function>
<function name='CFCopyDescription'>
<arg type64='@'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFCopyHomeDirectoryURL'>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFCopyTypeIDDescription'>
<arg type64='Q'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFDataAppendBytes'>
<arg type64='^{__CFData=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFDataCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFDataCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFData=}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFDataCreateMutable'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFDataCreateMutableCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{__CFData=}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFDataCreateWithBytesNoCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFDataDeleteBytes'>
<arg type64='^{__CFData=}'/>
<arg type64='{_CFRange=qq}'/>
<retval type64='v'/>
</function>
<function name='CFDataFind'>
<arg type64='^{__CFData=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='Q'/>
<retval type64='{_CFRange=qq}'/>
</function>
<function name='CFDataGetBytePtr'>
<arg type64='^{__CFData=}'/>
<retval type64='*'/>
</function>
<function name='CFDataGetBytes'>
<arg type64='^{__CFData=}'/>
<arg type64='{_CFRange=qq}'/>
<arg c_array_of_variable_length='true' type64='*' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFDataGetLength'>
<arg type64='^{__CFData=}'/>
<retval type64='q'/>
</function>
<function name='CFDataGetMutableBytePtr'>
<arg type64='^{__CFData=}'/>
<retval type64='*'/>
</function>
<function name='CFDataGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFDataIncreaseLength'>
<arg type64='^{__CFData=}'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFDataReplaceBytes'>
<arg type64='^{__CFData=}'/>
<arg type64='{_CFRange=qq}'/>
<arg c_array_length_in_arg='3' type64='*' type_modifier='n'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFDataSetLength'>
<arg type64='^{__CFData=}'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFDateCompare'>
<arg type64='^{__CFDate=}'/>
<arg type64='^{__CFDate=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFDateCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='d'/>
<retval already_retained='true' type64='^{__CFDate=}'/>
</function>
<function name='CFDateFormatterCopyProperty'>
<arg type64='^{__CFDateFormatter=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='@'/>
</function>
<function name='CFDateFormatterCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFLocale=}'/>
<arg type64='q'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFDateFormatter=}'/>
</function>
<function name='CFDateFormatterCreateDateFormatFromTemplate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='Q'/>
<arg type64='^{__CFLocale=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFDateFormatterCreateDateFromString'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFDateFormatter=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<retval already_retained='true' type64='^{__CFDate=}'/>
</function>
<function name='CFDateFormatterCreateISO8601Formatter'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='Q'/>
<retval already_retained='true' type64='^{__CFDateFormatter=}'/>
</function>
<function name='CFDateFormatterCreateStringWithAbsoluteTime'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFDateFormatter=}'/>
<arg type64='d'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFDateFormatterCreateStringWithDate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFDateFormatter=}'/>
<arg type64='^{__CFDate=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFDateFormatterGetAbsoluteTimeFromString'>
<arg type64='^{__CFDateFormatter=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<arg type64='^d' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFDateFormatterGetDateStyle'>
<arg type64='^{__CFDateFormatter=}'/>
<retval type64='q'/>
</function>
<function name='CFDateFormatterGetFormat'>
<arg type64='^{__CFDateFormatter=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFDateFormatterGetLocale'>
<arg type64='^{__CFDateFormatter=}'/>
<retval type64='^{__CFLocale=}'/>
</function>
<function name='CFDateFormatterGetTimeStyle'>
<arg type64='^{__CFDateFormatter=}'/>
<retval type64='q'/>
</function>
<function name='CFDateFormatterGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFDateFormatterSetFormat'>
<arg type64='^{__CFDateFormatter=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFDateFormatterSetProperty'>
<arg type64='^{__CFDateFormatter=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='@'/>
<retval type64='v'/>
</function>
<function name='CFDateGetAbsoluteTime'>
<arg type64='^{__CFDate=}'/>
<retval type64='d'/>
</function>
<function name='CFDateGetTimeIntervalSinceDate'>
<arg type64='^{__CFDate=}'/>
<arg type64='^{__CFDate=}'/>
<retval type64='d'/>
</function>
<function name='CFDateGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFDictionaryAddValue'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFDictionaryApplyFunction'>
<arg type64='^{__CFDictionary=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^v'/>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFDictionaryContainsKey'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='B'/>
</function>
<function name='CFDictionaryContainsValue'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='B'/>
</function>
<function name='CFDictionaryCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='3' type64='^^v' type_modifier='n'/>
<arg c_array_length_in_arg='3' type64='^^v' type_modifier='n'/>
<arg type64='q'/>
<arg type64='^{_CFDictionaryKeyCallBacks=q^?^?^?^?^?}' type_modifier='n'/>
<arg type64='^{_CFDictionaryValueCallBacks=q^?^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFDictionaryCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFDictionary=}'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFDictionaryCreateMutable'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{_CFDictionaryKeyCallBacks=q^?^?^?^?^?}' type_modifier='n'/>
<arg type64='^{_CFDictionaryValueCallBacks=q^?^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFDictionaryCreateMutableCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{__CFDictionary=}'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFDictionaryGetCount'>
<arg type64='^{__CFDictionary=}'/>
<retval type64='q'/>
</function>
<function name='CFDictionaryGetCountOfKey'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFDictionaryGetCountOfValue'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFDictionaryGetKeysAndValues'>
<arg type64='^{__CFDictionary=}'/>
<arg c_array_of_variable_length='true' type64='^^v' type_modifier='o'/>
<arg c_array_of_variable_length='true' type64='^^v' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFDictionaryGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFDictionaryGetValue'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='^v'/>
</function>
<function name='CFDictionaryGetValueIfPresent'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='^^v' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFDictionaryRemoveAllValues'>
<arg type64='^{__CFDictionary=}'/>
<retval type64='v'/>
</function>
<function name='CFDictionaryRemoveValue'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFDictionaryReplaceValue'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFDictionarySetValue'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFEqual'>
<arg type64='@'/>
<arg type64='@'/>
<retval type64='B'/>
</function>
<function name='CFErrorCopyDescription'>
<arg type64='^{__CFError=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFErrorCopyFailureReason'>
<arg type64='^{__CFError=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFErrorCopyRecoverySuggestion'>
<arg type64='^{__CFError=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFErrorCopyUserInfo'>
<arg type64='^{__CFError=}'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFErrorCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<arg type64='^{__CFDictionary=}'/>
<retval already_retained='true' type64='^{__CFError=}'/>
</function>
<function name='CFErrorCreateWithUserInfoKeysAndValues'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<arg c_array_length_in_arg='5' type64='^^v' type_modifier='n'/>
<arg c_array_length_in_arg='5' type64='^^v' type_modifier='n'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFError=}'/>
</function>
<function name='CFErrorGetCode'>
<arg type64='^{__CFError=}'/>
<retval type64='q'/>
</function>
<function name='CFErrorGetDomain'>
<arg type64='^{__CFError=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFErrorGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFFileDescriptorCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='i'/>
<arg type64='B'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFFileDescriptor=}'/>
<arg type64='Q'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFFileDescriptorContext=q^v^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFFileDescriptor=}'/>
</function>
<function name='CFFileDescriptorCreateRunLoopSource'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFFileDescriptor=}'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFRunLoopSource=}'/>
</function>
<function name='CFFileDescriptorDisableCallBacks'>
<arg type64='^{__CFFileDescriptor=}'/>
<arg type64='Q'/>
<retval type64='v'/>
</function>
<function name='CFFileDescriptorEnableCallBacks'>
<arg type64='^{__CFFileDescriptor=}'/>
<arg type64='Q'/>
<retval type64='v'/>
</function>
<function name='CFFileDescriptorGetContext'>
<arg type64='^{__CFFileDescriptor=}'/>
<arg type64='^{_CFFileDescriptorContext=q^v^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFFileDescriptorGetNativeDescriptor'>
<arg type64='^{__CFFileDescriptor=}'/>
<retval type64='i'/>
</function>
<function name='CFFileDescriptorGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFFileDescriptorInvalidate'>
<arg type64='^{__CFFileDescriptor=}'/>
<retval type64='v'/>
</function>
<function name='CFFileDescriptorIsValid'>
<arg type64='^{__CFFileDescriptor=}'/>
<retval type64='B'/>
</function>
<function name='CFFileSecurityClearProperties'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='Q'/>
<retval type64='B'/>
</function>
<function name='CFFileSecurityCopyAccessControlList'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='^^{_acl}'/>
<retval type64='B'/>
</function>
<function name='CFFileSecurityCopyGroupUUID'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='^^{__CFUUID}'/>
<retval type64='B'/>
</function>
<function name='CFFileSecurityCopyOwnerUUID'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='^^{__CFUUID}'/>
<retval type64='B'/>
</function>
<function name='CFFileSecurityCreate'>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFFileSecurity=}'/>
</function>
<function name='CFFileSecurityCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFFileSecurity=}'/>
<retval already_retained='true' type64='^{__CFFileSecurity=}'/>
</function>
<function name='CFFileSecurityGetGroup'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='^I'/>
<retval type64='B'/>
</function>
<function name='CFFileSecurityGetMode'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='^S'/>
<retval type64='B'/>
</function>
<function name='CFFileSecurityGetOwner'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='^I'/>
<retval type64='B'/>
</function>
<function name='CFFileSecurityGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFFileSecuritySetAccessControlList'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='^{_acl=}'/>
<retval type64='B'/>
</function>
<function name='CFFileSecuritySetGroup'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='I'/>
<retval type64='B'/>
</function>
<function name='CFFileSecuritySetGroupUUID'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='^{__CFUUID=}'/>
<retval type64='B'/>
</function>
<function name='CFFileSecuritySetMode'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='S'/>
<retval type64='B'/>
</function>
<function name='CFFileSecuritySetOwner'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='I'/>
<retval type64='B'/>
</function>
<function name='CFFileSecuritySetOwnerUUID'>
<arg type64='^{__CFFileSecurity=}'/>
<arg type64='^{__CFUUID=}'/>
<retval type64='B'/>
</function>
<function name='CFGetAllocator'>
<arg type64='@'/>
<retval type64='^{__CFAllocator=}'/>
</function>
<function name='CFGetRetainCount'>
<arg type64='@'/>
<retval type64='q'/>
</function>
<function name='CFGetTypeID'>
<arg type64='@'/>
<retval type64='Q'/>
</function>
<function name='CFGregorianDateGetAbsoluteTime'>
<arg type64='{_CFGregorianDate=iccccd}'/>
<arg type64='^{__CFTimeZone=}'/>
<retval type64='d'/>
</function>
<function name='CFGregorianDateIsValid'>
<arg type64='{_CFGregorianDate=iccccd}'/>
<arg type64='Q'/>
<retval type64='B'/>
</function>
<function name='CFHash'>
<arg type64='@'/>
<retval type64='Q'/>
</function>
<function name='CFLocaleCopyAvailableLocaleIdentifiers'>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFLocaleCopyCommonISOCurrencyCodes'>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFLocaleCopyCurrent'>
<retval already_retained='true' type64='^{__CFLocale=}'/>
</function>
<function name='CFLocaleCopyDisplayNameForPropertyValue'>
<arg type64='^{__CFLocale=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFLocaleCopyISOCountryCodes'>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFLocaleCopyISOCurrencyCodes'>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFLocaleCopyISOLanguageCodes'>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFLocaleCopyPreferredLanguages'>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFLocaleCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFLocale=}'/>
</function>
<function name='CFLocaleCreateCanonicalLanguageIdentifierFromString'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFLocaleCreateCanonicalLocaleIdentifierFromScriptManagerCodes'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='s'/>
<arg type64='s'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFLocaleCreateCanonicalLocaleIdentifierFromString'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFLocaleCreateComponentsFromLocaleIdentifier'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFLocaleCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFLocale=}'/>
<retval already_retained='true' type64='^{__CFLocale=}'/>
</function>
<function name='CFLocaleCreateLocaleIdentifierFromComponents'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFDictionary=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFLocaleCreateLocaleIdentifierFromWindowsLocaleCode'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='I'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFLocaleGetIdentifier'>
<arg type64='^{__CFLocale=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFLocaleGetLanguageCharacterDirection'>
<arg type64='^{__CFString=}'/>
<retval type64='q'/>
</function>
<function name='CFLocaleGetLanguageLineDirection'>
<arg type64='^{__CFString=}'/>
<retval type64='q'/>
</function>
<function name='CFLocaleGetSystem'>
<retval type64='^{__CFLocale=}'/>
</function>
<function name='CFLocaleGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFLocaleGetValue'>
<arg type64='^{__CFLocale=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='@'/>
</function>
<function name='CFLocaleGetWindowsLocaleCodeFromLocaleIdentifier'>
<arg type64='^{__CFString=}'/>
<retval type64='I'/>
</function>
<function name='CFMachPortCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFMachPort=}'/>
<arg type64='^v'/>
<arg type64='q'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFMachPortContext=q^v^?^?^?}' type_modifier='n'/>
<arg type64='^B' type_modifier='o'/>
<retval already_retained='true' type64='^{__CFMachPort=}'/>
</function>
<function name='CFMachPortCreateRunLoopSource'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFMachPort=}'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFRunLoopSource=}'/>
</function>
<function name='CFMachPortCreateWithPort'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='I'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFMachPort=}'/>
<arg type64='^v'/>
<arg type64='q'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFMachPortContext=q^v^?^?^?}' type_modifier='n'/>
<arg type64='^B' type_modifier='o'/>
<retval already_retained='true' type64='^{__CFMachPort=}'/>
</function>
<function name='CFMachPortGetContext'>
<arg type64='^{__CFMachPort=}'/>
<arg type64='^{_CFMachPortContext=q^v^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFMachPortGetInvalidationCallBack'>
<arg type64='^{__CFMachPort=}'/>
<retval function_pointer='true' type64='^?'>
<arg type64='^{__CFMachPort=}'/>
<arg type64='^v'/>
<retval type64='v'/>
</retval>
</function>
<function name='CFMachPortGetPort'>
<arg type64='^{__CFMachPort=}'/>
<retval type64='I'/>
</function>
<function name='CFMachPortGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFMachPortInvalidate'>
<arg type64='^{__CFMachPort=}'/>
<retval type64='v'/>
</function>
<function name='CFMachPortIsValid'>
<arg type64='^{__CFMachPort=}'/>
<retval type64='B'/>
</function>
<function name='CFMachPortSetInvalidationCallBack'>
<arg type64='^{__CFMachPort=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFMachPort=}'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<retval type64='v'/>
</function>
<function name='CFMakeCollectable'>
<arg type64='@'/>
<retval type64='@'/>
</function>
<function name='CFMessagePortCreateLocal'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFMessagePort=}'/>
<arg type64='i'/>
<arg type64='^{__CFData=}'/>
<arg type64='^v'/>
<retval type64='^{__CFData=}'/>
</arg>
<arg type64='^{_CFMessagePortContext=q^v^?^?^?}' type_modifier='n'/>
<arg type64='^B' type_modifier='o'/>
<retval already_retained='true' type64='^{__CFMessagePort=}'/>
</function>
<function name='CFMessagePortCreateRemote'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFMessagePort=}'/>
</function>
<function name='CFMessagePortCreateRunLoopSource'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFMessagePort=}'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFRunLoopSource=}'/>
</function>
<function name='CFMessagePortGetContext'>
<arg type64='^{__CFMessagePort=}'/>
<arg type64='^{_CFMessagePortContext=q^v^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFMessagePortGetInvalidationCallBack'>
<arg type64='^{__CFMessagePort=}'/>
<retval function_pointer='true' type64='^?'>
<arg type64='^{__CFMessagePort=}'/>
<arg type64='^v'/>
<retval type64='v'/>
</retval>
</function>
<function name='CFMessagePortGetName'>
<arg type64='^{__CFMessagePort=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFMessagePortGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFMessagePortInvalidate'>
<arg type64='^{__CFMessagePort=}'/>
<retval type64='v'/>
</function>
<function name='CFMessagePortIsRemote'>
<arg type64='^{__CFMessagePort=}'/>
<retval type64='B'/>
</function>
<function name='CFMessagePortIsValid'>
<arg type64='^{__CFMessagePort=}'/>
<retval type64='B'/>
</function>
<function name='CFMessagePortSendRequest'>
<arg type64='^{__CFMessagePort=}'/>
<arg type64='i'/>
<arg type64='^{__CFData=}'/>
<arg type64='d'/>
<arg type64='d'/>
<arg type64='^{__CFString=}'/>
<arg type64='^^{__CFData}' type_modifier='o'/>
<retval type64='i'/>
</function>
<function name='CFMessagePortSetDispatchQueue'>
<arg type64='^{__CFMessagePort=}'/>
<arg type64='@'/>
<retval type64='v'/>
</function>
<function name='CFMessagePortSetInvalidationCallBack'>
<arg type64='^{__CFMessagePort=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFMessagePort=}'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<retval type64='v'/>
</function>
<function name='CFMessagePortSetName'>
<arg type64='^{__CFMessagePort=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='B'/>
</function>
<function name='CFNotificationCenterAddObserver'>
<arg type64='^{__CFNotificationCenter=}'/>
<arg type64='^v' type_modifier='n'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFNotificationCenter=}'/>
<arg type64='^v'/>
<arg type64='^{__CFString=}'/>
<arg type64='^v'/>
<arg type64='^{__CFDictionary=}'/>
<retval type64='v'/>
</arg>
<arg type64='^{__CFString=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFNotificationCenterGetDarwinNotifyCenter'>
<retval type64='^{__CFNotificationCenter=}'/>
</function>
<function name='CFNotificationCenterGetDistributedCenter'>
<retval type64='^{__CFNotificationCenter=}'/>
</function>
<function name='CFNotificationCenterGetLocalCenter'>
<retval type64='^{__CFNotificationCenter=}'/>
</function>
<function name='CFNotificationCenterGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFNotificationCenterPostNotification'>
<arg type64='^{__CFNotificationCenter=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='^{__CFDictionary=}'/>
<arg type64='B'/>
<retval type64='v'/>
</function>
<function name='CFNotificationCenterPostNotificationWithOptions'>
<arg type64='^{__CFNotificationCenter=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='^{__CFDictionary=}'/>
<arg type64='Q'/>
<retval type64='v'/>
</function>
<function name='CFNotificationCenterRemoveEveryObserver'>
<arg type64='^{__CFNotificationCenter=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFNotificationCenterRemoveObserver'>
<arg type64='^{__CFNotificationCenter=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='^{__CFString=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFNullGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFNumberCompare'>
<arg type64='^{__CFNumber=}'/>
<arg type64='^{__CFNumber=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFNumberCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^v' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFNumber=}'/>
</function>
<function name='CFNumberFormatterCopyProperty'>
<arg type64='^{__CFNumberFormatter=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='@'/>
</function>
<function name='CFNumberFormatterCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFLocale=}'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFNumberFormatter=}'/>
</function>
<function name='CFNumberFormatterCreateNumberFromString'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFNumberFormatter=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<arg type64='Q'/>
<retval already_retained='true' type64='^{__CFNumber=}'/>
</function>
<function name='CFNumberFormatterCreateStringWithNumber'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFNumberFormatter=}'/>
<arg type64='^{__CFNumber=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFNumberFormatterCreateStringWithValue'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFNumberFormatter=}'/>
<arg type64='q'/>
<arg type64='^v' type_modifier='o'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFNumberFormatterGetDecimalInfoForCurrencyCode'>
<arg type64='^{__CFString=}'/>
<arg type64='^i' type_modifier='o'/>
<arg type64='^d' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFNumberFormatterGetFormat'>
<arg type64='^{__CFNumberFormatter=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFNumberFormatterGetLocale'>
<arg type64='^{__CFNumberFormatter=}'/>
<retval type64='^{__CFLocale=}'/>
</function>
<function name='CFNumberFormatterGetStyle'>
<arg type64='^{__CFNumberFormatter=}'/>
<retval type64='q'/>
</function>
<function name='CFNumberFormatterGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFNumberFormatterGetValueFromString'>
<arg type64='^{__CFNumberFormatter=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<arg type64='q'/>
<arg type64='^v' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFNumberFormatterSetFormat'>
<arg type64='^{__CFNumberFormatter=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFNumberFormatterSetProperty'>
<arg type64='^{__CFNumberFormatter=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='@'/>
<retval type64='v'/>
</function>
<function name='CFNumberGetByteSize'>
<arg type64='^{__CFNumber=}'/>
<retval type64='q'/>
</function>
<function name='CFNumberGetType'>
<arg type64='^{__CFNumber=}'/>
<retval type64='q'/>
</function>
<function name='CFNumberGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFNumberGetValue'>
<arg type64='^{__CFNumber=}'/>
<arg type64='q'/>
<arg type64='^v' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFNumberIsFloatType'>
<arg type64='^{__CFNumber=}'/>
<retval type64='B'/>
</function>
<function name='CFPlugInAddInstanceForFactory'>
<arg type64='^{__CFUUID=}'/>
<retval type64='v'/>
</function>
<function name='CFPlugInCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFBundle=}'/>
</function>
<function name='CFPlugInFindFactoriesForPlugInType'>
<arg type64='^{__CFUUID=}'/>
<retval type64='^{__CFArray=}'/>
</function>
<function name='CFPlugInFindFactoriesForPlugInTypeInPlugIn'>
<arg type64='^{__CFUUID=}'/>
<arg type64='^{__CFBundle=}'/>
<retval type64='^{__CFArray=}'/>
</function>
<function name='CFPlugInGetBundle'>
<arg type64='^{__CFBundle=}'/>
<retval type64='^{__CFBundle=}'/>
</function>
<function name='CFPlugInGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFPlugInInstanceCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFUUID=}'/>
<arg type64='^{__CFUUID=}'/>
<retval type64='^v'/>
</function>
<function name='CFPlugInInstanceCreateWithInstanceDataSize'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{__CFString=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFPlugInInstance=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^^v'/>
<retval type64='B'/>
</arg>
<retval already_retained='true' type64='^{__CFPlugInInstance=}'/>
</function>
<function name='CFPlugInInstanceGetFactoryName'>
<arg type64='^{__CFPlugInInstance=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFPlugInInstanceGetInstanceData'>
<arg type64='^{__CFPlugInInstance=}'/>
<retval type64='^v'/>
</function>
<function name='CFPlugInInstanceGetInterfaceFunctionTable'>
<arg type64='^{__CFPlugInInstance=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^^v' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFPlugInInstanceGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFPlugInIsLoadOnDemand'>
<arg type64='^{__CFBundle=}'/>
<retval type64='B'/>
</function>
<function name='CFPlugInRegisterFactoryFunction'>
<arg type64='^{__CFUUID=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFUUID=}'/>
<retval type64='^v'/>
</arg>
<retval type64='B'/>
</function>
<function name='CFPlugInRegisterFactoryFunctionByName'>
<arg type64='^{__CFUUID=}'/>
<arg type64='^{__CFBundle=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='B'/>
</function>
<function name='CFPlugInRegisterPlugInType'>
<arg type64='^{__CFUUID=}'/>
<arg type64='^{__CFUUID=}'/>
<retval type64='B'/>
</function>
<function name='CFPlugInRemoveInstanceForFactory'>
<arg type64='^{__CFUUID=}'/>
<retval type64='v'/>
</function>
<function name='CFPlugInSetLoadOnDemand'>
<arg type64='^{__CFBundle=}'/>
<arg type64='B'/>
<retval type64='v'/>
</function>
<function name='CFPlugInUnregisterFactory'>
<arg type64='^{__CFUUID=}'/>
<retval type64='B'/>
</function>
<function name='CFPlugInUnregisterPlugInType'>
<arg type64='^{__CFUUID=}'/>
<arg type64='^{__CFUUID=}'/>
<retval type64='B'/>
</function>
<function name='CFPreferencesAddSuitePreferencesToApp'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFPreferencesAppSynchronize'>
<arg type64='^{__CFString=}'/>
<retval type64='B'/>
</function>
<function name='CFPreferencesAppValueIsForced'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='B'/>
</function>
<function name='CFPreferencesCopyAppValue'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='^v'/>
</function>
<function name='CFPreferencesCopyApplicationList'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFPreferencesCopyKeyList'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFPreferencesCopyMultiple'>
<arg type64='^{__CFArray=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFPreferencesCopyValue'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='^v'/>
</function>
<function name='CFPreferencesGetAppBooleanValue'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^B' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFPreferencesGetAppIntegerValue'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^B' type_modifier='o'/>
<retval type64='q'/>
</function>
<function name='CFPreferencesRemoveSuitePreferencesFromApp'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFPreferencesSetAppValue'>
<arg type64='^{__CFString=}'/>
<arg type64='^v'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFPreferencesSetMultiple'>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^{__CFArray=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFPreferencesSetValue'>
<arg type64='^{__CFString=}'/>
<arg type64='^v'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFPreferencesSynchronize'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='B'/>
</function>
<function name='CFPropertyListCreateData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^v'/>
<arg type64='q'/>
<arg type64='Q'/>
<arg type64='^^{__CFError}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFPropertyListCreateDeepCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^v'/>
<arg type64='Q'/>
<retval type64='^v'/>
</function>
<function name='CFPropertyListCreateFromStream'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFReadStream=}'/>
<arg type64='q'/>
<arg type64='Q'/>
<arg type64='^q' type_modifier='o'/>
<arg type64='^^{__CFString}' type_modifier='o'/>
<retval type64='^v'/>
</function>
<function name='CFPropertyListCreateFromXMLData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='Q'/>
<arg type64='^^{__CFString}' type_modifier='o'/>
<retval type64='^v'/>
</function>
<function name='CFPropertyListCreateWithData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='Q'/>
<arg type64='^q'/>
<arg type64='^^{__CFError}'/>
<retval type64='^v'/>
</function>
<function name='CFPropertyListCreateWithStream'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFReadStream=}'/>
<arg type64='q'/>
<arg type64='Q'/>
<arg type64='^q'/>
<arg type64='^^{__CFError}'/>
<retval type64='^v'/>
</function>
<function name='CFPropertyListCreateXMLData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^v'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFPropertyListIsValid'>
<arg type64='^v'/>
<arg type64='q'/>
<retval type64='B'/>
</function>
<function name='CFPropertyListWrite'>
<arg type64='^v'/>
<arg type64='^{__CFWriteStream=}'/>
<arg type64='q'/>
<arg type64='Q'/>
<arg type64='^^{__CFError}'/>
<retval type64='q'/>
</function>
<function name='CFPropertyListWriteToStream'>
<arg type64='^v'/>
<arg type64='^{__CFWriteStream=}'/>
<arg type64='q'/>
<arg type64='^^{__CFString}' type_modifier='o'/>
<retval type64='q'/>
</function>
<function inline='true' name='CFRangeMake'>
<arg type64='q'/>
<arg type64='q'/>
<retval type64='{_CFRange=qq}'/>
</function>
<function name='CFReadStreamClose'>
<arg type64='^{__CFReadStream=}'/>
<retval type64='v'/>
</function>
<function name='CFReadStreamCopyDispatchQueue'>
<arg type64='^{__CFReadStream=}'/>
<retval type64='@'/>
</function>
<function name='CFReadStreamCopyError'>
<arg type64='^{__CFReadStream=}'/>
<retval already_retained='true' type64='^{__CFError=}'/>
</function>
<function name='CFReadStreamCopyProperty'>
<arg type64='^{__CFReadStream=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='@'/>
</function>
<function name='CFReadStreamCreateWithBytesNoCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFReadStream=}'/>
</function>
<function name='CFReadStreamCreateWithFile'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFReadStream=}'/>
</function>
<function name='CFReadStreamGetBuffer'>
<arg type64='^{__CFReadStream=}'/>
<arg type64='q'/>
<arg type64='^q' type_modifier='o'/>
<retval c_array_length_in_arg='1' type64='*'/>
</function>
<function name='CFReadStreamGetError'>
<arg type64='^{__CFReadStream=}'/>
<retval type64='{_CFStreamError=qi}'/>
</function>
<function name='CFReadStreamGetStatus'>
<arg type64='^{__CFReadStream=}'/>
<retval type64='q'/>
</function>
<function name='CFReadStreamGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFReadStreamHasBytesAvailable'>
<arg type64='^{__CFReadStream=}'/>
<retval type64='B'/>
</function>
<function name='CFReadStreamOpen'>
<arg type64='^{__CFReadStream=}'/>
<retval type64='B'/>
</function>
<function name='CFReadStreamRead'>
<arg type64='^{__CFReadStream=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='o'/>
<arg type64='q'/>
<retval type64='q'/>
</function>
<function name='CFReadStreamScheduleWithRunLoop'>
<arg type64='^{__CFReadStream=}'/>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFReadStreamSetClient'>
<arg type64='^{__CFReadStream=}'/>
<arg type64='Q'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFReadStream=}'/>
<arg type64='Q'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFStreamClientContext=q^v^?^?^?}' type_modifier='n'/>
<retval type64='B'/>
</function>
<function name='CFReadStreamSetDispatchQueue'>
<arg type64='^{__CFReadStream=}'/>
<arg type64='@'/>
<retval type64='v'/>
</function>
<function name='CFReadStreamSetProperty'>
<arg type64='^{__CFReadStream=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='@'/>
<retval type64='B'/>
</function>
<function name='CFReadStreamUnscheduleFromRunLoop'>
<arg type64='^{__CFReadStream=}'/>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFRelease'>
<arg type64='@'/>
<retval type64='v'/>
</function>
<function name='CFRetain'>
<arg type64='@'/>
<retval type64='@'/>
</function>
<function name='CFRunLoopAddCommonMode'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopAddObserver'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFRunLoopObserver=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopAddSource'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFRunLoopSource=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopAddTimer'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFRunLoopTimer=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopContainsObserver'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFRunLoopObserver=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='B'/>
</function>
<function name='CFRunLoopContainsSource'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFRunLoopSource=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='B'/>
</function>
<function name='CFRunLoopContainsTimer'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFRunLoopTimer=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='B'/>
</function>
<function name='CFRunLoopCopyAllModes'>
<arg type64='^{__CFRunLoop=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFRunLoopCopyCurrentMode'>
<arg type64='^{__CFRunLoop=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFRunLoopGetCurrent'>
<retval type64='^{__CFRunLoop=}'/>
</function>
<function name='CFRunLoopGetMain'>
<retval type64='^{__CFRunLoop=}'/>
</function>
<function name='CFRunLoopGetNextTimerFireDate'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='d'/>
</function>
<function name='CFRunLoopGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFRunLoopIsWaiting'>
<arg type64='^{__CFRunLoop=}'/>
<retval type64='B'/>
</function>
<function name='CFRunLoopObserverCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='Q'/>
<arg type64='B'/>
<arg type64='q'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFRunLoopObserver=}'/>
<arg type64='Q'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFRunLoopObserverContext=q^v^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFRunLoopObserver=}'/>
</function>
<function name='CFRunLoopObserverCreateWithHandler'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='Q'/>
<arg type64='B'/>
<arg type64='q'/>
<arg function_pointer='true' type64='@?'>
<arg type64='^{__CFRunLoopObserver=}'/>
<arg type64='Q'/>
<retval type64='v'/>
</arg>
<retval already_retained='true' type64='^{__CFRunLoopObserver=}'/>
</function>
<function name='CFRunLoopObserverDoesRepeat'>
<arg type64='^{__CFRunLoopObserver=}'/>
<retval type64='B'/>
</function>
<function name='CFRunLoopObserverGetActivities'>
<arg type64='^{__CFRunLoopObserver=}'/>
<retval type64='Q'/>
</function>
<function name='CFRunLoopObserverGetContext'>
<arg type64='^{__CFRunLoopObserver=}'/>
<arg type64='^{_CFRunLoopObserverContext=q^v^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopObserverGetOrder'>
<arg type64='^{__CFRunLoopObserver=}'/>
<retval type64='q'/>
</function>
<function name='CFRunLoopObserverGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFRunLoopObserverInvalidate'>
<arg type64='^{__CFRunLoopObserver=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopObserverIsValid'>
<arg type64='^{__CFRunLoopObserver=}'/>
<retval type64='B'/>
</function>
<function name='CFRunLoopPerformBlock'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='@'/>
<arg function_pointer='true' type64='@?'>
<retval type64='v'/>
</arg>
<retval type64='v'/>
</function>
<function name='CFRunLoopRemoveObserver'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFRunLoopObserver=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopRemoveSource'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFRunLoopSource=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopRemoveTimer'>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFRunLoopTimer=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopRun'>
<retval type64='v'/>
</function>
<function name='CFRunLoopRunInMode'>
<arg type64='^{__CFString=}'/>
<arg type64='d'/>
<arg type64='B'/>
<retval type64='i'/>
</function>
<function name='CFRunLoopSourceCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{_CFRunLoopSourceContext=q^v^?^?^?^?^?^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFRunLoopSource=}'/>
</function>
<function name='CFRunLoopSourceGetContext'>
<arg type64='^{__CFRunLoopSource=}'/>
<arg type64='^{_CFRunLoopSourceContext=q^v^?^?^?^?^?^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopSourceGetOrder'>
<arg type64='^{__CFRunLoopSource=}'/>
<retval type64='q'/>
</function>
<function name='CFRunLoopSourceGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFRunLoopSourceInvalidate'>
<arg type64='^{__CFRunLoopSource=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopSourceIsValid'>
<arg type64='^{__CFRunLoopSource=}'/>
<retval type64='B'/>
</function>
<function name='CFRunLoopSourceSignal'>
<arg type64='^{__CFRunLoopSource=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopStop'>
<arg type64='^{__CFRunLoop=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopTimerCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='d'/>
<arg type64='d'/>
<arg type64='Q'/>
<arg type64='q'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFRunLoopTimer=}'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFRunLoopTimerContext=q^v^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFRunLoopTimer=}'/>
</function>
<function name='CFRunLoopTimerCreateWithHandler'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='d'/>
<arg type64='d'/>
<arg type64='Q'/>
<arg type64='q'/>
<arg function_pointer='true' type64='@?'>
<arg type64='^{__CFRunLoopTimer=}'/>
<retval type64='v'/>
</arg>
<retval already_retained='true' type64='^{__CFRunLoopTimer=}'/>
</function>
<function name='CFRunLoopTimerDoesRepeat'>
<arg type64='^{__CFRunLoopTimer=}'/>
<retval type64='B'/>
</function>
<function name='CFRunLoopTimerGetContext'>
<arg type64='^{__CFRunLoopTimer=}'/>
<arg type64='^{_CFRunLoopTimerContext=q^v^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopTimerGetInterval'>
<arg type64='^{__CFRunLoopTimer=}'/>
<retval type64='d'/>
</function>
<function name='CFRunLoopTimerGetNextFireDate'>
<arg type64='^{__CFRunLoopTimer=}'/>
<retval type64='d'/>
</function>
<function name='CFRunLoopTimerGetOrder'>
<arg type64='^{__CFRunLoopTimer=}'/>
<retval type64='q'/>
</function>
<function name='CFRunLoopTimerGetTolerance'>
<arg type64='^{__CFRunLoopTimer=}'/>
<retval type64='d'/>
</function>
<function name='CFRunLoopTimerGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFRunLoopTimerInvalidate'>
<arg type64='^{__CFRunLoopTimer=}'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopTimerIsValid'>
<arg type64='^{__CFRunLoopTimer=}'/>
<retval type64='B'/>
</function>
<function name='CFRunLoopTimerSetNextFireDate'>
<arg type64='^{__CFRunLoopTimer=}'/>
<arg type64='d'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopTimerSetTolerance'>
<arg type64='^{__CFRunLoopTimer=}'/>
<arg type64='d'/>
<retval type64='v'/>
</function>
<function name='CFRunLoopWakeUp'>
<arg type64='^{__CFRunLoop=}'/>
<retval type64='v'/>
</function>
<function name='CFSetAddValue'>
<arg type64='^{__CFSet=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFSetApplyFunction'>
<arg type64='^{__CFSet=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFSetContainsValue'>
<arg type64='^{__CFSet=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='B'/>
</function>
<function name='CFSetCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='^^v' type_modifier='n'/>
<arg type64='q'/>
<arg type64='^{_CFSetCallBacks=q^?^?^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFSet=}'/>
</function>
<function name='CFSetCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFSet=}'/>
<retval already_retained='true' type64='^{__CFSet=}'/>
</function>
<function name='CFSetCreateMutable'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{_CFSetCallBacks=q^?^?^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFSet=}'/>
</function>
<function name='CFSetCreateMutableCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{__CFSet=}'/>
<retval already_retained='true' type64='^{__CFSet=}'/>
</function>
<function name='CFSetGetCount'>
<arg type64='^{__CFSet=}'/>
<retval type64='q'/>
</function>
<function name='CFSetGetCountOfValue'>
<arg type64='^{__CFSet=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFSetGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFSetGetValue'>
<arg type64='^{__CFSet=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='^v'/>
</function>
<function name='CFSetGetValueIfPresent'>
<arg type64='^{__CFSet=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='^^v' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFSetGetValues'>
<arg type64='^{__CFSet=}'/>
<arg c_array_of_variable_length='true' type64='^^v' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFSetRemoveAllValues'>
<arg type64='^{__CFSet=}'/>
<retval type64='v'/>
</function>
<function name='CFSetRemoveValue'>
<arg type64='^{__CFSet=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFSetReplaceValue'>
<arg type64='^{__CFSet=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFSetSetValue'>
<arg type64='^{__CFSet=}'/>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFShow'>
<arg type64='@'/>
<retval type64='v'/>
</function>
<function name='CFShowStr'>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFSocketConnectToAddress'>
<arg type64='^{__CFSocket=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='d'/>
<retval type64='q'/>
</function>
<function name='CFSocketCopyAddress'>
<arg type64='^{__CFSocket=}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFSocketCopyPeerAddress'>
<arg type64='^{__CFSocket=}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFSocketCopyRegisteredSocketSignature'>
<arg type64='^{_CFSocketSignature=iii^{__CFData}}' type_modifier='n'/>
<arg type64='d'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{_CFSocketSignature=iii^{__CFData}}' type_modifier='o'/>
<arg type64='^^{__CFData}' type_modifier='o'/>
<retval type64='q'/>
</function>
<function name='CFSocketCopyRegisteredValue'>
<arg type64='^{_CFSocketSignature=iii^{__CFData}}' type_modifier='n'/>
<arg type64='d'/>
<arg type64='^{__CFString=}'/>
<arg type64='^^v' type_modifier='o'/>
<arg type64='^^{__CFData}' type_modifier='o'/>
<retval type64='q'/>
</function>
<function name='CFSocketCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='i'/>
<arg type64='i'/>
<arg type64='i'/>
<arg type64='Q'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFSocket=}'/>
<arg type64='Q'/>
<arg type64='^{__CFData=}'/>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFSocketContext=q^v^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFSocket=}'/>
</function>
<function name='CFSocketCreateConnectedToSocketSignature'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{_CFSocketSignature=iii^{__CFData}}' type_modifier='n'/>
<arg type64='Q'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFSocket=}'/>
<arg type64='Q'/>
<arg type64='^{__CFData=}'/>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFSocketContext=q^v^?^?^?}' type_modifier='n'/>
<arg type64='d'/>
<retval already_retained='true' type64='^{__CFSocket=}'/>
</function>
<function name='CFSocketCreateRunLoopSource'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFSocket=}'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFRunLoopSource=}'/>
</function>
<function name='CFSocketCreateWithNative'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='i'/>
<arg type64='Q'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFSocket=}'/>
<arg type64='Q'/>
<arg type64='^{__CFData=}'/>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFSocketContext=q^v^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFSocket=}'/>
</function>
<function name='CFSocketCreateWithSocketSignature'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{_CFSocketSignature=iii^{__CFData}}' type_modifier='n'/>
<arg type64='Q'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFSocket=}'/>
<arg type64='Q'/>
<arg type64='^{__CFData=}'/>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFSocketContext=q^v^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFSocket=}'/>
</function>
<function name='CFSocketDisableCallBacks'>
<arg type64='^{__CFSocket=}'/>
<arg type64='Q'/>
<retval type64='v'/>
</function>
<function name='CFSocketEnableCallBacks'>
<arg type64='^{__CFSocket=}'/>
<arg type64='Q'/>
<retval type64='v'/>
</function>
<function name='CFSocketGetContext'>
<arg type64='^{__CFSocket=}'/>
<arg type64='^{_CFSocketContext=q^v^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFSocketGetDefaultNameRegistryPortNumber'>
<retval type64='S'/>
</function>
<function name='CFSocketGetNative'>
<arg type64='^{__CFSocket=}'/>
<retval type64='i'/>
</function>
<function name='CFSocketGetSocketFlags'>
<arg type64='^{__CFSocket=}'/>
<retval type64='Q'/>
</function>
<function name='CFSocketGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFSocketInvalidate'>
<arg type64='^{__CFSocket=}'/>
<retval type64='v'/>
</function>
<function name='CFSocketIsValid'>
<arg type64='^{__CFSocket=}'/>
<retval type64='B'/>
</function>
<function name='CFSocketRegisterSocketSignature'>
<arg type64='^{_CFSocketSignature=iii^{__CFData}}' type_modifier='n'/>
<arg type64='d'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{_CFSocketSignature=iii^{__CFData}}' type_modifier='n'/>
<retval type64='q'/>
</function>
<function name='CFSocketRegisterValue'>
<arg type64='^{_CFSocketSignature=iii^{__CFData}}' type_modifier='n'/>
<arg type64='d'/>
<arg type64='^{__CFString=}'/>
<arg type64='^v'/>
<retval type64='q'/>
</function>
<function name='CFSocketSendData'>
<arg type64='^{__CFSocket=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='d'/>
<retval type64='q'/>
</function>
<function name='CFSocketSetAddress'>
<arg type64='^{__CFSocket=}'/>
<arg type64='^{__CFData=}'/>
<retval type64='q'/>
</function>
<function name='CFSocketSetDefaultNameRegistryPortNumber'>
<arg type64='S'/>
<retval type64='v'/>
</function>
<function name='CFSocketSetSocketFlags'>
<arg type64='^{__CFSocket=}'/>
<arg type64='Q'/>
<retval type64='v'/>
</function>
<function name='CFSocketUnregister'>
<arg type64='^{_CFSocketSignature=iii^{__CFData}}' type_modifier='n'/>
<arg type64='d'/>
<arg type64='^{__CFString=}'/>
<retval type64='q'/>
</function>
<function name='CFStreamCreateBoundPair'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^^{__CFReadStream}' type_modifier='o'/>
<arg type64='^^{__CFWriteStream}' type_modifier='o'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFStreamCreatePairWithPeerSocketSignature'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{_CFSocketSignature=iii^{__CFData}}' type_modifier='n'/>
<arg type64='^^{__CFReadStream}' type_modifier='o'/>
<arg type64='^^{__CFWriteStream}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFStreamCreatePairWithSocket'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='i'/>
<arg type64='^^{__CFReadStream}' type_modifier='o'/>
<arg type64='^^{__CFWriteStream}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFStreamCreatePairWithSocketToHost'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='I'/>
<arg type64='^^{__CFReadStream}' type_modifier='o'/>
<arg type64='^^{__CFWriteStream}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFStringAppend'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFStringAppendCString'>
<arg type64='^{__CFString=}'/>
<arg type64='*'/>
<arg type64='I'/>
<retval type64='v'/>
</function>
<function name='CFStringAppendCharacters'>
<arg type64='^{__CFString=}'/>
<arg c_array_length_in_arg='2' type64='^S' type_modifier='n'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFStringAppendFormat' variadic='true'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFDictionary=}'/>
<arg printf_format='true' type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFStringAppendFormatAndArguments'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__va_list_tag=II^v^v}'/>
<retval type64='v'/>
</function>
<function name='CFStringAppendPascalString'>
<arg type64='^{__CFString=}'/>
<arg type64='*'/>
<arg type64='I'/>
<retval type64='v'/>
</function>
<function name='CFStringCapitalize'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFLocale=}'/>
<retval type64='v'/>
</function>
<function name='CFStringCompare'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='Q'/>
<retval type64='q'/>
</function>
<function name='CFStringCompareWithOptions'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='Q'/>
<retval type64='q'/>
</function>
<function name='CFStringCompareWithOptionsAndLocale'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='Q'/>
<arg type64='^{__CFLocale=}'/>
<retval type64='q'/>
</function>
<function name='CFStringConvertEncodingToIANACharSetName'>
<arg type64='I'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFStringConvertEncodingToNSStringEncoding'>
<arg type64='I'/>
<retval type64='Q'/>
</function>
<function name='CFStringConvertEncodingToWindowsCodepage'>
<arg type64='I'/>
<retval type64='I'/>
</function>
<function name='CFStringConvertIANACharSetNameToEncoding'>
<arg type64='^{__CFString=}'/>
<retval type64='I'/>
</function>
<function name='CFStringConvertNSStringEncodingToEncoding'>
<arg type64='Q'/>
<retval type64='I'/>
</function>
<function name='CFStringConvertWindowsCodepageToEncoding'>
<arg type64='I'/>
<retval type64='I'/>
</function>
<function name='CFStringCreateArrayBySeparatingStrings'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFStringCreateArrayWithFindResults'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='Q'/>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFStringCreateByCombiningStrings'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFArray=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateExternalRepresentation'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='I'/>
<arg type64='C'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFStringCreateFromExternalRepresentation'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='I'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateMutable'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateMutableCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateMutableWithExternalCharactersNoCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='^S' type_modifier='n'/>
<arg type64='q'/>
<arg type64='q'/>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithBytes'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<arg type64='I'/>
<arg type64='B'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithBytesNoCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<arg type64='I'/>
<arg type64='B'/>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithCString'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='*'/>
<arg type64='I'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithCStringNoCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='*'/>
<arg type64='I'/>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithCharacters'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='^S' type_modifier='n'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithCharactersNoCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='^S' type_modifier='n'/>
<arg type64='q'/>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithFileSystemRepresentation'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='*'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithFormat' variadic='true'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFDictionary=}'/>
<arg printf_format='true' type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithFormatAndArguments'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__va_list_tag=II^v^v}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithPascalString'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='*'/>
<arg type64='I'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithPascalStringNoCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='*'/>
<arg type64='I'/>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringCreateWithSubstring'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringDelete'>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<retval type64='v'/>
</function>
<function name='CFStringFind'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='Q'/>
<retval type64='{_CFRange=qq}'/>
</function>
<function name='CFStringFindAndReplace'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='Q'/>
<retval type64='q'/>
</function>
<function name='CFStringFindCharacterFromSet'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFCharacterSet=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='Q'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFStringFindWithOptions'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='Q'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFStringFindWithOptionsAndLocale'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='Q'/>
<arg type64='^{__CFLocale=}'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFStringFold'>
<arg type64='^{__CFString=}'/>
<arg type64='Q'/>
<arg type64='^{__CFLocale=}'/>
<retval type64='v'/>
</function>
<function name='CFStringGetBytes'>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='I'/>
<arg type64='C'/>
<arg type64='B'/>
<arg c_array_length_in_arg='6' type64='*' type_modifier='o'/>
<arg type64='q'/>
<arg type64='^q' type_modifier='o'/>
<retval type64='q'/>
</function>
<function name='CFStringGetCString'>
<arg type64='^{__CFString=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='o'/>
<arg type64='q'/>
<arg type64='I'/>
<retval type64='B'/>
</function>
<function name='CFStringGetCStringPtr'>
<arg type64='^{__CFString=}'/>
<arg type64='I'/>
<retval type64='*'/>
</function>
<function name='CFStringGetCharacterAtIndex'>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<retval type64='S'/>
</function>
<function inline='true' name='CFStringGetCharacterFromInlineBuffer'>
<arg type64='^{_CFStringInlineBuffer=[64S]^{__CFString}^S*{_CFRange=qq}qq}' type_modifier='n'/>
<arg type64='q'/>
<retval type64='S'/>
</function>
<function name='CFStringGetCharacters'>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg c_array_of_variable_length='true' type64='^S' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFStringGetCharactersPtr'>
<arg type64='^{__CFString=}'/>
<retval type64='^S'/>
</function>
<function name='CFStringGetDoubleValue'>
<arg type64='^{__CFString=}'/>
<retval type64='d'/>
</function>
<function name='CFStringGetFastestEncoding'>
<arg type64='^{__CFString=}'/>
<retval type64='I'/>
</function>
<function name='CFStringGetFileSystemRepresentation'>
<arg type64='^{__CFString=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='o'/>
<arg type64='q'/>
<retval type64='B'/>
</function>
<function name='CFStringGetHyphenationLocationBeforeIndex'>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='Q'/>
<arg type64='^{__CFLocale=}'/>
<arg type64='^I'/>
<retval type64='q'/>
</function>
<function name='CFStringGetIntValue'>
<arg type64='^{__CFString=}'/>
<retval type64='i'/>
</function>
<function name='CFStringGetLength'>
<arg type64='^{__CFString=}'/>
<retval type64='q'/>
</function>
<function name='CFStringGetLineBounds'>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^q' type_modifier='o'/>
<arg type64='^q' type_modifier='o'/>
<arg type64='^q' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFStringGetListOfAvailableEncodings'>
<retval type64='^I'/>
</function>
<function inline='true' name='CFStringGetLongCharacterForSurrogatePair'>
<arg type64='S'/>
<arg type64='S'/>
<retval type64='I'/>
</function>
<function name='CFStringGetMaximumSizeForEncoding'>
<arg type64='q'/>
<arg type64='I'/>
<retval type64='q'/>
</function>
<function name='CFStringGetMaximumSizeOfFileSystemRepresentation'>
<arg type64='^{__CFString=}'/>
<retval type64='q'/>
</function>
<function name='CFStringGetMostCompatibleMacStringEncoding'>
<arg type64='I'/>
<retval type64='I'/>
</function>
<function name='CFStringGetNameOfEncoding'>
<arg type64='I'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFStringGetParagraphBounds'>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^q' type_modifier='o'/>
<arg type64='^q' type_modifier='o'/>
<arg type64='^q' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFStringGetPascalString'>
<arg type64='^{__CFString=}'/>
<arg type64='*'/>
<arg type64='q'/>
<arg type64='I'/>
<retval type64='B'/>
</function>
<function name='CFStringGetPascalStringPtr'>
<arg type64='^{__CFString=}'/>
<arg type64='I'/>
<retval type64='*'/>
</function>
<function name='CFStringGetRangeOfComposedCharactersAtIndex'>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<retval type64='{_CFRange=qq}'/>
</function>
<function name='CFStringGetSmallestEncoding'>
<arg type64='^{__CFString=}'/>
<retval type64='I'/>
</function>
<function inline='true' name='CFStringGetSurrogatePairForLongCharacter'>
<arg type64='I'/>
<arg type64='^S'/>
<retval type64='B'/>
</function>
<function name='CFStringGetSystemEncoding'>
<retval type64='I'/>
</function>
<function name='CFStringGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFStringHasPrefix'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='B'/>
</function>
<function name='CFStringHasSuffix'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='B'/>
</function>
<function inline='true' name='CFStringInitInlineBuffer'>
<arg type64='^{__CFString=}'/>
<arg type64='^{_CFStringInlineBuffer=[64S]^{__CFString}^S*{_CFRange=qq}qq}' type_modifier='n'/>
<arg type64='{_CFRange=qq}'/>
<retval type64='v'/>
</function>
<function name='CFStringInsert'>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFStringIsEncodingAvailable'>
<arg type64='I'/>
<retval type64='B'/>
</function>
<function name='CFStringIsHyphenationAvailableForLocale'>
<arg type64='^{__CFLocale=}'/>
<retval type64='B'/>
</function>
<function inline='true' name='CFStringIsSurrogateHighCharacter'>
<arg type64='S'/>
<retval type64='B'/>
</function>
<function inline='true' name='CFStringIsSurrogateLowCharacter'>
<arg type64='S'/>
<retval type64='B'/>
</function>
<function name='CFStringLowercase'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFLocale=}'/>
<retval type64='v'/>
</function>
<function name='CFStringNormalize'>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFStringPad'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFStringReplace'>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFStringReplaceAll'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFStringSetExternalCharactersNoCopy'>
<arg type64='^{__CFString=}'/>
<arg c_array_length_in_arg='2' type64='^S' type_modifier='n'/>
<arg type64='q'/>
<arg type64='q'/>
<retval type64='v'/>
</function>
<function name='CFStringTokenizerAdvanceToNextToken'>
<arg type64='^{__CFStringTokenizer=}'/>
<retval type64='Q'/>
</function>
<function name='CFStringTokenizerCopyBestStringLanguage'>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFStringTokenizerCopyCurrentTokenAttribute'>
<arg type64='^{__CFStringTokenizer=}'/>
<arg type64='Q'/>
<retval already_retained='true' type64='@'/>
</function>
<function name='CFStringTokenizerCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<arg type64='Q'/>
<arg type64='^{__CFLocale=}'/>
<retval already_retained='true' type64='^{__CFStringTokenizer=}'/>
</function>
<function name='CFStringTokenizerGetCurrentSubTokens'>
<arg type64='^{__CFStringTokenizer=}'/>
<arg type64='^{_CFRange=qq}'/>
<arg type64='q'/>
<arg type64='^{__CFArray=}'/>
<retval type64='q'/>
</function>
<function name='CFStringTokenizerGetCurrentTokenRange'>
<arg type64='^{__CFStringTokenizer=}'/>
<retval type64='{_CFRange=qq}'/>
</function>
<function name='CFStringTokenizerGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFStringTokenizerGoToTokenAtIndex'>
<arg type64='^{__CFStringTokenizer=}'/>
<arg type64='q'/>
<retval type64='Q'/>
</function>
<function name='CFStringTokenizerSetString'>
<arg type64='^{__CFStringTokenizer=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='{_CFRange=qq}'/>
<retval type64='v'/>
</function>
<function name='CFStringTransform'>
<arg type64='^{__CFString=}'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<arg type64='^{__CFString=}'/>
<arg type64='B'/>
<retval type64='B'/>
</function>
<function name='CFStringTrim'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFStringTrimWhitespace'>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFStringUppercase'>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFLocale=}'/>
<retval type64='v'/>
</function>
<function inline='true' name='CFSwapInt16'>
<arg type64='S'/>
<retval type64='S'/>
</function>
<function inline='true' name='CFSwapInt16BigToHost'>
<arg type64='S'/>
<retval type64='S'/>
</function>
<function inline='true' name='CFSwapInt16HostToBig'>
<arg type64='S'/>
<retval type64='S'/>
</function>
<function inline='true' name='CFSwapInt16HostToLittle'>
<arg type64='S'/>
<retval type64='S'/>
</function>
<function inline='true' name='CFSwapInt16LittleToHost'>
<arg type64='S'/>
<retval type64='S'/>
</function>
<function inline='true' name='CFSwapInt32'>
<arg type64='I'/>
<retval type64='I'/>
</function>
<function inline='true' name='CFSwapInt32BigToHost'>
<arg type64='I'/>
<retval type64='I'/>
</function>
<function inline='true' name='CFSwapInt32HostToBig'>
<arg type64='I'/>
<retval type64='I'/>
</function>
<function inline='true' name='CFSwapInt32HostToLittle'>
<arg type64='I'/>
<retval type64='I'/>
</function>
<function inline='true' name='CFSwapInt32LittleToHost'>
<arg type64='I'/>
<retval type64='I'/>
</function>
<function inline='true' name='CFSwapInt64'>
<arg type64='Q'/>
<retval type64='Q'/>
</function>
<function inline='true' name='CFSwapInt64BigToHost'>
<arg type64='Q'/>
<retval type64='Q'/>
</function>
<function inline='true' name='CFSwapInt64HostToBig'>
<arg type64='Q'/>
<retval type64='Q'/>
</function>
<function inline='true' name='CFSwapInt64HostToLittle'>
<arg type64='Q'/>
<retval type64='Q'/>
</function>
<function inline='true' name='CFSwapInt64LittleToHost'>
<arg type64='Q'/>
<retval type64='Q'/>
</function>
<function name='CFTimeZoneCopyAbbreviation'>
<arg type64='^{__CFTimeZone=}'/>
<arg type64='d'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFTimeZoneCopyAbbreviationDictionary'>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFTimeZoneCopyDefault'>
<retval already_retained='true' type64='^{__CFTimeZone=}'/>
</function>
<function name='CFTimeZoneCopyKnownNames'>
<retval already_retained='true' type64='^{__CFArray=}'/>
</function>
<function name='CFTimeZoneCopyLocalizedName'>
<arg type64='^{__CFTimeZone=}'/>
<arg type64='q'/>
<arg type64='^{__CFLocale=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFTimeZoneCopySystem'>
<retval already_retained='true' type64='^{__CFTimeZone=}'/>
</function>
<function name='CFTimeZoneCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFData=}'/>
<retval already_retained='true' type64='^{__CFTimeZone=}'/>
</function>
<function name='CFTimeZoneCreateWithName'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='B'/>
<retval already_retained='true' type64='^{__CFTimeZone=}'/>
</function>
<function name='CFTimeZoneCreateWithTimeIntervalFromGMT'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='d'/>
<retval already_retained='true' type64='^{__CFTimeZone=}'/>
</function>
<function name='CFTimeZoneGetData'>
<arg type64='^{__CFTimeZone=}'/>
<retval type64='^{__CFData=}'/>
</function>
<function name='CFTimeZoneGetDaylightSavingTimeOffset'>
<arg type64='^{__CFTimeZone=}'/>
<arg type64='d'/>
<retval type64='d'/>
</function>
<function name='CFTimeZoneGetName'>
<arg type64='^{__CFTimeZone=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFTimeZoneGetNextDaylightSavingTimeTransition'>
<arg type64='^{__CFTimeZone=}'/>
<arg type64='d'/>
<retval type64='d'/>
</function>
<function name='CFTimeZoneGetSecondsFromGMT'>
<arg type64='^{__CFTimeZone=}'/>
<arg type64='d'/>
<retval type64='d'/>
</function>
<function name='CFTimeZoneGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFTimeZoneIsDaylightSavingTime'>
<arg type64='^{__CFTimeZone=}'/>
<arg type64='d'/>
<retval type64='B'/>
</function>
<function name='CFTimeZoneResetSystem'>
<retval type64='v'/>
</function>
<function name='CFTimeZoneSetAbbreviationDictionary'>
<arg type64='^{__CFDictionary=}'/>
<retval type64='v'/>
</function>
<function name='CFTimeZoneSetDefault'>
<arg type64='^{__CFTimeZone=}'/>
<retval type64='v'/>
</function>
<function name='CFTreeAppendChild'>
<arg type64='^{__CFTree=}'/>
<arg type64='^{__CFTree=}'/>
<retval type64='v'/>
</function>
<function name='CFTreeApplyFunctionToChildren'>
<arg type64='^{__CFTree=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFTreeCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{_CFTreeContext=q^v^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFTree=}'/>
</function>
<function name='CFTreeFindRoot'>
<arg type64='^{__CFTree=}'/>
<retval type64='^{__CFTree=}'/>
</function>
<function name='CFTreeGetChildAtIndex'>
<arg type64='^{__CFTree=}'/>
<arg type64='q'/>
<retval type64='^{__CFTree=}'/>
</function>
<function name='CFTreeGetChildCount'>
<arg type64='^{__CFTree=}'/>
<retval type64='q'/>
</function>
<function name='CFTreeGetChildren'>
<arg type64='^{__CFTree=}'/>
<arg c_array_of_variable_length='true' type64='^^{__CFTree}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFTreeGetContext'>
<arg type64='^{__CFTree=}'/>
<arg type64='^{_CFTreeContext=q^v^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFTreeGetFirstChild'>
<arg type64='^{__CFTree=}'/>
<retval type64='^{__CFTree=}'/>
</function>
<function name='CFTreeGetNextSibling'>
<arg type64='^{__CFTree=}'/>
<retval type64='^{__CFTree=}'/>
</function>
<function name='CFTreeGetParent'>
<arg type64='^{__CFTree=}'/>
<retval type64='^{__CFTree=}'/>
</function>
<function name='CFTreeGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFTreeInsertSibling'>
<arg type64='^{__CFTree=}'/>
<arg type64='^{__CFTree=}'/>
<retval type64='v'/>
</function>
<function name='CFTreePrependChild'>
<arg type64='^{__CFTree=}'/>
<arg type64='^{__CFTree=}'/>
<retval type64='v'/>
</function>
<function name='CFTreeRemove'>
<arg type64='^{__CFTree=}'/>
<retval type64='v'/>
</function>
<function name='CFTreeRemoveAllChildren'>
<arg type64='^{__CFTree=}'/>
<retval type64='v'/>
</function>
<function name='CFTreeSetContext'>
<arg type64='^{__CFTree=}'/>
<arg type64='^{_CFTreeContext=q^v^?^?^?}' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFTreeSortChildren'>
<arg type64='^{__CFTree=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^v'/>
<arg type64='^v'/>
<arg type64='^v'/>
<retval type64='q'/>
</arg>
<arg type64='^v' type_modifier='n'/>
<retval type64='v'/>
</function>
<function name='CFURLCanBeDecomposed'>
<arg type64='^{__CFURL=}'/>
<retval type64='B'/>
</function>
<function name='CFURLClearResourcePropertyCache'>
<arg type64='^{__CFURL=}'/>
<retval type64='v'/>
</function>
<function name='CFURLClearResourcePropertyCacheForKey'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFURLCopyAbsoluteURL'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCopyFileSystemPath'>
<arg type64='^{__CFURL=}'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyFragment'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyHostName'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyLastPathComponent'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyNetLocation'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyParameterString'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyPassword'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyPath'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyPathExtension'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyQueryString'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyResourcePropertiesForKeys'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFArray=}'/>
<arg type64='^^{__CFError}'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFURLCopyResourcePropertyForKey'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^v'/>
<arg type64='^^{__CFError}'/>
<retval type64='B'/>
</function>
<function name='CFURLCopyResourceSpecifier'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyScheme'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyStrictPath'>
<arg type64='^{__CFURL=}'/>
<arg type64='^B' type_modifier='o'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCopyUserName'>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCreateAbsoluteURLWithBytes'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<arg type64='I'/>
<arg type64='^{__CFURL=}'/>
<arg type64='B'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateBookmarkData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='Q'/>
<arg type64='^{__CFArray=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^^{__CFError}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFURLCreateBookmarkDataFromAliasRecord'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFData=}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFURLCreateBookmarkDataFromFile'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^^{__CFError}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFURLCreateByResolvingBookmarkData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='Q'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFArray=}'/>
<arg type64='^B'/>
<arg type64='^^{__CFError}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateCopyAppendingPathComponent'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='B'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateCopyAppendingPathExtension'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateCopyDeletingLastPathComponent'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateCopyDeletingPathExtension'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='I'/>
<arg type64='B'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFURLCreateDataAndPropertiesFromResource'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^^{__CFData}' type_modifier='o'/>
<arg type64='^^{__CFDictionary}' type_modifier='o'/>
<arg type64='^{__CFArray=}'/>
<arg type64='^i' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFURLCreateFilePathURL'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^^{__CFError}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateFileReferenceURL'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^^{__CFError}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateFromFSRef'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{FSRef=}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateFromFileSystemRepresentation'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<arg type64='B'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateFromFileSystemRepresentationRelativeToBase'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<arg type64='B'/>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreatePropertyFromResource'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^i' type_modifier='o'/>
<retval already_retained='true' type64='@'/>
</function>
<function name='CFURLCreateResourcePropertiesForKeysFromBookmarkData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFArray=}'/>
<arg type64='^{__CFData=}'/>
<retval already_retained='true' type64='^{__CFDictionary=}'/>
</function>
<function name='CFURLCreateResourcePropertyForKeyFromBookmarkData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFData=}'/>
<retval already_retained='true' type64='@'/>
</function>
<function name='CFURLCreateStringByAddingPercentEscapes'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='I'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCreateStringByReplacingPercentEscapes'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCreateStringByReplacingPercentEscapesUsingEncoding'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='I'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFURLCreateWithBytes'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<arg type64='I'/>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateWithFileSystemPath'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<arg type64='B'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateWithFileSystemPathRelativeToBase'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<arg type64='B'/>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLCreateWithString'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFURL=}'/>
</function>
<function name='CFURLDestroyResource'>
<arg type64='^{__CFURL=}'/>
<arg type64='^i' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFURLEnumeratorCreateForDirectoryURL'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='Q'/>
<arg type64='^{__CFArray=}'/>
<retval already_retained='true' type64='^{__CFURLEnumerator=}'/>
</function>
<function name='CFURLEnumeratorCreateForMountedVolumes'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='Q'/>
<arg type64='^{__CFArray=}'/>
<retval already_retained='true' type64='^{__CFURLEnumerator=}'/>
</function>
<function name='CFURLEnumeratorGetDescendentLevel'>
<arg type64='^{__CFURLEnumerator=}'/>
<retval type64='q'/>
</function>
<function name='CFURLEnumeratorGetNextURL'>
<arg type64='^{__CFURLEnumerator=}'/>
<arg type64='^^{__CFURL}'/>
<arg type64='^^{__CFError}'/>
<retval type64='q'/>
</function>
<function name='CFURLEnumeratorGetSourceDidChange'>
<arg type64='^{__CFURLEnumerator=}'/>
<retval type64='B'/>
</function>
<function name='CFURLEnumeratorGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFURLEnumeratorSkipDescendents'>
<arg type64='^{__CFURLEnumerator=}'/>
<retval type64='v'/>
</function>
<function name='CFURLGetBaseURL'>
<arg type64='^{__CFURL=}'/>
<retval type64='^{__CFURL=}'/>
</function>
<function name='CFURLGetByteRangeForComponent'>
<arg type64='^{__CFURL=}'/>
<arg type64='q'/>
<arg type64='^{_CFRange=qq}' type_modifier='o'/>
<retval type64='{_CFRange=qq}'/>
</function>
<function name='CFURLGetBytes'>
<arg type64='^{__CFURL=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='o'/>
<arg type64='q'/>
<retval type64='q'/>
</function>
<function name='CFURLGetFSRef'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{FSRef=}' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFURLGetFileSystemRepresentation'>
<arg type64='^{__CFURL=}'/>
<arg type64='B'/>
<arg c_array_length_in_arg='3' type64='*' type_modifier='o'/>
<arg type64='q'/>
<retval type64='B'/>
</function>
<function name='CFURLGetPortNumber'>
<arg type64='^{__CFURL=}'/>
<retval type64='i'/>
</function>
<function name='CFURLGetString'>
<arg type64='^{__CFURL=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFURLGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFURLHasDirectoryPath'>
<arg type64='^{__CFURL=}'/>
<retval type64='B'/>
</function>
<function name='CFURLIsFileReferenceURL'>
<arg type64='^{__CFURL=}'/>
<retval type64='B'/>
</function>
<function name='CFURLResourceIsReachable'>
<arg type64='^{__CFURL=}'/>
<arg type64='^^{__CFError}'/>
<retval type64='B'/>
</function>
<function name='CFURLSetResourcePropertiesForKeys'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^^{__CFError}'/>
<retval type64='B'/>
</function>
<function name='CFURLSetResourcePropertyForKey'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='@'/>
<arg type64='^^{__CFError}'/>
<retval type64='B'/>
</function>
<function name='CFURLSetTemporaryResourcePropertyForKey'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='@'/>
<retval type64='v'/>
</function>
<function name='CFURLStartAccessingSecurityScopedResource'>
<arg type64='^{__CFURL=}'/>
<retval type64='B'/>
</function>
<function name='CFURLStopAccessingSecurityScopedResource'>
<arg type64='^{__CFURL=}'/>
<retval type64='v'/>
</function>
<function name='CFURLWriteBookmarkDataToFile'>
<arg type64='^{__CFData=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='Q'/>
<arg type64='^^{__CFError}'/>
<retval type64='B'/>
</function>
<function name='CFURLWriteDataAndPropertiesToResource'>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='^{__CFDictionary=}'/>
<arg type64='^i' type_modifier='o'/>
<retval type64='B'/>
</function>
<function name='CFUUIDCreate'>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFUUID=}'/>
</function>
<function name='CFUUIDCreateFromString'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='^{__CFUUID=}'/>
</function>
<function name='CFUUIDCreateFromUUIDBytes'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='{_CFUUIDBytes=CCCCCCCCCCCCCCCC}'/>
<retval already_retained='true' type64='^{__CFUUID=}'/>
</function>
<function name='CFUUIDCreateString'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFUUID=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFUUIDCreateWithBytes'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<retval already_retained='true' type64='^{__CFUUID=}'/>
</function>
<function name='CFUUIDGetConstantUUIDWithBytes'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<arg type64='C'/>
<retval type64='^{__CFUUID=}'/>
</function>
<function name='CFUUIDGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFUUIDGetUUIDBytes'>
<arg type64='^{__CFUUID=}'/>
<retval type64='{_CFUUIDBytes=CCCCCCCCCCCCCCCC}'/>
</function>
<function name='CFUserNotificationCancel'>
<arg type64='^{__CFUserNotification=}'/>
<retval type64='i'/>
</function>
<function inline='true' name='CFUserNotificationCheckBoxChecked'>
<arg type64='q'/>
<retval type64='Q'/>
</function>
<function name='CFUserNotificationCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='d'/>
<arg type64='Q'/>
<arg type64='^i' type_modifier='o'/>
<arg type64='^{__CFDictionary=}'/>
<retval already_retained='true' type64='^{__CFUserNotification=}'/>
</function>
<function name='CFUserNotificationCreateRunLoopSource'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFUserNotification=}'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFUserNotification=}'/>
<arg type64='Q'/>
<retval type64='v'/>
</arg>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFRunLoopSource=}'/>
</function>
<function name='CFUserNotificationDisplayAlert'>
<arg type64='d'/>
<arg type64='Q'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^Q' type_modifier='o'/>
<retval type64='i'/>
</function>
<function name='CFUserNotificationDisplayNotice'>
<arg type64='d'/>
<arg type64='Q'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='i'/>
</function>
<function name='CFUserNotificationGetResponseDictionary'>
<arg type64='^{__CFUserNotification=}'/>
<retval type64='^{__CFDictionary=}'/>
</function>
<function name='CFUserNotificationGetResponseValue'>
<arg type64='^{__CFUserNotification=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='q'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFUserNotificationGetTypeID'>
<retval type64='Q'/>
</function>
<function inline='true' name='CFUserNotificationPopUpSelection'>
<arg type64='q'/>
<retval type64='Q'/>
</function>
<function name='CFUserNotificationReceiveResponse'>
<arg type64='^{__CFUserNotification=}'/>
<arg type64='d'/>
<arg type64='^Q' type_modifier='o'/>
<retval type64='i'/>
</function>
<function inline='true' name='CFUserNotificationSecureTextField'>
<arg type64='q'/>
<retval type64='Q'/>
</function>
<function name='CFUserNotificationUpdate'>
<arg type64='^{__CFUserNotification=}'/>
<arg type64='d'/>
<arg type64='Q'/>
<arg type64='^{__CFDictionary=}'/>
<retval type64='i'/>
</function>
<function name='CFWriteStreamCanAcceptBytes'>
<arg type64='^{__CFWriteStream=}'/>
<retval type64='B'/>
</function>
<function name='CFWriteStreamClose'>
<arg type64='^{__CFWriteStream=}'/>
<retval type64='v'/>
</function>
<function name='CFWriteStreamCopyDispatchQueue'>
<arg type64='^{__CFWriteStream=}'/>
<retval type64='@'/>
</function>
<function name='CFWriteStreamCopyError'>
<arg type64='^{__CFWriteStream=}'/>
<retval already_retained='true' type64='^{__CFError=}'/>
</function>
<function name='CFWriteStreamCopyProperty'>
<arg type64='^{__CFWriteStream=}'/>
<arg type64='^{__CFString=}'/>
<retval already_retained='true' type64='@'/>
</function>
<function name='CFWriteStreamCreateWithAllocatedBuffers'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFAllocator=}'/>
<retval already_retained='true' type64='^{__CFWriteStream=}'/>
</function>
<function name='CFWriteStreamCreateWithBuffer'>
<arg type64='^{__CFAllocator=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFWriteStream=}'/>
</function>
<function name='CFWriteStreamCreateWithFile'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<retval already_retained='true' type64='^{__CFWriteStream=}'/>
</function>
<function name='CFWriteStreamGetError'>
<arg type64='^{__CFWriteStream=}'/>
<retval type64='{_CFStreamError=qi}'/>
</function>
<function name='CFWriteStreamGetStatus'>
<arg type64='^{__CFWriteStream=}'/>
<retval type64='q'/>
</function>
<function name='CFWriteStreamGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFWriteStreamOpen'>
<arg type64='^{__CFWriteStream=}'/>
<retval type64='B'/>
</function>
<function name='CFWriteStreamScheduleWithRunLoop'>
<arg type64='^{__CFWriteStream=}'/>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFWriteStreamSetClient'>
<arg type64='^{__CFWriteStream=}'/>
<arg type64='Q'/>
<arg function_pointer='true' type64='^?' type_modifier='n'>
<arg type64='^{__CFWriteStream=}'/>
<arg type64='Q'/>
<arg type64='^v'/>
<retval type64='v'/>
</arg>
<arg type64='^{_CFStreamClientContext=q^v^?^?^?}' type_modifier='n'/>
<retval type64='B'/>
</function>
<function name='CFWriteStreamSetDispatchQueue'>
<arg type64='^{__CFWriteStream=}'/>
<arg type64='@'/>
<retval type64='v'/>
</function>
<function name='CFWriteStreamSetProperty'>
<arg type64='^{__CFWriteStream=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='@'/>
<retval type64='B'/>
</function>
<function name='CFWriteStreamUnscheduleFromRunLoop'>
<arg type64='^{__CFWriteStream=}'/>
<arg type64='^{__CFRunLoop=}'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFWriteStreamWrite'>
<arg type64='^{__CFWriteStream=}'/>
<arg c_array_length_in_arg='2' type64='*' type_modifier='n'/>
<arg type64='q'/>
<retval type64='q'/>
</function>
<function name='CFXMLCreateStringByEscapingEntities'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFDictionary=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFXMLCreateStringByUnescapingEntities'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFString=}'/>
<arg type64='^{__CFDictionary=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFXMLNodeCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='q'/>
<arg type64='^{__CFString=}'/>
<arg type64='^v' type_modifier='n'/>
<arg type64='q'/>
<retval already_retained='true' type64='^{__CFXMLNode=}'/>
</function>
<function name='CFXMLNodeCreateCopy'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFXMLNode=}'/>
<retval already_retained='true' type64='^{__CFXMLNode=}'/>
</function>
<function name='CFXMLNodeGetInfoPtr'>
<arg type64='^{__CFXMLNode=}'/>
<retval type64='^v'/>
</function>
<function name='CFXMLNodeGetString'>
<arg type64='^{__CFXMLNode=}'/>
<retval type64='^{__CFString=}'/>
</function>
<function name='CFXMLNodeGetTypeCode'>
<arg type64='^{__CFXMLNode=}'/>
<retval type64='q'/>
</function>
<function name='CFXMLNodeGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFXMLNodeGetVersion'>
<arg type64='^{__CFXMLNode=}'/>
<retval type64='q'/>
</function>
<function name='CFXMLParserAbort'>
<arg type64='^{__CFXMLParser=}'/>
<arg type64='q'/>
<arg type64='^{__CFString=}'/>
<retval type64='v'/>
</function>
<function name='CFXMLParserCopyErrorDescription'>
<arg type64='^{__CFXMLParser=}'/>
<retval already_retained='true' type64='^{__CFString=}'/>
</function>
<function name='CFXMLParserCreate'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='Q'/>
<arg type64='q'/>
<arg type64='^{_CFXMLParserCallBacks=q^?^?^?^?^?}' type_modifier='n'/>
<arg type64='^{_CFXMLParserContext=q^v^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFXMLParser=}'/>
</function>
<function name='CFXMLParserCreateWithDataFromURL'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='Q'/>
<arg type64='q'/>
<arg type64='^{_CFXMLParserCallBacks=q^?^?^?^?^?}' type_modifier='n'/>
<arg type64='^{_CFXMLParserContext=q^v^?^?^?}' type_modifier='n'/>
<retval already_retained='true' type64='^{__CFXMLParser=}'/>
</function>
<function name='CFXMLParserGetCallBacks'>
<arg type64='^{__CFXMLParser=}'/>
<arg type64='^{_CFXMLParserCallBacks=q^?^?^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFXMLParserGetContext'>
<arg type64='^{__CFXMLParser=}'/>
<arg type64='^{_CFXMLParserContext=q^v^?^?^?}' type_modifier='o'/>
<retval type64='v'/>
</function>
<function name='CFXMLParserGetDocument'>
<arg type64='^{__CFXMLParser=}'/>
<retval type64='^v'/>
</function>
<function name='CFXMLParserGetLineNumber'>
<arg type64='^{__CFXMLParser=}'/>
<retval type64='q'/>
</function>
<function name='CFXMLParserGetLocation'>
<arg type64='^{__CFXMLParser=}'/>
<retval type64='q'/>
</function>
<function name='CFXMLParserGetSourceURL'>
<arg type64='^{__CFXMLParser=}'/>
<retval type64='^{__CFURL=}'/>
</function>
<function name='CFXMLParserGetStatusCode'>
<arg type64='^{__CFXMLParser=}'/>
<retval type64='q'/>
</function>
<function name='CFXMLParserGetTypeID'>
<retval type64='Q'/>
</function>
<function name='CFXMLParserParse'>
<arg type64='^{__CFXMLParser=}'/>
<retval type64='B'/>
</function>
<function name='CFXMLTreeCreateFromData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='Q'/>
<arg type64='q'/>
<retval type64='^{__CFTree=}'/>
</function>
<function name='CFXMLTreeCreateFromDataWithError'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFData=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='Q'/>
<arg type64='q'/>
<arg type64='^^{__CFDictionary}' type_modifier='o'/>
<retval type64='^{__CFTree=}'/>
</function>
<function name='CFXMLTreeCreateWithDataFromURL'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFURL=}'/>
<arg type64='Q'/>
<arg type64='q'/>
<retval type64='^{__CFTree=}'/>
</function>
<function name='CFXMLTreeCreateWithNode'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFXMLNode=}'/>
<retval type64='^{__CFTree=}'/>
</function>
<function name='CFXMLTreeCreateXMLData'>
<arg type64='^{__CFAllocator=}'/>
<arg type64='^{__CFTree=}'/>
<retval already_retained='true' type64='^{__CFData=}'/>
</function>
<function name='CFXMLTreeGetNode'>
<arg type64='^{__CFTree=}'/>
<retval type64='^{__CFXMLNode=}'/>
</function>
</signatures>
