plugins {
    id 'java'
    id 'org.springframework.boot' version '3.1.0'
    id 'io.spring.dependency-management' version '1.1.0'
    id 'org.openjfx.javafxplugin' version '0.0.13'
    id 'maven-publish'
    id "io.freefair.lombok" version "6.5.0.3"
    id 'checkstyle'
    id 'com.github.johnrengelman.shadow' version '7.0.0'
    id 'jacoco'
}

group = 'com.phonecheck'
version = '0.0.1-SNAPSHOT'

repositories {
    mavenLocal()
    mavenCentral()
}


import com.github.jengelman.gradle.plugins.shadow.transformers.PropertiesFileTransformer

shadowJar {
    archiveBaseName = "${project.name}"
    archiveVersion = "${project.version}"
    archiveClassifier = 'fat'
    mergeServiceFiles()
    append 'META-INF/spring.handlers'
    append 'META-INF/spring.schemas'
    append 'META-INF/spring.tooling'
    transform(PropertiesFileTransformer) {
        paths = ['META-INF/spring.factories']
        mergeStrategy = "append"
    }
    manifest {
        attributes 'Main-Class': "com.phonecheck.ui.MultiModuleApplication"
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web'
//    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    implementation 'org.apache.commons:commons-lang3:3.12.0'
    implementation 'org.apache.poi:poi-ooxml:4.1.1'
    implementation 'org.json:json:20231013'

    implementation 'org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.5'
    implementation 'io.netty:netty-codec-mqtt:4.1.78.Final'
    implementation 'org.controlsfx:controlsfx:11.2.1'
    implementation 'com.jfoenix:jfoenix:9.0.1'
    implementation 'de.jensd:fontawesomefx-materialdesignfont:2.0.26-9.1.2'
    implementation 'org.openjfx:javafx-web:20.0.1'
    implementation group: 'com.google.code.gson', name: 'gson', version: '2.10.1'

    implementation group: 'net.sourceforge.barbecue', name: 'barbecue', version: '1.5-beta1'
    implementation "com.google.zxing:core:3.3.0"

    implementation project(':model')
    implementation project(':mqtt')
    implementation project(":executor")
    implementation project(":commands-module")
    implementation project(":communicator")

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "org.testfx:testfx-core:4.0.16-alpha"
    testImplementation "org.testfx:testfx-junit5:4.0.16-alpha"
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testImplementation 'org.testfx:testfx-core:4.0.16-alpha'
    testImplementation 'org.testfx:testfx-junit5:4.0.16-alpha'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'
    testImplementation 'org.mockito:mockito-core:4.6.1'
    testImplementation 'org.mockito:mockito-junit-jupiter:4.6.1'

    //    starting dependencies required by the airpods sdk
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'com.h2database:h2'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    //    ending dependencies required by the airpods sdk
}

test {
    useJUnitPlatform()
    finalizedBy(jacocoTestReport)
    jvmArgs '--add-exports', 'javafx.graphics/com.sun.javafx.application=ALL-UNNAMED'
}

javafx {
    modules = ['javafx.controls', 'javafx.fxml', 'javafx.web', 'javafx.swing']
    version = '19.0.2.1'
}


jacoco {
    toolVersion = "0.8.12"
}

jacocoTestReport {
    reports {
        xml.required = false
        csv.required = false
        html.outputLocation = layout.buildDirectory.dir("$buildDir/reports/jacoco")
    }
}

jacocoTestCoverageVerification {
    dependsOn jacocoTestReport // tests are required to run before generating the report
    violationRules {
        rule {
            limit {
                minimum = 0.0
            }
        }
    }
}

check.dependsOn(jacocoTestCoverageVerification)
