<?xml version="1.0" encoding="UTF-8"?>

<?import java.net.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox fx:id="root" alignment="TOP_CENTER" prefHeight="290.0" prefWidth="623.0" style="-fx-background-color: white;" xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.phonecheck.ui.controller.print.labels.TestResultsLabelLandscapeController">
        <stylesheets>
            <URL value="@../../style/branded-label.css" />
        </stylesheets>
        <VBox maxHeight="-Infinity" maxWidth="-Infinity" spacing="10.0">
            <VBox fx:id="titleLayout" alignment="TOP_CENTER" prefHeight="22.0" prefWidth="609.0" style="-fx-border-color: black; -fx-border-width: 2;">
                <HBox alignment="TOP_CENTER" spacing="3.0">
                    <Text fx:id="titleText" strokeType="OUTSIDE" strokeWidth="0.0" text="%title" textAlignment="CENTER" wrappingWidth="599.75">
                        <font>
                            <Font name="Avenir Black" size="18.0" />
                        </font>
                        <HBox.margin>
                            <Insets />
                        </HBox.margin>
                    </Text>
                </HBox>

            </VBox>
            <VBox fx:id="testLayout" maxHeight="-Infinity" minHeight="-Infinity" prefHeight="180.0" prefWidth="609.0">
                <VBox.margin>
                    <Insets top="0.0" />
                </VBox.margin>
                <children>
                    <FlowPane fx:id="testResultsContainer" maxWidth="609.0" minWidth="609.0" prefWidth="609.0" prefWrapLength="300.0" />
                </children></VBox>
            <HBox fx:id="functionalityLayout">
                <children>
                    <Text strokeType="OUTSIDE" strokeWidth="0.0" text="%functionality" />
                    <Text fx:id="functionality" strokeType="OUTSIDE" strokeWidth="0.0" text="%fullFuctionalOrSeeNotes">
                        <HBox.margin>
                            <Insets left="10.0" />
                        </HBox.margin>
                    </Text>
                </children>
            </HBox>
            <HBox>
                <VBox fx:id="imeiElementLayout" alignment="CENTER" prefWidth="375.0" spacing="0.0">
                            <children>
                                <ImageView fx:id="imeiBarcodeImageView" fitHeight="30.0" fitWidth="374.0" pickOnBounds="true">
                                    <image>
                                    </image>
                                    <VBox.margin>
                                        <Insets />
                                    </VBox.margin>
                                </ImageView>
                                <Text fx:id="imeiElementText" strokeType="OUTSIDE" strokeWidth="0.0" text="%imei">
                                    <font>
                                        <Font name="Tahoma" size="16.0" />
                                    </font>
                        <HBox.margin>
                           <Insets bottom="11.0" left="-5.0" />
                        </HBox.margin>
                                </Text>
                            </children>
                            <HBox.margin>
                                <Insets bottom="11.0" left="120.0" />
                            </HBox.margin>
                        </VBox>


                <HBox fx:id="portNumberLayout" alignment="BOTTOM_RIGHT" spacing="4.0" visible="false">
                    <children>
                        <Text fx:id="portNumberHeading" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular" text="%port">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                        <Text fx:id="portNumber" strokeType="OUTSIDE" strokeWidth="0.0" styleClass="text-regular">
                            <font>
                                <Font size="18.0" />
                            </font>
                        </Text>
                    </children>
                    <HBox.margin>
                        <Insets bottom="11.0" />
                    </HBox.margin>
                </HBox>

                    <VBox.margin>
                        <Insets bottom="1.0" />
                    </VBox.margin>
            </HBox>
            <VBox.margin>
                <Insets />
            </VBox.margin>
            <padding>
                <Insets left="10.0" right="4.0" />
            </padding>
        </VBox>
    </VBox>
