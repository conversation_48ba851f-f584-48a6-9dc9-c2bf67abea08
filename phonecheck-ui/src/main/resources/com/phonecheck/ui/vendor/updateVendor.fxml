<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>
<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="248.0"
            prefWidth="426.0" xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1"
            fx:controller="com.phonecheck.ui.controller.vendor.VendorUpdateController">
    <children>
        <VBox fx:id="loaderViewVendor" alignment="CENTER" prefHeight="248.0" prefWidth="426.0" spacing="8.0">
            <ProgressIndicator prefHeight="100" prefWidth="100"/>
        </VBox>
        <VBox fx:id="mainViewVendor" visible="false" prefHeight="248.0" prefWidth="426.0">
            <children>
                <Label layoutX="29.0" layoutY="22.0" prefHeight="17.0" prefWidth="318.0"
                       text="%enterVendorInvoiceInfo">
                    <font>
                        <Font name="System Bold" size="16.0"/>
                    </font>
                </Label>
                <GridPane layoutX="29.0" layoutY="55.0" prefHeight="123.0" prefWidth="274.0">
                    <columnConstraints>
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="182.0" minWidth="10.0" prefWidth="68.0"/>
                        <ColumnConstraints hgrow="SOMETIMES" maxWidth="313.0" minWidth="10.0" prefWidth="269.0"/>
                    </columnConstraints>
                    <rowConstraints>
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
                        <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES"/>
                    </rowConstraints>
                    <children>
                        <ComboBox fx:id="vendorComboBox" editable="true" prefHeight="27.0" prefWidth="177.0"
                                  promptText="%vendorName" GridPane.columnIndex="1"/>
                        <ComboBox fx:id="invoiceComboBox" editable="true" prefHeight="27.0" prefWidth="177.0"
                                  promptText="%invoiceNo" GridPane.columnIndex="1" GridPane.rowIndex="1"/>
                        <TextField fx:id="qtyTextField" maxWidth="177.0" prefHeight="27.0" prefWidth="157.0"
                                   promptText="%qty" GridPane.columnIndex="1" GridPane.rowIndex="2"/>
                        <TextField fx:id="boxTextField" maxWidth="177.0" prefHeight="26.0" prefWidth="258.0"
                                   promptText="%boxNo" GridPane.columnIndex="1" GridPane.rowIndex="3"/>
                        <Label prefHeight="17.0" prefWidth="86.0" text="%vendorName"/>
                        <Label prefHeight="17.0" prefWidth="76.0" text="%invoiceNo" GridPane.rowIndex="1"/>
                        <Label prefHeight="17.0" prefWidth="55.0" text="%qty" GridPane.rowIndex="2"/>
                        <Label prefHeight="17.0" prefWidth="61.0" text="%boxNo" GridPane.rowIndex="3"/>
                    </children>
                    <VBox.margin>
                        <Insets top="10.0"/>
                    </VBox.margin>
                </GridPane>
                <Button fx:id="submitButton" disable="true" layoutX="333.0" layoutY="207.0" mnemonicParsing="false"
                        onMouseClicked="#onSubmitButtonClicked" prefHeight="27.0" prefWidth="68.0" text="%submit">
                    <VBox.margin>
                        <Insets left="326.0" top="34.0"/>
                    </VBox.margin>
                </Button>
            </children>
            <padding>
                <Insets left="20.0" top="22.0"/>
            </padding>
        </VBox>
    </children>
</AnchorPane>
