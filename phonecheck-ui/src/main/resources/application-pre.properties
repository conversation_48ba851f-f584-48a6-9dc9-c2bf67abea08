server.port=8083
# application properties
applicationName=Phonecheck
applicationVersion=3.12.1c
# mqtt configurations
mqtt.scheme=tcp://
mqtt.host=127.0.0.1
mqtt.port=1883
mqtt.server.enabled=false
mqtt.autoReconnect=true
mqtt.maxReconnectDelay=10
mqtt.connectionTimeout=3600
mqtt.keepAlive=120
mqtt.maxInFlight=32000
mqtt.client.id=phonecheck-frontend
# logging configurations
logging.level.com.phonecheck=INFO
logging.path=${user.home}/Library/Application Support/PhoneCheck3/logs
# spring configurations
spring.main.lazy-initialization=true
spring.main.web-application-type=servlet
spring.jmx.enabled=false
spring.task.execution.pool.core-size=64
spring.task.execution.pool.max-size=1024
spring.task.execution.pool.queue-capacity=1024
spring.task.execution.thread-name-prefix=ExecutorThread-
server.tomcat.max-threads=2048
management.endpoints.web.exposure.include=*
# communicator properties
communicator.redis.port=6379
communicator.redis.host=localhost
cloud3.base.url=${CLOUD3_BASE_URL}
