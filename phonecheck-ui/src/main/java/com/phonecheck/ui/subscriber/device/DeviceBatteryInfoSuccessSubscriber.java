package com.phonecheck.ui.subscriber.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.event.device.DeviceBatteryInfoSuccessEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceBatteryInfoSuccessMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Subscribes to battery/info/collection/success mqtt topic for observing
 * battery info collection success event in UI App
 */
@Component
@AllArgsConstructor
public class DeviceBatteryInfoSuccessSubscriber extends AbstractMqttTopicSubscriber {

    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceBatteryInfoSuccessSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS,
                "battery", "info", "collection", "success"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID,
                        "battery", "info", "collection", "success")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        try {
            final DeviceBatteryInfoSuccessMessage request = mapper.readValue(payload,
                    DeviceBatteryInfoSuccessMessage.class);

            setDeviceIdInMDC(request.getId());
            LOGGER.info("Device battery info success message: {}", payload);

            final Device device = new Device() {
            };
            device.setId(request.getId());
            device.setBatteryInfo(request.getBatteryInfo());
            device.setBatteryStateHealth(request.getBatteryStateHealth());
            device.setBatteryPercentage(request.getBatteryPercentage());
            device.setStage(DeviceStage.BATTERY_INFO_COLLECTION_SUCCESS);
            device.setBatteryDegraded(request.getBatteryDegraded());

            final DeviceBatteryInfoSuccessEvent event = new DeviceBatteryInfoSuccessEvent(this, device);
            eventPublisher.publishEvent(event);
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
