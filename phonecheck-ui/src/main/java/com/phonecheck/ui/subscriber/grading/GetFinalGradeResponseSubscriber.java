package com.phonecheck.ui.subscriber.grading;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.grading.GetFinalGradeResponseEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.GetFinalGradeResponseMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/***
 * Subscribes to final grade response from backend.
 */
@Component
@AllArgsConstructor
public class GetFinalGradeResponseSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetFinalGradeResponseSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.IOS,
                "get", "final", "grade", "response"),
                TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID,
                        "get", "final", "grade", "response")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object
        try {
            final GetFinalGradeResponseMessage request = mapper.readValue(payload,
                    GetFinalGradeResponseMessage.class);

            setDeviceIdInMDC(request.getId());
            LOGGER.debug("Get final grade response message: {}", msg.getPayload());

            final Device device = new Device() {
            };
            device.setId(request.getId());

            eventPublisher.publishEvent(new GetFinalGradeResponseEvent(this, device, request.getFinalGrade()));
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}
