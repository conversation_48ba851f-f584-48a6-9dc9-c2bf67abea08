package com.phonecheck.ui.menu;

import com.phonecheck.model.device.RunningMode;
import com.phonecheck.model.phonecheckapi.SelectPackageResponse;
import com.phonecheck.model.store.UiInMemoryStore;
import com.phonecheck.model.util.LocalizationService;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.ui.controller.MainController;
import javafx.scene.control.Menu;
import javafx.scene.control.MenuBar;
import javafx.scene.control.MenuItem;
import javafx.scene.control.SeparatorMenuItem;
import javafx.scene.layout.BorderPane;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Data
public class DesktopAppMenu {
    private static final Logger LOGGER = LoggerFactory.getLogger(DesktopAppMenu.class);
    private static final String APP_NAME = "PhoneCheck";
    private final MainController mainController;
    private MenuBar menuBar;
    private final UiInMemoryStore uiInMemoryStore;
    private final LocalizationService localizationService;
    private final OsChecker osChecker;

    /**
     * Initialize phonecheck app menu
     *
     * @return Menu
     */
    private Menu initPhoneCheckMenu() {
        // TODO this must be set through installer
        Menu desktopAppMenu = new Menu(APP_NAME);
        MenuItem aboutItem = new MenuItem(localizationService.getLanguageSpecificText("about") + APP_NAME);
        MenuItem quitMenuItem = new MenuItem(localizationService.getLanguageSpecificText("quit") + APP_NAME);
        desktopAppMenu.getItems().addAll(
                aboutItem,
                new SeparatorMenuItem(),
                quitMenuItem
        );

        return desktopAppMenu;
    }

    /**
     * Initialize action menu
     *
     * @return Menu
     */
    private Menu initActionMenu() {
        LOGGER.debug("Adding Actions menu");
        // Actions
        Menu actionsMenu = new Menu(localizationService.getLanguageSpecificText("actions"));
        MenuItem portMappingItem = new MenuItem(localizationService.getLanguageSpecificText("portMapping"));
        MenuItem manualEntryItem = new MenuItem(localizationService.getLanguageSpecificText("manualEntry"));
        MenuItem killAdbProcessItem = new MenuItem(localizationService.getLanguageSpecificText("killAdbProcess"));
        portMappingItem.setOnAction(actionEvent -> mainController.onPortMappingAction());
        manualEntryItem.setOnAction(actionEvent -> mainController.onManualEntryAction());
        killAdbProcessItem.setOnAction(actionEvent -> mainController.onKillAdbProcess());
        actionsMenu.getItems().addAll(
                portMappingItem,
                manualEntryItem,
                killAdbProcessItem
        );
        if (osChecker.isMac()) {
            MenuItem restartUsbMuxDItem = new MenuItem(localizationService.getLanguageSpecificText("restartUsbmuxd"));
            restartUsbMuxDItem.setOnAction(actionEvent -> mainController.onRestartUsbmuxD());
            actionsMenu.getItems().add(restartUsbMuxDItem);
        }
        return actionsMenu;
    }

    /**
     * Creates menu and sub menu as View -> Appearance -> Light Background and Dark Background
     *
     * @return viewMenu
     */
    private Menu initViewMenu() {
        LOGGER.debug("Adding View menu");
        // View Menu
        Menu viewMenu = new Menu(localizationService.getLanguageSpecificText("view"));
        Menu subMenu = new Menu(localizationService.getLanguageSpecificText("appearance"));
        viewMenu.getItems().add(subMenu);

        return viewMenu;
    }

    /**
     * Initialize transaction menu
     *
     * @return Menu
     */
    private Menu initTransactionMenu() {
        LOGGER.debug("Adding Transactions menu");
        // Transactions
        Menu transactionsMenu = new Menu(localizationService.getLanguageSpecificText("transactions"));
        MenuItem newTransactionMenuItem = new MenuItem(localizationService.
                getLanguageSpecificText("newTransaction"));
        MenuItem viewTransactionMenuItem = new MenuItem(localizationService.
                getLanguageSpecificText("viewTransaction"));
        MenuItem transactionHistoryMenuItem = new MenuItem(localizationService.
                getLanguageSpecificText("transactionHistory"));
        MenuItem deviceLookupMenuItem = new MenuItem(localizationService.
                getLanguageSpecificText("cloudDeviceLookup"));
        newTransactionMenuItem.setOnAction(actionEvent -> mainController.onNewTransaction());
        viewTransactionMenuItem.setOnAction(actionEvent -> mainController.onViewTransaction());
        transactionHistoryMenuItem.setOnAction(actionEvent -> mainController.onTransactionHistory());
        deviceLookupMenuItem.setOnAction(actionEvent -> mainController.onDeviceLookupAction());
        transactionsMenu.getItems().addAll(
                newTransactionMenuItem,
                viewTransactionMenuItem,
                transactionHistoryMenuItem,
                deviceLookupMenuItem
        );
        return transactionsMenu;
    }

    /**
     * Initialize settings menu
     *
     * @return Menu
     */
    private Menu initSettingMenu() {
        LOGGER.debug("Adding Settings menu");
        // Settings
        Menu settingsMenu = new Menu(localizationService.getLanguageSpecificText("settings"));
        MenuItem customizationsItem = new MenuItem(localizationService.
                getLanguageSpecificText("openCustomizations"));
        MenuItem cloudCustomizationItem = new MenuItem(localizationService
                .getLanguageSpecificText("openCloudCustomizations"));
        customizationsItem.setOnAction(actionEvent -> mainController.onOpenCustomizations());
        settingsMenu.getItems().addAll(
                customizationsItem
        );
        cloudCustomizationItem.setOnAction(actionEvent -> mainController.onOpenCloudCustomizations(false));
        settingsMenu.getItems().addAll(
                cloudCustomizationItem
        );
        return settingsMenu;
    }

    /**
     * Initialize device operation menu
     *
     * @return Menu
     */
    private Menu initDeviceOperationsMenu() {
        LOGGER.debug("Adding Device operations menu");
        // Device Operations
        Menu deviceOperationsMenu = new Menu(localizationService.getLanguageSpecificText("deviceOperations"));
        MenuItem doCheckESN = new MenuItem(localizationService.getLanguageSpecificText("esnCheck"));
        doCheckESN.setOnAction(e -> mainController.bulkEsnCheck());
        deviceOperationsMenu.getItems().add(doCheckESN);

        MenuItem doCheckBlacklistESN = new MenuItem(localizationService.
                getLanguageSpecificText("esnCheckBlacklist"));

        doCheckBlacklistESN.setOnAction(e -> mainController.bulkEsnCheckBlacklist());
        deviceOperationsMenu.getItems().add(doCheckBlacklistESN);

        MenuItem doCheckAllESN = new MenuItem(localizationService.getLanguageSpecificText("esnCheckAll"));
        doCheckAllESN.setOnAction(e -> mainController.bulkEsnCheckAll());
        deviceOperationsMenu.getItems().add(doCheckAllESN);

        MenuItem doCheckUsInsuranceBlackList = new MenuItem(localizationService.
                getLanguageSpecificText("esnCheckUsInsuranceBlacklist"));
        doCheckUsInsuranceBlackList.setOnAction(e -> mainController.bulkEsnCheckUsInsuranceBlackList());
        deviceOperationsMenu.getItems().add(doCheckUsInsuranceBlackList);

        MenuItem doProvisionQrPrint = new MenuItem(localizationService.
                getLanguageSpecificText("provisionOrQRPrint"));
        doProvisionQrPrint.setOnAction(e -> mainController.onProvisionQrPrintToPDF());
        deviceOperationsMenu.getItems().add(doProvisionQrPrint);

        MenuItem doSimLockCheck = new MenuItem(localizationService.
                getLanguageSpecificText("simLock"));
        doSimLockCheck.setOnAction(e -> mainController.onSimLockSelection());
        deviceOperationsMenu.getItems().add(doSimLockCheck);

        MenuItem doCarrierLockCheck = new MenuItem(localizationService.
                getLanguageSpecificText("carrierLock"));
        doCarrierLockCheck.setOnAction(e -> mainController.onCarrierLockSelection());
        deviceOperationsMenu.getItems().add(doCarrierLockCheck);

        MenuItem doExportLogs = new MenuItem(localizationService.
                getLanguageSpecificText("exportLog"));
        doExportLogs.setOnAction(e -> mainController.onBulkExportLogs());
        deviceOperationsMenu.getItems().add(doExportLogs);

        MenuItem pushWifi = new MenuItem(localizationService.
                getLanguageSpecificText("pushWiFi"));
        pushWifi.setOnAction(e -> mainController.onBulkPushWifi());
        deviceOperationsMenu.getItems().add(pushWifi);

        MenuItem appInstall = new MenuItem(localizationService.
                getLanguageSpecificText("appInstall"));
        appInstall.setOnAction(e -> mainController.onBulkAppInstall());
        deviceOperationsMenu.getItems().add(appInstall);

        MenuItem removeAppAndProfile = new MenuItem(localizationService.
                getLanguageSpecificText("removeAppAndProfiles"));
        removeAppAndProfile.setOnAction(e -> mainController.onBulkRemoveAppAndProfile());
        deviceOperationsMenu.getItems().add(removeAppAndProfile);

        MenuItem syncResults = new MenuItem(localizationService.
                getLanguageSpecificText("syncResults"));
        syncResults.setOnAction(e -> mainController.onBulkSyncResults());
        deviceOperationsMenu.getItems().add(syncResults);

        MenuItem performNetworkUnlock = new MenuItem(localizationService.
                getLanguageSpecificText("performNetworkUnlock"));
        performNetworkUnlock.setOnAction(e -> mainController.onBulkPerformNetworkUnlock());
        deviceOperationsMenu.getItems().add(performNetworkUnlock);

        MenuItem autoExportMenuItem = new MenuItem(localizationService.
                getLanguageSpecificText("forceAutoExport"));
        autoExportMenuItem.setOnAction(e -> mainController.onBulkAutoExport());
        deviceOperationsMenu.getItems().add(autoExportMenuItem);

        MenuItem recheckBatteryInfo = new MenuItem(localizationService.
                getLanguageSpecificText("recheckBatteryInfo"));
        recheckBatteryInfo.setOnAction(e -> mainController.onRecheckBatteryInfoClicked());
        deviceOperationsMenu.getItems().add(recheckBatteryInfo);

        return deviceOperationsMenu;
    }

    /**
     * Initialize select packages menu
     *
     * @return Menu
     */
    private Menu initSelectPackageMenu() {
        LOGGER.debug("Adding Select Package menu");
        // Select Packages
        Menu selectPackagesMenu = new Menu(localizationService.getLanguageSpecificText("selectPackage"));
        MenuItem spDefaultMenuItem = new MenuItem(localizationService.getLanguageSpecificText("select Package"));
        spDefaultMenuItem.setOnAction(e -> mainController.onSelectPackageClick("PackageProfile"));
        selectPackagesMenu.getItems().add(spDefaultMenuItem);
        if (uiInMemoryStore.getSelectPackageResponses() != null) {
            for (Map.Entry<String, SelectPackageResponse.ProfilesConfiguration> entry :
                    uiInMemoryStore.getSelectPackageResponses().entrySet()) {
                String profileName = entry.getKey();
                MenuItem spMenuItems = new MenuItem(profileName);
                spMenuItems.setOnAction(e -> mainController.onSelectPackageClick(profileName));
                selectPackagesMenu.getItems().add(spMenuItems);
            }
        }
        return selectPackagesMenu;
    }

    /**
     * Initialize vendor menu
     *
     * @return Menu
     */
    private Menu initVendorMenu() {
        LOGGER.debug("Adding Vendor menu");
        // Vendor
        Menu vendorMenu = new Menu(localizationService.getLanguageSpecificText("vendor"));
        MenuItem updateVendorInfo = new MenuItem(localizationService.getLanguageSpecificText("updateVendorInfo"));
        updateVendorInfo.setOnAction(e -> mainController.onVendorUpdateClicked());
        vendorMenu.getItems().add(updateVendorInfo);
        return vendorMenu;
    }

    /**
     * Initialize mode menu
     *
     * @return Menu
     */
    private Menu initModeMenu() {
        LOGGER.debug("Adding Mode menu");
        // Vendor
        Menu modeMenu = new Menu(localizationService.getLanguageSpecificText("mode"));
        MenuItem updateDefaultMode = new MenuItem(localizationService.
                getLanguageSpecificText(RunningMode.DEFAULT_MODE.getKey()));
        MenuItem updateAirpodsMode = new MenuItem(localizationService.
                getLanguageSpecificText(RunningMode.AIRPOD_MODE.getKey()));
        MenuItem updateIWatchMode = new MenuItem(localizationService.
                getLanguageSpecificText(RunningMode.IWATCH_MODE.getKey()));
        MenuItem preCheckMode = new MenuItem(localizationService.
                getLanguageSpecificText(RunningMode.PRE_CHECK.getKey()));

        updateDefaultMode.setOnAction(e -> mainController.displayDefaultModeUI());
        updateAirpodsMode.setOnAction(e -> mainController.createAirpodsPortBox(false));
        updateIWatchMode.setOnAction(e -> mainController.displayIWatchModeUI());
        preCheckMode.setOnAction(e -> mainController.displayPreCheckModeUI());

        modeMenu.getItems().add(updateDefaultMode);
//        modeMenu.getItems().add(preCheckMode);
        modeMenu.getItems().add(updateIWatchMode);
        if (osChecker.isMac()) {
            modeMenu.getItems().add(updateAirpodsMode);
        }

        return modeMenu;
    }

    /**
     * Initialize help menu
     *
     * @return Menu
     */
    private Menu initHelpMenu() {
        LOGGER.debug("Adding Help menu");
        // Help Menu (items TBD)
        Menu helpMenu = new Menu(localizationService.getLanguageSpecificText("help"));
        helpMenu.getItems().addAll(
        );
        return helpMenu;
    }


    /**
     * Initializes all menus
     *
     * @return border pane
     */
    public BorderPane initMenu() {
        MenuBar bar = new MenuBar();
        bar.getMenus().addAll(
                // TODO: add these back when there is functionality associated
                // initPhoneCheckMenu(),
                initActionMenu(),
                initTransactionMenu(),
                // initViewMenu(),
                initSettingMenu(),
                initDeviceOperationsMenu(),
                initSelectPackageMenu(),
                initVendorMenu(),
                initModeMenu()
                // initHelpMenu()
        );

        bar.useSystemMenuBarProperty().set(true);

        BorderPane borderPane = new BorderPane();
        borderPane.setTop(bar);
        setMenuBar(bar);
        return borderPane;
    }
}
