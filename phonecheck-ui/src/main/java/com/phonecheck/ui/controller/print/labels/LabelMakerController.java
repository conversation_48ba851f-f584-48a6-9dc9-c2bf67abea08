package com.phonecheck.ui.controller.print.labels;

import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.phonecheckapi.LabelFxmlResponse;
import com.phonecheck.model.print.PrintMode;
import com.phonecheck.model.print.brother.BrotherLabelRoll;
import com.phonecheck.model.print.brother.BrotherRollType;
import com.phonecheck.model.print.dymo.DymoLabelRoll;
import com.phonecheck.model.print.dymo.DymoPaperTray;
import com.phonecheck.model.print.label.LabelOrientation;
import com.phonecheck.model.print.label.LabelSizeUnit;
import com.phonecheck.model.print.zebra.ZebraLabelRoll;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.util.DeviceCosmeticResultsUtil;
import com.phonecheck.model.util.TestResultsUtil;
import com.phonecheck.ui.controller.utility.DeviceTestResultsLabelConfigUtil;
import com.phonecheck.ui.controller.utility.LabelMakerUtil;
import javafx.geometry.Pos;
import javafx.scene.Parent;
import javafx.scene.control.Label;
import javafx.scene.control.ScrollPane;
import javafx.scene.effect.ColorAdjust;
import javafx.scene.image.ImageView;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Pane;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.scene.text.Text;
import javafx.scene.text.TextFlow;
import javafx.scene.transform.Rotate;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Scope("prototype")
public class LabelMakerController extends AbstractLabelController {
    private static final Logger LOGGER = LoggerFactory.getLogger(LabelMakerController.class);

    private static final List<String> DATE_FIELDS = List.of("EraseStart", "EraseEnd", "Date", "DateAndTime", "Time");
    private static final String REGEX = "(?<=url=)([^\"\\s]+)";
    private static final String REPLACEMENT = "\"$1\"";
    private static final int LABEL_RESOLUTION = 96;
    // Label text Axis from cloud is not so accurate so added a threshold to handle overlapping manually
    private static final int Y_AXIS_OVERLAP_THRESHOLD = 5;
    private static final String LABEL_EXTENSION = "_label";
    private static final String DEVICE_TITLE_KEY = "deviceTitle";
    private static final String DEVICE_MAKE_KEY = "deviceMake";
    private static final String DEVICE_MEMORY_KEY = "deviceMemory";
    private static final String DEVICE_COLOR_KEY = "deviceColor";
    private static final String DEVICE_MODEL_NO_KEY = "deviceModelNo";
    private static final String DEVICE_CARRIER_KEY = "deviceCarrier";
    private static final String TITLE_QR_CODE_KEY = "TitleQRCode";
    private static final String MAKE_KEY = "make";
    private static final String MODEL_KEY = "model";
    private static final String COLOR_KEY = "color";
    private static final String CARRIER_KEY = "carrier";
    private static final String MEMORY_KEY = "memory";

    private final FlowPane failedTestResultsContainer = new FlowPane();
    private final FlowPane passedTestResultsContainer = new FlowPane();
    private final Map<String, String> nodesMap = new HashMap<>();
    private final List<javafx.scene.Node> textNodes = new ArrayList<>();
    private LabelFxmlResponse.Data fxmlData;
    @Setter
    private String labelName;
    @Setter
    private Pane rootPane;
    private float paperWidth;
    private float paperHeight;
    private LabelSizeUnit sizeUnit;
    private LabelOrientation orientation;
    private String paperSize;

    @Setter
    private DeviceTestResultsLabelConfigUtil testResultsLabelConfigUtil;

    @Override
    public Pane getRootPane() {
        return rootPane;
    }

    @Override
    public int getXPosition() {
        return 10;
    }

    @Override
    public int getYPosition() {
        return 5;
    }

    @Override
    public float getWidth() {
        return paperWidth;
    }

    @Override
    public float getHeight() {
        return paperHeight;
    }

    @Override
    public LabelSizeUnit getSizeUnit() {
        return sizeUnit;
    }

    @Override
    public LabelOrientation getDefaultOrientation() {
        return orientation;
    }

    @Override
    public String getLabelName() {
        return labelName;
    }

    @Override
    public String getLabelFileName() {
        return getLabelName() + LABEL_EXTENSION;
    }

    // ==================================================================
    //                      Zebra Specific Methods
    // ==================================================================

    @Override
    public String getLpCommandOptionsForZebra(final LabelOrientation cloudRollOrientation) {
        ZebraLabelRoll labelRoll = ZebraLabelRoll.getByLabelSize(paperSize, cloudRollOrientation);
        String mediaSize = ZebraLabelRoll.NA.getMediaSize();

        LabelOrientation labelOrientation = getDefaultOrientation();
        int orientation;

        if (cloudRollOrientation == null) {
            orientation = labelOrientation == LabelOrientation.LANDSCAPE ? 2 : 5;
        } else if (cloudRollOrientation == labelOrientation) {
            orientation = 2;
        } else {
            orientation = 5;
        }

        if (labelRoll != null) {
            mediaSize = labelRoll.getMediaSize();

            if (cloudRollOrientation == null && labelRoll.getOrientation() == LabelOrientation.PORTRAIT) {
                orientation = orientation == 5 ? 2 : 5;
            }
        }

        return "-o media=\"" + mediaSize + "\" -o orientation-requested=" + orientation;
    }

    // ==================================================================
    //                      Dymo Specific Methods
    // ==================================================================

    @Override
    public String getLpCommandOptionsForDymo(final DymoPaperTray dymoPaperTray,
                                             final LabelOrientation cloudRollOrientation) {
        DymoLabelRoll labelRoll = DymoLabelRoll.getByLabelSize(paperSize);
        String mediaSize = DymoLabelRoll.NA.getMediaSize();

        LabelOrientation labelOrientation = getDefaultOrientation();
        int orientation;

        if (cloudRollOrientation == null) {
            orientation = labelOrientation == LabelOrientation.LANDSCAPE ? 2 : 5;
        } else if (cloudRollOrientation == labelOrientation) {
            orientation = 2;
        } else {
            orientation = 5;
        }

        if (labelRoll != null) {
            mediaSize = labelRoll.getMediaSize();

            if (cloudRollOrientation == null && labelRoll.getOrientation() == LabelOrientation.PORTRAIT) {
                orientation = orientation == 5 ? 2 : 5;
            }
        }

        String inputTray = StringUtils.EMPTY;
        if (dymoPaperTray != null) {
            inputTray = " -o InputSlot=" + dymoPaperTray.getName();
        }

        return "-o media=\"" + mediaSize + "\" -o orientation-requested=" + orientation + inputTray;
    }

    // ==================================================================
    //                      Brother Specific Methods
    // ==================================================================

    @Override
    public String getLpCommandOptionsForBrother(final BrotherRollType brotherRollType,
                                                final LabelOrientation cloudRollOrientation) {
        if (brotherRollType != null) {
            BrotherLabelRoll labelRoll = BrotherLabelRoll.getByLabelSizeAndType(paperSize, brotherRollType.getValue());

            String mediaSize = BrotherLabelRoll.NA.getMediaSize();

            LabelOrientation labelOrientation = getDefaultOrientation();
            int orientation;

            if (cloudRollOrientation == null) {
                orientation = labelOrientation == LabelOrientation.LANDSCAPE ? 2 : 5;
            } else if (cloudRollOrientation == labelOrientation) {
                orientation = 2;
            } else {
                orientation = 5;
            }

            if (labelRoll != null) {
                mediaSize = labelRoll.getMediaSize();

                if (cloudRollOrientation == null && labelRoll.getOrientation() == LabelOrientation.PORTRAIT) {
                    orientation = orientation == 5 ? 2 : 5;
                }
            }

            return "-o media=" + mediaSize + " -o orientation-requested=" + orientation;
        } else {
            return StringUtils.EMPTY;
        }
    }

    // ==================================================================
    //                     Label's UI Specific Methods
    // ==================================================================

    /**
     * Initializes the views for a specified device based on the provided data.
     *
     * @param device    The Device for which views are to be initialized.
     * @param printMode print mode
     */
    @Override
    public void initializeViews(final Device device, final PrintMode printMode) {
        testResultsLabelConfigUtil.initializeCanvasData();
        fxmlData = getUiInMemoryStore().getLabelDataMap().get(getLabelName());
        boolean isFxmlV2Available = StringUtils.isNotEmpty(fxmlData.getFxmlV2());
        String fxml = isFxmlV2Available ? fxmlData.getFxmlV2() : fxmlData.getFxml();
        loadNodesFromCloudFxml(isFxmlV2Available, fxml);
        displayDeviceTestResultsOnLabel(device.getDeviceTestResult(),
                printMode, getUiInMemoryStore().getAssignedCloudCustomization());

        boolean listNonGenuineParts = fxml.contains("Parts:NonGenuine");
        LabelMakerUtil.loadDeviceDataIntoMap(device, printMode, getUiInMemoryStore().getAssignedCloudCustomization(),
                getUiInMemoryStore().getMasterUserName(), getUiInMemoryStore().getWarehouseName(),
                listNonGenuineParts);
        setDimensionAndOrientation(fxmlData.getCanvasSize());
        setDeviceTitleForQr(device);
        setDeviceDetailOnLabel(device);
        // It should always be called after setDeviceDetailOnLabel and before handleOverlappingNodes
        setDeviceTitle();

        if (!isFxmlV2Available) {
            handleOverlappingNodes();
        }
    }

    /**
     * Displays the test results of device on respective labels.
     *
     * @param deviceTestResult           The iPhone device object for which the test results are displayed.
     * @param printMode                  print mode
     * @param cloudCustomizationResponse cloud customization response
     */
    public void displayDeviceTestResultsOnLabel(final DeviceTestResult deviceTestResult,
                                                final PrintMode printMode,
                                                final CloudCustomizationResponse cloudCustomizationResponse) {

        boolean labelContainsResultBox = nodesMap.containsValue("AnchorPane") &&
                (nodesMap.containsKey("TestResults") || nodesMap.containsKey("FailedResults"));

        if (deviceTestResult != null && labelContainsResultBox) {

            // Triple <cosmetic Result, Failed cosmetic result, Passed cosmetic result>
            Triple<String, String, String> cosmeticFailedPassedResult =
                    DeviceCosmeticResultsUtil.getAllCosmeticTestResults(deviceTestResult, cloudCustomizationResponse);

            String passedTestResultsToBeDisplayed =
                    getPassedTesResultsToBeDisplayed(deviceTestResult, printMode, cosmeticFailedPassedResult);
            String failedTestResultsToBeDisplayed =
                    getFailedTesResultsToBeDisplayed(deviceTestResult, printMode, cosmeticFailedPassedResult);

            addTestResultToPane(passedTestResultsToBeDisplayed, passedTestResultsContainer, "passed-results.png");
            addTestResultToPane(failedTestResultsToBeDisplayed, failedTestResultsContainer, "failed-results.png");
        }
    }

    /**
     * Adds test results to the specified FlowPane container.This method processes a comma-separated string of
     * test results, trims each result, and checks if it is not blank. For each non-blank result, it creates a
     * visual representation using the specified image and adds it to the container.
     *
     * @param testResults The test results as a comma-separated string. If null, the method does nothing.
     * @param container   The FlowPane container to which the test results will be added.
     * @param imageName   The name of the image to be displayed with each test result.
     */
    private void addTestResultToPane(final String testResults, final FlowPane container, final String imageName) {
        if (StringUtils.isNotBlank(testResults)) {

            nodesMap.forEach((key, value) -> {
                if (value.contains("AnchorPane") &&
                        (key.contains("TestResults") || key.contains("FailedResults"))) {
                    AnchorPane anchorPane = (AnchorPane) getRootPane().lookup("#" + key);
                    FlowPane flowPane = (FlowPane) anchorPane.getChildren().get(0);
                    flowPane.setPrefHeight(anchorPane.getPrefHeight());
                    flowPane.setMaxHeight(anchorPane.getPrefHeight());
                    container.setPrefWidth(flowPane.getPrefWidth());
                    container.setPrefHeight(flowPane.getPrefHeight());
                    container.setHgap(12);
                }
            });

            testResultsLabelConfigUtil.adjustFontSize(container.getPrefWidth(), fxmlData.getCanvasSize());

            String[] results = testResults.split(",");
            for (String result : results) {
                result = result.trim();
                if (StringUtils.isNotBlank(result)) {
                    container.getChildren().add(displayTestResults(result, imageName));
                }
            }
        }
    }

    /**
     * Displays test results on labels
     *
     * @param testResult test results string
     * @param imgName    image name
     * @return Pane
     */
    private Pane displayTestResults(final String testResult, final String imgName) {

        final double fontSize = testResultsLabelConfigUtil.getFontSize();
        final double imageHeight = testResultsLabelConfigUtil.getImageHeight();
        final double imageWidth = testResultsLabelConfigUtil.getImageWidth();
        final double columnWidth = testResultsLabelConfigUtil.getHBoxWidth();


        Text text = new Text();
        text.setFont(new Font("Helvetica", fontSize));
        String updatedTestResult = testResult.length() > 20 ? testResult.substring(0, 20) : testResult;
        text.setText(updatedTestResult);

        ImageView imageView = new ImageView();
        imageView.setFitWidth(imageWidth);
        imageView.setFitHeight(imageHeight);

        setImageToImageView(LabelMakerController.this, imageView, imgName);
        ColorAdjust colorAdjust = new ColorAdjust();
        colorAdjust.setBrightness(-1.0);
        colorAdjust.setContrast(0.0);
        colorAdjust.setHue(0.0);
        colorAdjust.setSaturation(0.0);

        imageView.setEffect(colorAdjust);

        HBox pane = new HBox();
        pane.setAlignment(Pos.CENTER_LEFT);
        pane.getChildren().add(imageView);
        pane.getChildren().add(text);
        pane.setPrefWidth(columnWidth);
        pane.setMinWidth(columnWidth);
        pane.setMaxWidth(columnWidth);
        return pane;
    }

    /**
     * Sets the dimensions and orientation of the label based on the provided canvas size.
     *
     * @param canvasSize A string representing the canvas size in the format "widthxheight".
     */
    private void setDimensionAndOrientation(final String canvasSize) {
        String[] dimensionArray = canvasSize.split("x");
        if (StringUtils.isNotBlank(canvasSize)) {
            try {
                this.paperSize = canvasSize;

                this.paperWidth = Float.parseFloat(dimensionArray[0]);

                // Check if height contains a unit like 'mm'
                String heightString = dimensionArray[1];
                if (heightString.toLowerCase().endsWith("mm")) {
                    // Extract numeric part for height
                    String heightNumericPart = heightString.substring(0, heightString.length() - 2).trim();
                    this.paperHeight = Float.parseFloat(heightNumericPart);
                    this.sizeUnit = LabelSizeUnit.MILLIMETER;
                } else {
                    this.paperHeight = Float.parseFloat(heightString);
                    this.sizeUnit = LabelSizeUnit.INCH;
                }

                if (sizeUnit == LabelSizeUnit.MILLIMETER) {
                    // if dimension in mm then adjust the pref width and height accordingly
                    float widthToSetPref = this.paperWidth / 25.4f;
                    float heightToSetPref = this.paperHeight / 25.4f;

                    this.rootPane.setPrefWidth(widthToSetPref * LABEL_RESOLUTION);
                    this.rootPane.setPrefHeight(heightToSetPref * LABEL_RESOLUTION);
                } else {
                    this.rootPane.setPrefWidth(paperWidth * LABEL_RESOLUTION);
                    this.rootPane.setPrefHeight(paperHeight * LABEL_RESOLUTION);
                }
            } catch (NumberFormatException e) {
                LOGGER.error("Error while parsing dimensions", e);
            }
        }
        this.orientation = this.paperWidth < this.paperHeight ? LabelOrientation.PORTRAIT : LabelOrientation.LANDSCAPE;
    }

    /**
     * This method will take all the element with fx:id value into nodesMap
     *
     * @param isFxmlV2      is fxml from v2 api
     * @param fxmlFromCloud fxml of the selected label
     */
    private void loadNodesFromCloudFxml(final boolean isFxmlV2, final String fxmlFromCloud) {
        String cloudFxml = removeRedundantTags(fxmlFromCloud);
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        try {
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document document = builder.parse(new InputSource(
                    new StringReader(
                            isFxmlV2 ? cloudFxml.replaceAll(REGEX, REPLACEMENT) :
                                    fxmlFromCloud.replaceAll(REGEX, REPLACEMENT)
                    )));
            document.normalizeDocument();
            Node root = document.getDocumentElement();
            exploreAndMapNodes(root);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while reading fx:id from the cloud fxml. ", e);
        }
    }

    /**
     * Recursively explores nodes in the XML document, mapping their fx:id to their type.
     * It also checks if a node is a Pane (or its subclass) and explores its child nodes recursively.
     *
     * @param root The parent node to start the exploration from. This is usually the root element of the XML document.
     */
    private void exploreAndMapNodes(final Node root) {
        NodeList nList = root.getChildNodes();
        for (int index = 0; index < nList.getLength(); index++) {
            Node nNode = nList.item(index);
            if (nNode.getNodeType() == Node.ELEMENT_NODE) {
                String nodeName = nNode.getNodeName();
                if (nNode.getAttributes().getNamedItem("fx:id") != null) {
                    nodesMap.put(nNode.getAttributes().getNamedItem("fx:id").getTextContent(), nodeName);
                }

                if (isPane(nodeName)) {
                    exploreAndMapNodes(nNode);
                }
            }
        }
    }

    /**
     * Determines if a given node name corresponds to a JavaFX Pane or any of its subclasses.
     * This method helps in identifying if a node in the FXML document is a type of Pane.
     *
     * @param nodeName The name of the node to check.
     * @return true if the nodeName corresponds to a Pane or its subclass, false otherwise.
     */
    private boolean isPane(final String nodeName) {
        // Add all Pane subclasses that you need to recognize
        List<String> paneTypes = Arrays.asList(
                "Pane", "AnchorPane",
                "HBox", "VBox",
                "GridPane", "FlowPane",
                "StackPane", "BorderPane",
                "TextFlow", "ScrollPane"
        );
        return paneTypes.contains(nodeName);
    }

    /**
     * Removes redundant XML tags from the given content.
     * This method removes specific redundant tags
     * from the input string, which are commonly used in FXML files but not be needed in the processed content.
     *
     * @param content the input string from which redundant tags need to be removed
     * @return a new string with the specified redundant tags removed
     */
    private String removeRedundantTags(final String content) {
        return content.replaceAll("<content>", "")
                .replaceAll("</content>", "")
                .replaceAll("<children>", "")
                .replaceAll("</children>", "");
    }

    /**
     * This method helps in identifying if a node in an element of deviceTitle.
     *
     * @param nodeKey The node id of the node to check.
     * @return true if the nodeKey corresponds to any element inside deviceTitle TextFlow, false otherwise.
     */
    private boolean isDeviceTitleKey(final String nodeKey) {
        List<String> deviceTitleKeys = Arrays.asList(
                DEVICE_TITLE_KEY,
                DEVICE_MAKE_KEY,
                DEVICE_MEMORY_KEY,
                DEVICE_COLOR_KEY,
                DEVICE_MODEL_NO_KEY,
                DEVICE_CARRIER_KEY
        );
        return deviceTitleKeys.contains(nodeKey);
    }

    /**
     * This method will get all textNodes in device title and convert them to a single textNode.
     * It is required otherwise handleOverlappingNodes() will ellipse every textNode inside deviceTitle.
     */
    private void setDeviceTitle() {
        Text titleNode = getDeviceTitleNodeFromScrollPane();
        if (titleNode == null) {
            titleNode = (Text) getRootPane().lookup("#" + DEVICE_TITLE_KEY);
        }
        if (titleNode != null) {
            TextFlow textFlow = (TextFlow) titleNode.getParent();
            StringBuilder newDeviceTitle = new StringBuilder();
            textFlow.getChildren().forEach(node -> {
                if (node instanceof Text) {
                    newDeviceTitle.append(((Text) node).getText()).append(" ");
                } else if (node instanceof Label) {
                    newDeviceTitle.append(((Label) node).getText()).append(": ");
                }
            });
            textFlow.getChildren().clear();
            titleNode.setText(newDeviceTitle + " ");
            applyStylesAndFormats(titleNode, null, null);
            textFlow.getChildren().add(titleNode);
            textNodes.add(titleNode);
        }
    }

    /**
     * this method will adjust the overlapping of the text as per the resolution
     */
    private void handleOverlappingNodes() {
        try {
            for (int textToEllipse = 0; textToEllipse < textNodes.size(); textToEllipse++) {
                for (int textToCompare = 0; textToCompare < textNodes.size(); textToCompare++) {
                    // We need to check all ith values with all jth values
                    TextFlow textFlow;
                    Text textNode;

                    if (textNodes.get(textToEllipse) instanceof TextFlow) {
                        textFlow = (TextFlow) textNodes.get(textToEllipse);
                        textNode = (Text) textFlow.getChildren().get(0);
                    } else {
                        textNode = (Text) textNodes.get(textToEllipse);
                        textFlow = (TextFlow) textNode.getParent();
                    }

                    if (textFlow.getChildren().isEmpty()) {
                        continue;
                    }

                    // There are two types of text 1. textflows and 2. text so we need to handle them both manually

                    boolean isIntersectingTextFlow = textNodes.get(textToCompare) instanceof TextFlow &&
                            textFlow.getBoundsInParent().intersects(textNodes.get(textToCompare).getBoundsInParent()) &&
                            Math.abs(textFlow.getLayoutY() -
                                    textNodes.get(textToCompare).getLayoutY()) < Y_AXIS_OVERLAP_THRESHOLD;

                    if (textToEllipse != textToCompare && isIntersectingTextFlow) {

                        boolean isBeforeXText = textFlow.getBoundsInParent().getMinX() <
                                textNodes.get(textToCompare).getBoundsInParent().getMinX();

                        // i==j means same node, so we will not check it.
                        if (isBeforeXText) {
                            // textFlowObj is on left side
                            textNode.setText(ellipseText(textNode.getText(),
                                    calculateCharactersToBeTruncated(textFlow,
                                            textNodes.get(textToCompare).getBoundsInParent().getMinX(),
                                            textNode.getText().length())));
                        }
                    }

                    boolean isIntersectingText = textNodes.get(textToCompare) instanceof Text &&
                            textFlow.getBoundsInParent()
                                    .intersects(textNodes.get(textToCompare).getParent().getBoundsInParent()) &&
                            textNodes.get(textToCompare).getParent().getId() == null;

                    if (textToEllipse != textToCompare && isIntersectingText) {

                        boolean isBeforeXTextFlow = textFlow.getBoundsInParent().getMinX() <
                                textNodes.get(textToCompare).getParent().getBoundsInParent().getMinX();

                        // i==j means same node, so we will not check it.
                        if (isBeforeXTextFlow) {
                            // textFlowObj is on left side
                            textNode.setText(ellipseText(textNode.getText(),
                                    calculateCharactersToBeTruncated(textFlow,
                                            textNodes.get(textToCompare).getParent().getBoundsInParent().getMinX(),
                                            textNode.getText().length())));
                        }
                    }

                    if (textFlow.getBoundsInParent().getMaxX() >= (this.rootPane.getPrefWidth() /
                            LABEL_RESOLUTION) * LABEL_RESOLUTION) {
                        // text is exceeding parent boundary
                        textNode.setText(ellipseText(textNode.getText(),
                                calculateCharactersToBeTruncated(textFlow,
                                        (this.rootPane.getPrefWidth() /
                                                LABEL_RESOLUTION) * LABEL_RESOLUTION,
                                        textNode.getText().length())));
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while adjusting label size", e);
        }
    }

    /**
     * Truncates a given string and appends ellipsis if needed.
     *
     * @param input      The input string to be truncated.
     * @param characters The maximum number of characters to retain before appending ellipsis.
     * @return The truncated string with ellipsis if needed.
     */
    public static String ellipseText(final String input, final int characters) {
        try {
            if (input != null && !input.isEmpty()) {
                return input.substring(0, Math.min(characters, input.length())) + "... ";
            } else {
                return "";
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred while truncating string");
            return "";
        }
    }

    /**
     * This method will take text, intersection and number of text as parameters.
     *
     * @param text                     : Text to be reviewed for trim
     * @param intersectionPoint        : Intersection point of the text
     * @param numberOfCharactersInText : Total number of characters in the text
     * @return Returns the no of characters to be trimmed
     */
    private int calculateCharactersToBeTruncated(final TextFlow text, final double intersectionPoint,
                                                 final int numberOfCharactersInText) {
        try {
            double totalPixelsOfText = text.getBoundsInParent().getMaxX() - text.getBoundsInParent().getMinX();
            double numOfCharactersInOnePixel = numberOfCharactersInText / totalPixelsOfText;
            double textNewWidthInPixels = intersectionPoint - text.getBoundsInParent().getMinX();
            int numberOfCharsToBeTrimmed = (int) (numOfCharactersInOnePixel * textNewWidthInPixels);
            //We need 4 characters after trim including three dots and one space.
            for (int i = 4; i > 0; i--) {
                if (numberOfCharsToBeTrimmed > i) {
                    // 'i' is margin we are leaving after trim
                    return numberOfCharsToBeTrimmed - i;
                }
            }
            return numberOfCharsToBeTrimmed;
        } catch (Exception e) {
            LOGGER.error("Exception occurred while calculating number of characters to be trimmed.", e);
            return numberOfCharactersInText;
        }
    }

    /**
     * Setting the device detail on label elements,
     * or hide the label elements if the given value is null or empty.
     *
     * @param device target device
     */
    private void setDeviceDetailOnLabel(final Device device) {
        nodesMap.forEach((key, value) -> {
            if (StringUtils.equalsIgnoreCase(value, "TextFlow")
                    && LabelMakerUtil.getLabelFieldsMap().containsKey(key)) {
                TextFlow textFlow = (TextFlow) getRootPane().lookup("#" + key);
                Text textNode = (Text) textFlow.getChildren().get(0);

                applyStylesAndFormats(textNode, null, LabelMakerUtil.getLabelFieldsMap().get(key).trim());

                if (!StringUtils.isBlank(LabelMakerUtil.getLabelFieldsMap().get(key))) {
                    textNodes.add(textFlow);
                }
            } else if (StringUtils.equalsIgnoreCase(value, "Text")
                    && LabelMakerUtil.getLabelFieldsMap().containsKey(key)) {
                List<Text> textList = getTextFlowFromScrollPane(key);
                int counter = 0;
                for (Text text : textList) {
                    if (text == null) {
                        text = (Text) getRootPane().lookup("#" + key);
                    }
                    applyStylesAndFormats(text, null, counter > 0 ? " " +
                            LabelMakerUtil.getLabelFieldsMap().get(key).trim() :
                            LabelMakerUtil.getLabelFieldsMap().get(key).trim());

                    if (!StringUtils.isBlank(LabelMakerUtil.getLabelFieldsMap().get(key))) {
                        // No need to add device title key as we are already handling it in setDeviceTitle();
                        if (!isDeviceTitleKey(key)) {
                            textNodes.add(text);
                        }
                    }
                    counter++;
                }
            } else if (value.contains("AnchorPane")) {
                // Setup test results containers
                setupTestResultsPane(key, passedTestResultsContainer, "TestResults");
                setupTestResultsPane(key, failedTestResultsContainer, "FailedResults");

            } else if (value.contains("Label") && LabelMakerUtil.getLabelFieldsMap().containsKey(key)) {
                List<Label> labels = findElementsById(rootPane, key, Label.class);
                int counter = 0;
                for (Label label : labels) {
                    if (label != null) {
                        applyStylesAndFormats(null, label, counter > 0 ? " " +
                                LabelMakerUtil.getLabelFieldsMap().get(key).trim() :
                                LabelMakerUtil.getLabelFieldsMap().get(key).trim());
                        if (StringUtils.isNotBlank(LabelMakerUtil.getLabelFieldsMap().get(key)) &&
                                !DATE_FIELDS.contains(key)) {
                            if (key.equals("ESN")) {
                                label.setText(getEsnImageTextResponseService().getEsnImageTextForJapanese(device,
                                        getUiInMemoryStore().getCurrentLanguage()));
                            } else if (StringUtils.isBlank(label.getText())) {
                                label.setText(LabelMakerUtil.getLabelFieldsMap().get(key).trim());
                            }
                        }
                        hideElementIfBatteryHealthZero(label, key, device);
                    }
                    counter++;
                }
            } else if (value.contains("Image") && LabelMakerUtil.getLabelFieldsMap().containsKey(key)) {
                ImageView imageNode = (ImageView) getRootPane().lookup("#" + key);
                double imageWidth = Double.parseDouble(String.valueOf(
                        imageNode.getScaleX() * imageNode.getFitWidth()));
                double imageHeight = Double.parseDouble(String.valueOf(
                        imageNode.getScaleY() * imageNode.getFitHeight()));
                imageNode.setFitWidth(imageWidth);
                imageNode.setFitHeight(imageHeight);
                if (StringUtils.isBlank(LabelMakerUtil.getLabelFieldsMap().get(key))) {
                    imageNode.setVisible(false);
                    imageNode.setManaged(false);
                } else {
                    if (StringUtils.containsIgnoreCase(key, "BarCode")) {
                        double scaleY = (paperHeight == 1 || paperWidth == 1) ? 1 : 0.75;
                        imageNode.setScaleX(1);
                        imageNode.setScaleY(isRotationAngleVertical(imageNode) ? 1 : scaleY);
                        imageNode.setImage(generateBarcodeFromData(LabelMakerUtil.getLabelFieldsMap()
                                .get(key), 600));
                        imageNode.setTranslateX(-5);
                    } else if (StringUtils.containsIgnoreCase(key, "QRCode")) {
                        imageNode.setScaleX(1.0);
                        imageNode.setScaleY(1.0);
                        imageNode.setImage(generateQRCodeFromData(LabelMakerUtil.getLabelFieldsMap()
                                .get(key), 300));
                    } else if (StringUtils.containsIgnoreCase(key, "IMEIMatrixCode") ||
                            StringUtils.containsIgnoreCase(key, "IMEI2MatrixCode")) {
                        imageNode.setScaleX(1.0);
                        imageNode.setScaleY(1.0);
                        imageNode.setImage(generateMatrixCode(LabelMakerUtil.getLabelFieldsMap()
                                .get(key), 300));
                    } else {
                        imageNode.setScaleX(0.5);
                        imageNode.setScaleY(0.5);
                        setImageToImageView(LabelMakerController.this, imageNode,
                                LabelMakerUtil.getLabelFieldsMap().get(key));
                    }
                }
            } else if (value.contains("ImageView") && "VersionIndicator".equals(key) &&
                    device instanceof IosDevice iosDevice) {
                // Version indicator will only work for IOS devices
                ImageView imageNode = (ImageView) getRootPane().lookup("#" + key);
                final String accessibleText = imageNode.getAccessibleText();
                final String criteriaVersionText = StringUtils.isNotBlank(accessibleText) ?
                        accessibleText : StringUtils.EMPTY;

                boolean iosVersionMatchFound = false;

                if (StringUtils.isNotBlank(criteriaVersionText)) {
                    Pattern pattern = Pattern.compile("\\d+");
                    Matcher matcher = pattern.matcher(criteriaVersionText);
                    if (matcher.find()) {
                        // found criteria ios version
                        int iosVersion = Integer.parseInt(matcher.group());
                        LOGGER.info("iosVersion in criteria: {} and product version of device: {}", iosVersion,
                                iosDevice.getProductVersion());
                        final String productVersion = iosDevice.getProductVersion();
                        if (StringUtils.isNotBlank(productVersion)) {
                            final String majorVersion = productVersion.split("\\.")[0];
                            if (NumberUtils.isParsable(majorVersion) && Integer.parseInt(majorVersion) >= iosVersion) {
                                LOGGER.info("Will show the version indicator for iOS device.");
                                // if os major version follows criteria, then show the image view
                                iosVersionMatchFound = true;
                            }

                        }
                    }
                }

                if (!iosVersionMatchFound) {
                    LOGGER.info("Criteria text was: {} and device product version was: {}." +
                          " Will not show the version indicator.", criteriaVersionText, iosDevice.getProductVersion());
                    imageNode.setVisible(false);
                    imageNode.setManaged(false);
                }

            }
        });
    }

    /**
     * Finds all Label nodes within the given Pane that match the specified ID.
     *
     * @param root the root Pane to search for Label nodes
     * @param id   the ID to match for Label nodes
     * @param type the Class object representing the type of nodes to collect
     *             @param <T>      the type of nodes to collect
     * @return a list of Label nodes with the specified ID; an empty list if no matches are found
     */
    public <T extends javafx.scene.Node> List<T> findElementsById(final Pane root, final String id,
                                                                  final Class<T> type) {
        List<T> elements = new ArrayList<>();
        collectNodesById(root, id, type, elements);
        return elements;
    }

    /**
     * Recursively collects nodes of the specified type with the specified ID from the given Parent node.
     *
     * @param root     the Parent node to search for children
     * @param id       the ID to match for nodes
     * @param type     the Class object representing the type of nodes to collect
     * @param elements the list to collect matching nodes into
     * @param <T>      the type of nodes to collect
     */
    private <T extends javafx.scene.Node> void collectNodesById(
            final Parent root, final String id, final Class<T> type, final List<T> elements) {
        for (javafx.scene.Node node : root.getChildrenUnmodifiable()) {
            if (type.isInstance(node) && id.equals(node.getId())) {
                elements.add(type.cast(node));
            }
            if (node instanceof Parent) {
                collectNodesById((Parent) node, id, type, elements);
            }
        }
    }

    /**
     * Configures the test results pane within the specified AnchorPane.
     * If the AnchorPane with the given key and paneId is found, and the resultsContainer is not empty,
     * it sets the preferred and maximum height of the FlowPane to match the AnchorPane's height,
     * sets the preferred width of the resultsContainer to match the FlowPane's width,
     * and adds the resultsContainer to the FlowPane.
     *
     * @param key              The key used to look up the AnchorPane.
     * @param resultsContainer The FlowPane containing the results to be added.
     * @param paneId           The ID of the AnchorPane to be matched.
     */
    private void setupTestResultsPane(final String key, final FlowPane resultsContainer, final String paneId) {
        AnchorPane anchorPane = (AnchorPane) getRootPane().lookup("#" + key);
        if (anchorPane != null && anchorPane.getId().equals(paneId) && !resultsContainer.getChildren().isEmpty()) {
            FlowPane flowPane = (FlowPane) anchorPane.getChildren().get(0);
            flowPane.setPrefHeight(anchorPane.getPrefHeight());
            flowPane.setMaxHeight(anchorPane.getPrefHeight());
            resultsContainer.setPrefWidth(flowPane.getPrefWidth());
            flowPane.getChildren().add(resultsContainer);
        }
        // TODO: We'll handle all anchor-panes in this method which will be added later
    }

    /**
     * Retrieves a Text node from a TextFlow within a ScrollPane in the root AnchorPane.
     * This method iterates over the children of the root AnchorPane to find a ScrollPane.
     * Once found, it retrieves the content of the ScrollPane, checks if it is a TextFlow,
     * and then looks up a Text node with the specified nodeKey within the TextFlow.
     *
     * @param nodeKey Text node to be retrieved
     * @return the Text node with the specified nodeKey, or null if not found
     */
    private List<Text> getTextFlowFromScrollPane(final String nodeKey) {
        AnchorPane rootPane = (AnchorPane) getRootPane();
        List<Text> textList = new ArrayList<>();
        for (javafx.scene.Node node : rootPane.getChildren()) {
            if (node instanceof ScrollPane scrollPane) {
                javafx.scene.Node content = scrollPane.getContent();
                if (content instanceof TextFlow textFlow) {
                    textList.add((Text) textFlow.lookup("#" + nodeKey));
                }
            }
        }
        return textList;
    }

    /**
     * Retrieves the title node from a TextFlow within a ScrollPane in the root AnchorPane.
     * This method iterates over the children of the root AnchorPane to find a ScrollPane.
     * Once found, it retrieves the content of the ScrollPane and checks if it is a TextFlow.
     * It then searches for a Text node with a specific key within the TextFlow.
     *
     * @return the Title Text node or null if not found
     */
    private Text getDeviceTitleNodeFromScrollPane() {
        AnchorPane rootPane = (AnchorPane) getRootPane();
        for (javafx.scene.Node node : rootPane.getChildren()) {
            if (!(node instanceof ScrollPane scrollPane)) {
                continue;
            }
            javafx.scene.Node content = scrollPane.getContent();
            if (!(content instanceof TextFlow textFlow)) {
                continue;
            }
            for (javafx.scene.Node child : textFlow.getChildren()) {
                if (!(child instanceof Text text &&
                        text.lookup("#" + DEVICE_TITLE_KEY) != null)) {
                    continue;
                }
                return (Text) text.lookup("#" + DEVICE_TITLE_KEY);
            }
        }
        return null;
    }

    /**
     * Retrieves the {@link ImageView} node representing the QR code title from the root {@link AnchorPane}.
     * <p>
     * This method searches through the immediate children of the root pane, looking for an {@code ImageView}
     * that contains a node with the ID specified by {@code TITLE_QR_CODE_KEY}. If such a node is found,
     * it is returned; otherwise, {@code null} is returned.
     * </p>
     *
     * @return the {@code ImageView} node with the specified ID, or {@code null} if not found
     */
    private ImageView getTitleQrNodeFromScrollPane() {
        AnchorPane rootPane = (AnchorPane) getRootPane();
        if (rootPane == null) {
            return null;
        }
        for (javafx.scene.Node child : rootPane.getChildren()) {
            if (!(child instanceof ImageView imageView &&
                    imageView.lookup("#" + TITLE_QR_CODE_KEY) != null)) {
                continue;
            }
            return (ImageView) imageView.lookup("#" + TITLE_QR_CODE_KEY);
        }
        return null;
    }

    /**
     * Parses and applies styles to a JavaFX Text node based on custom tags,
     * and ensures no tag remains in the final text.
     *
     * @param textNode  The Text node to be modified.
     * @param labelNode The Label node to be modified.
     * @param text      The optional default text to use if not formatting as a date.
     */
    public void applyStylesAndFormats(final Text textNode, final Label labelNode, final String text) {
        String rawText = (textNode != null) ? textNode.getText() : (labelNode != null) ? labelNode.getText() : null;

        if (rawText == null) {
            return;
        }
        // Find the start of any directive
        int startTag = rawText.indexOf("::[");
        if (startTag != -1) {
            int endTag = rawText.indexOf("}]", startTag) + 2;
            String directives = rawText.substring(startTag + 2, endTag - 1);  // Extract everything inside ::[...}]

            // Split style and date format directives
            String styleDirective = directives.contains("style{")
                    ? directives.substring(directives.indexOf("style{") + 6,
                    directives.indexOf("}", directives.indexOf("style{"))) : "";
            String dateFormatDirective = directives.contains("dateFormat{")
                    ? directives.substring(directives.indexOf("dateFormat{") + 11,
                    directives.indexOf("}", directives.indexOf("dateFormat{"))) : "";
            String title = directives.contains("config{title:")
                    ? directives.substring(directives.indexOf("config{title:") + 13,
                    directives.indexOf(";", directives.indexOf("config{title:"))) : "";

            // Handle date formatting if present
            String formattedDate = "";
            if (!dateFormatDirective.isEmpty()) {
                SimpleDateFormat dateFormat = new SimpleDateFormat(dateFormatDirective);
                try {
                    Date date;
                    if (LabelMakerUtil.getLabelFieldsMap().get(labelNode.getId()) != null) {
                        if (StringUtils.isNotBlank(LabelMakerUtil.getLabelFieldsMap().get(labelNode.getId()))) {
                            String longStr = LabelMakerUtil.getLabelFieldsMap().get(labelNode.getId());
                            date = new Date(Long.parseLong(longStr));
                            formattedDate = dateFormat.format(date);
                        } else {
                            formattedDate = StringUtils.EMPTY;
                        }
                    } else {
                        date = new Date();
                        formattedDate = dateFormat.format(date);
                    }
                } catch (final Exception e) {
                    LOGGER.error("Exception occurred while formatting date using format: {}", dateFormatDirective, e);
                }
            }

            if (StringUtils.isNotBlank(title)) {
                title = title + " : ";
            }
            // Determine the final text
            String newText;
            if (StringUtils.isNotBlank(text) && StringUtils.isBlank(formattedDate)) {
                newText = title + text;
            } else {
                newText = formattedDate.isEmpty()
                        ? title + rawText.substring(0, startTag) + rawText.substring(endTag)
                        : formattedDate;
            }

            // Initialize style variables
            FontWeight fontWeight = FontWeight.NORMAL;
            boolean toUpperCase = false;

            // Handle style directives
            if (styleDirective.contains("bold")) {
                fontWeight = FontWeight.BOLD;
            }
            if (styleDirective.contains("caps")) {
                toUpperCase = true;
            }

            if (toUpperCase) {
                newText = newText.toUpperCase();
            }

            if (textNode != null) {
                textNode.setFont(Font.font(textNode.getFont().getFamily(), fontWeight, textNode.getFont().getSize()));
                textNode.setText(newText);
            } else {
                labelNode.setFont(Font.font(labelNode.getFont().getFamily(),
                        fontWeight, labelNode.getFont().getSize()));
                labelNode.setText(newText);
            }

            // Apply styles to all sibling Text nodes if parent is TextFlow
            if (textNode != null) {
                if (textNode.getParent() instanceof TextFlow textFlow) {
                    if (textFlow.lookup("#deviceTitle") == null) {
                        for (javafx.scene.Node child : textFlow.getChildren()) {
                            if (child != textNode && child instanceof Text sibling) {
                                sibling.setFont(Font.font(sibling.getFont().getFamily(),
                                        fontWeight, sibling.getFont().getSize()));
                                sibling.setText(toUpperCase ? sibling.getText().toUpperCase() : sibling.getText());
                            }
                        }
                    }
                }
            } else {
                if (labelNode.getParent() instanceof TextFlow textFlow) {
                    for (javafx.scene.Node child : textFlow.getChildren()) {
                        if (child instanceof Text sibling) {
                            sibling.setFont(Font.font(sibling.getFont().getFamily(),
                                    fontWeight, sibling.getFont().getSize()));
                            sibling.setText(toUpperCase ? sibling.getText().toUpperCase() : sibling.getText());
                        }
                    }
                }
            }

        } else if (StringUtils.isNotBlank(text)) {
            // Set the default text if no directive found
            if (textNode != null) {
                textNode.setText(text);
            } else {
                labelNode.setText(rawText);
            }
        }
    }

    /**
     * This method is used to hide battery health element if zero or null battery info
     *
     * @param label  Label element
     * @param key    Id of element
     * @param device target device
     */
    private void hideElementIfBatteryHealthZero(final Label label, final String key, final Device device) {
        if (key.equals("BatteryHealth") && label != null) {
            if (device.getBatteryInfo() == null || device.getBatteryInfo().getHealthPercentage() == 0) {
                // the battery health field parent is an AnchorPane when Battery Health value is unchecked on label
                // maker and is a TextFlow when the value is checked
                if (label.getParent() instanceof AnchorPane anchorPane) {
                    anchorPane.setVisible(false);
                } else {
                    TextFlow tf = (TextFlow) label.getParent();
                    tf.setVisible(false);
                }
            }
        }
    }

    /**
     * Based on the cosmetic customisation passed cosmetics are appended to the passed
     * test results
     *
     * @param deviceTestResult           device test results
     * @param printMode                  print mode
     * @param cosmeticFailedPassedResult cosmetic failed and passed results
     * @return the string of passed test results to be displayed on the label.
     */
    private String getPassedTesResultsToBeDisplayed(final DeviceTestResult deviceTestResult,
                                                    final PrintMode printMode,
                                                    final Triple<String, String, String> cosmeticFailedPassedResult) {

        String passedTestResults = StringUtils.EMPTY;
        String passedCosmeticTestResults = StringUtils.EMPTY;
        if (!PrintMode.TRANSACTION.equals(printMode) && cosmeticFailedPassedResult != null
                && StringUtils.isNotBlank(cosmeticFailedPassedResult.getRight())) {
            passedCosmeticTestResults = cosmeticFailedPassedResult.getRight();
        }
        if (deviceTestResult.getTestResults() != null
                && StringUtils.isNotBlank(deviceTestResult.getTestResults().toString())) {
            passedTestResults = TestResultsUtil.listToCommaSeparatedString(
                    deviceTestResult.getTestResults().getPassed());
        }
        if (StringUtils.isNotBlank(passedTestResults)
                && StringUtils.isNotBlank(passedCosmeticTestResults)) {
            return StringUtils.joinWith(",", passedCosmeticTestResults, passedTestResults);
        } else if (StringUtils.isNotBlank(passedTestResults)) {
            return passedTestResults;
        } else if (StringUtils.isNotBlank(passedCosmeticTestResults)) {
            return passedCosmeticTestResults;
        }
        return "";
    }

    /**
     * Based on the cosmetic customisation failed cosmetics are appended to the failed
     * test results
     *
     * @param deviceTestResult           target device test results
     * @param printMode                  print mode
     * @param cosmeticFailedPassedResult cosmetic failed and passed results
     * @return the string of failed test results to be displayed on the label.
     */
    private String getFailedTesResultsToBeDisplayed(final DeviceTestResult deviceTestResult,
                                                    final PrintMode printMode,
                                                    final Triple<String, String, String> cosmeticFailedPassedResult) {

        String failedTestResults = "";
        String failedCosmeticTestResults = "";
        if (!PrintMode.TRANSACTION.equals(printMode) && cosmeticFailedPassedResult != null &&
                StringUtils.isNotBlank(cosmeticFailedPassedResult.getMiddle())) {
            failedCosmeticTestResults = cosmeticFailedPassedResult.getMiddle();
        }
        if (deviceTestResult.getTestResults() != null &&
                StringUtils.isNotBlank(deviceTestResult.getTestResults().toString())) {
            failedTestResults = TestResultsUtil.listToCommaSeparatedString(
                    deviceTestResult.getTestResults().getFailed());
        }
        if (StringUtils.isNotBlank(failedTestResults) && StringUtils.isNotBlank(failedCosmeticTestResults)) {
            return StringUtils.joinWith(",", failedCosmeticTestResults, failedTestResults);
        } else if (StringUtils.isNotBlank(failedTestResults)) {
            return failedTestResults;
        } else if (StringUtils.isNotBlank(failedCosmeticTestResults)) {
            return failedCosmeticTestResults;
        }
        return "";
    }

    /**
     * Check if rotation angle is 270 or 90
     *
     * @param imageNode Image View to set barcode
     * @return boolean
     */
    private boolean isRotationAngleVertical(final ImageView imageNode) {
        Rotate rotateAngle = (imageNode != null &&
                imageNode.getTransforms() != null &&
                !imageNode.getTransforms().isEmpty()) ? (Rotate) (imageNode.getTransforms().get(0)) : null;
        return rotateAngle != null && (rotateAngle.getAngle() == 90.0 || rotateAngle.getAngle() == 270.0);

    }

    /**
     * Sets the device title for the QR code by extracting relevant properties
     * from the given {@link Device} and updating the UI text node accordingly.
     *
     * @param device target device
     */
    private void setDeviceTitleForQr(final Device device) {
        ImageView imageNode = getTitleQrNodeFromScrollPane();
        if (imageNode == null) {
            imageNode = (ImageView) getRootPane().lookup("#" + TITLE_QR_CODE_KEY);
        }
        if (imageNode != null && StringUtils.isNotBlank(imageNode.getAccessibleText())) {
            final String text = imageNode.getAccessibleText().replaceAll(".*\\{(.*?)}.*", "$1");

            // Split by semicolon and remove any empty strings
            final List<String> titleNodes = Arrays.stream(text.split(";"))
                    .filter(node -> !node.isEmpty())
                    .toList();
            StringJoiner newDeviceTitle = new StringJoiner(" ");
            //If Qr do not have make element, we must append model name
            if (titleNodes.stream()
                    .noneMatch(node -> node.equalsIgnoreCase("make"))) {
                newDeviceTitle.add(device.getModel());
            }
            titleNodes.stream()
                    .map(node -> getNodeValueFromId(device, node))
                    .filter(StringUtils::isNotBlank)
                    .forEach(newDeviceTitle::add);
            if (StringUtils.isNotBlank(newDeviceTitle.toString())) {
                LabelMakerUtil.setTitleQRCode(newDeviceTitle.toString());
            }
        }
    }

    /**
     * Retrieves the value of a specific property from the given {@link Device}.
     *
     * @param device   target device
     * @param property the key representing the property to retrieve.
     * @return the corresponding value of the property, or an empty string if the key is invalid.
     */
    private String getNodeValueFromId(final Device device, final String property) {
        return switch (property) {
            case MAKE_KEY -> device.getMake() + " " + device.getModel(); //appending model name
            case MEMORY_KEY -> device.getDiskSize() == null ? StringUtils.EMPTY :
                    String.format("%s", device.getDiskSize());
            case COLOR_KEY -> device.getColor();
            case MODEL_KEY -> device.getModelNo();
            case CARRIER_KEY -> device.getCarrier();
            default -> StringUtils.EMPTY;
        };
    }
}