package com.phonecheck.backend.listener.transaction;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.transaction.TransactionDetailsEditEvent;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class TransactionDetailsEditListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceInfoDBService deviceInfoDBService;

    @Mock
    private CloudDeviceDataSyncService cloudDeviceDataSyncService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private TransactionDetailsEditListener listener;

    @BeforeEach
    void beforeEach() {
        listener = new TransactionDetailsEditListener(mqttClient, objectMapper,
                deviceInfoDBService, cloudDeviceDataSyncService);
    }

    @Test
    public void testOnEvent() {
        Transaction transaction = Transaction.builder()
                .licenseId(1)
                .transactionId(3)
                .build();
        Device device = new IosDevice();
        device.setId("ss");

        listener.onEvent(new TransactionDetailsEditEvent(listener, device, "12", transaction));

        verify(deviceInfoDBService).updateDeviceTransactionInfo(any(IosDevice.class), any());
    }
}
