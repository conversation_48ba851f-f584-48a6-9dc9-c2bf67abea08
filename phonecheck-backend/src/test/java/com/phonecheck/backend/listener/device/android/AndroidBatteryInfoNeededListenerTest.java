package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.device.results.AndroidTestResultsService;
import com.phonecheck.info.DeviceTestPlanUtil;
import com.phonecheck.info.android.AndroidBatteryInfoService;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.battery.BatterySource;
import com.phonecheck.model.cloudapi.AndroidConfigResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidBatteryInfoNeededEvent;
import com.phonecheck.model.event.device.android.AndroidBatteryInfoSuccessEvent;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.util.CustomizationUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AndroidBatteryInfoNeededListenerTest {

    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private AndroidBatteryInfoService batteryInfoService;
    private AndroidBatteryInfoNeededListener listener;

    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private CustomizationUtil customizationUtil;
    @Mock
    private DeviceTestPlanUtil deviceTestPlanUtil;
    @Mock
    private AndroidTestResultsService androidTestResultsService;

    @BeforeEach
    void setUp() {
        listener = new AndroidBatteryInfoNeededListener(mqttClient, objectMapper, eventPublisher,
                batteryInfoService, deviceConnectionTracker, inMemoryStore, customizationUtil, deviceTestPlanUtil,
                androidTestResultsService);
    }

    @Test
    @DisplayName("Fetch battery info from samsung device using BatteryHealth from asoc")
    public void testOnEvent() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("device_id");
        device.setMake("samsung");
        device.setModelNo("A1234");
        when(deviceConnectionTracker.getDevice(any())).thenReturn(device);
        BatteryInfo expectedBatteryInfo = BatteryInfo.builder()
                .batteryPercentage(80)
                .healthPercentage(90)
                .cycle(500)
                .oemHealthPercentage(0)
                .cocoHealthPercentage(0)
                .currentCapacity(1500)
                .designedCapacity(2000)
                .cocoCurrentCapacity(0)
                .cocoDesignedCapacity(0)
                .isCharging(true)
                .source(BatterySource.BS01)
                .batteryResistance(0.0)
                .build();
        when(batteryInfoService.getBatteryHealthFromAsoc(eq(device))).thenReturn(expectedBatteryInfo);

        AndroidBatteryInfoNeededEvent event = new AndroidBatteryInfoNeededEvent(listener, device);
        listener.onEvent(event);

        verify(batteryInfoService, never()).getGoogleBatteryInfo(any());
        verify(batteryInfoService).getBatteryHealthFromAsoc(any());
        verify(batteryInfoService, never()).getBatteryHealthFromUsage(any());
        verify(batteryInfoService, never()).getBatteryHealthFromFgCycles(any());
        verify(batteryInfoService, never()).getBatteryHealthFromBatteryCycles(any());

        verify(eventPublisher).publishEvent(argThat((AndroidBatteryInfoSuccessEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getDevice().getId());
            Assertions.assertEquals(expectedBatteryInfo, ((AndroidDevice) arg.getDevice()).getBatteryInfo());
            Assertions.assertEquals(80,
                    ((AndroidDevice) arg.getDevice()).getBatteryPercentage());
            Assertions.assertEquals(90,
                    ((AndroidDevice) arg.getDevice()).getBatteryStateHealth());
            return true;
        }));
    }

    @Test
    public void testWhenHealthPercentIsBetween76And79() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("device_id");
        device.setMake("samsung");
        device.setModelNo("A1234");
        when(deviceConnectionTracker.getDevice(any())).thenReturn(device);
        BatteryInfo expectedBatteryInfo = BatteryInfo.builder()
                .batteryPercentage(80)
                .healthPercentage(76)
                .cycle(500)
                .oemHealthPercentage(0)
                .cocoHealthPercentage(0)
                .currentCapacity(1500)
                .designedCapacity(2000)
                .cocoCurrentCapacity(0)
                .cocoDesignedCapacity(0)
                .isCharging(true)
                .source(BatterySource.BS01)
                .batteryResistance(0.0)
                .build();
        when(batteryInfoService.getBatteryHealthFromAsoc(eq(device))).thenReturn(expectedBatteryInfo);

        AndroidBatteryInfoNeededEvent event = new AndroidBatteryInfoNeededEvent(listener, device);
        listener.onEvent(event);

        verify(batteryInfoService, never()).getGoogleBatteryInfo(any());
        verify(batteryInfoService).getBatteryHealthFromAsoc(any());
        verify(batteryInfoService, never()).getBatteryHealthFromUsage(any());
        verify(batteryInfoService, never()).getBatteryHealthFromFgCycles(any());
        verify(batteryInfoService, never()).getBatteryHealthFromBatteryCycles(any());

        verify(eventPublisher).publishEvent(argThat((AndroidBatteryInfoSuccessEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getDevice().getId());
            Assertions.assertEquals(expectedBatteryInfo, ((AndroidDevice) arg.getDevice()).getBatteryInfo());
            Assertions.assertEquals(80,
                    ((AndroidDevice) arg.getDevice()).getBatteryPercentage());
            // if health percent was between 76-79 then
            // the healthPercent and batteryStateHealth will be rounded off to 80
            Assertions.assertEquals(80,
                    ((AndroidDevice) arg.getDevice()).getBatteryStateHealth());
            return true;
        }));
    }

    @Test
    @DisplayName("Fetch battery info from Google device using Google Battery Info method")
    public void getBatteryInfoTest() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("device_id");
        device.setMake("google");
        device.setModelNo("A1234");
        when(deviceConnectionTracker.getDevice(any())).thenReturn(device);
        BatteryInfo expectedBatteryInfo = BatteryInfo.builder()
                .batteryPercentage(80)
                .healthPercentage(90)
                .cycle(500)
                .oemHealthPercentage(0)
                .cocoHealthPercentage(0)
                .currentCapacity(1500)
                .designedCapacity(2000)
                .cocoCurrentCapacity(0)
                .cocoDesignedCapacity(0)
                .isCharging(true)
                .source(BatterySource.BS01)
                .batteryResistance(0.0)
                .build();
        when(batteryInfoService.getGoogleBatteryInfo(eq(device))).thenReturn(expectedBatteryInfo);

        AndroidBatteryInfoNeededEvent event = new AndroidBatteryInfoNeededEvent(listener, device);
        listener.onEvent(event);

        verify(batteryInfoService).getGoogleBatteryInfo(any());
        verify(batteryInfoService, never()).getBatteryHealthFromCycleCount(any());
        verify(batteryInfoService, never()).getBatteryHealthFromFgCycles(any());
        verify(batteryInfoService, never()).getBatteryHealthFromBatteryCycles(any());

        verify(eventPublisher).publishEvent(argThat((AndroidBatteryInfoSuccessEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getDevice().getId());
            Assertions.assertEquals(expectedBatteryInfo, ((AndroidDevice) arg.getDevice()).getBatteryInfo());
            Assertions.assertEquals(80,
                    ((AndroidDevice) arg.getDevice()).getBatteryPercentage());
            Assertions.assertEquals(90,
                    ((AndroidDevice) arg.getDevice()).getBatteryStateHealth());
            return true;
        }));
    }

    @Test
    @DisplayName("Fetch battery info from Google device using cycle count method")
    public void getBatteryInfoTest1() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("device_id");
        device.setMake("google");
        device.setModelNo("A1234");
        when(deviceConnectionTracker.getDevice(any())).thenReturn(device);
        BatteryInfo expectedBatteryInfo = BatteryInfo.builder()
                .batteryPercentage(80)
                .healthPercentage(90)
                .cycle(500)
                .oemHealthPercentage(0)
                .cocoHealthPercentage(0)
                .currentCapacity(1500)
                .designedCapacity(2000)
                .cocoCurrentCapacity(0)
                .cocoDesignedCapacity(0)
                .isCharging(true)
                .source(BatterySource.BS01)
                .batteryResistance(0.0)
                .build();
        when(batteryInfoService.getGoogleBatteryInfo(eq(device))).thenReturn(null);
        when(batteryInfoService.getBatteryHealthFromCycleCount(eq(device))).thenReturn(expectedBatteryInfo);

        AndroidBatteryInfoNeededEvent event = new AndroidBatteryInfoNeededEvent(listener, device);
        listener.onEvent(event);

        verify(batteryInfoService).getGoogleBatteryInfo(any());
        verify(batteryInfoService).getBatteryHealthFromCycleCount(any());
        verify(batteryInfoService, never()).getBatteryHealthFromFgCycles(any());
        verify(batteryInfoService, never()).getBatteryHealthFromBatteryCycles(any());

        verify(eventPublisher).publishEvent(argThat((AndroidBatteryInfoSuccessEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getDevice().getId());
            Assertions.assertEquals(expectedBatteryInfo, ((AndroidDevice) arg.getDevice()).getBatteryInfo());
            Assertions.assertEquals(80,
                    ((AndroidDevice) arg.getDevice()).getBatteryPercentage());
            Assertions.assertEquals(90,
                    ((AndroidDevice) arg.getDevice()).getBatteryStateHealth());
            return true;
        }));
    }

    @Test
    @DisplayName("Fetch battery info from Google device using FG Cycles method")
    public void getBatteryInfoTest2() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("device_id");
        device.setMake("google");
        device.setModelNo("A1234");
        when(deviceConnectionTracker.getDevice(any())).thenReturn(device);
        BatteryInfo expectedBatteryInfo = BatteryInfo.builder()
                .batteryPercentage(80)
                .healthPercentage(90)
                .cycle(500)
                .oemHealthPercentage(0)
                .cocoHealthPercentage(0)
                .currentCapacity(1500)
                .designedCapacity(2000)
                .cocoCurrentCapacity(0)
                .cocoDesignedCapacity(0)
                .isCharging(true)
                .source(BatterySource.BS01)
                .batteryResistance(0.0)
                .build();
        when(batteryInfoService.getGoogleBatteryInfo(eq(device))).thenReturn(null);
        when(batteryInfoService.getBatteryHealthFromCycleCount(eq(device))).thenReturn(null);
        when(batteryInfoService.getBatteryHealthFromFgCycles(eq(device))).thenReturn(expectedBatteryInfo);

        AndroidBatteryInfoNeededEvent event = new AndroidBatteryInfoNeededEvent(listener, device);
        listener.onEvent(event);

        verify(batteryInfoService).getGoogleBatteryInfo(any());
        verify(batteryInfoService).getBatteryHealthFromCycleCount(any());
        verify(batteryInfoService).getBatteryHealthFromFgCycles(any());
        verify(batteryInfoService, never()).getBatteryHealthFromBatteryCycles(any());

        verify(eventPublisher).publishEvent(argThat((AndroidBatteryInfoSuccessEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getDevice().getId());
            Assertions.assertEquals(expectedBatteryInfo, ((AndroidDevice) arg.getDevice()).getBatteryInfo());
            Assertions.assertEquals(80,
                    ((AndroidDevice) arg.getDevice()).getBatteryPercentage());
            Assertions.assertEquals(90,
                    ((AndroidDevice) arg.getDevice()).getBatteryStateHealth());
            return true;
        }));
    }

    @Test
    @DisplayName("Fetch battery info from Google device using Battery Cycles method")
    public void getBatteryInfoTest3() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("device_id");
        device.setMake("google");
        device.setModelNo("A1234");
        when(deviceConnectionTracker.getDevice(any())).thenReturn(device);
        BatteryInfo expectedBatteryInfo = BatteryInfo.builder()
                .batteryPercentage(80)
                .healthPercentage(90)
                .cycle(500)
                .oemHealthPercentage(0)
                .cocoHealthPercentage(0)
                .currentCapacity(1500)
                .designedCapacity(2000)
                .cocoCurrentCapacity(0)
                .cocoDesignedCapacity(0)
                .isCharging(true)
                .source(BatterySource.BS01)
                .batteryResistance(0.0)
                .build();
        when(batteryInfoService.getGoogleBatteryInfo(eq(device))).thenReturn(null);
        when(batteryInfoService.getBatteryHealthFromCycleCount(eq(device))).thenReturn(null);
        when(batteryInfoService.getBatteryHealthFromFgCycles(eq(device))).thenReturn(null);
        when(batteryInfoService.getBatteryHealthFromBatteryCycles(eq(device))).thenReturn(expectedBatteryInfo);

        AndroidBatteryInfoNeededEvent event = new AndroidBatteryInfoNeededEvent(listener, device);
        listener.onEvent(event);

        verify(batteryInfoService).getGoogleBatteryInfo(any());
        verify(batteryInfoService).getBatteryHealthFromCycleCount(any());
        verify(batteryInfoService).getBatteryHealthFromFgCycles(any());
        verify(batteryInfoService).getBatteryHealthFromBatteryCycles(any());

        verify(eventPublisher).publishEvent(argThat((AndroidBatteryInfoSuccessEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getDevice().getId());
            Assertions.assertEquals(expectedBatteryInfo, ((AndroidDevice) arg.getDevice()).getBatteryInfo());
            Assertions.assertEquals(80,
                    ((AndroidDevice) arg.getDevice()).getBatteryPercentage());
            Assertions.assertEquals(90,
                    ((AndroidDevice) arg.getDevice()).getBatteryStateHealth());
            return true;
        }));
    }

    @Test
    @DisplayName("Fetch battery info from battery ocr message for 80% health")
    public void getBatteryInfoTest4() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("device_id");
        device.setMake("sharp");
        device.setModelNo("A1234");
        when(inMemoryStore.getCurrentLanguage()).thenReturn("Japanese");
        when(customizationUtil.isBatteryWarningCustomizationEnabled()).thenReturn(true);
        when(batteryInfoService.getBatteryMessageWithOCR(anyString(), anyString())).thenReturn(Pair.of(Boolean.TRUE,
                Boolean.TRUE));
        when(deviceConnectionTracker.getDevice(any())).thenReturn(device);
        BatteryInfo expectedBatteryInfo = BatteryInfo.builder()
                .batteryPercentage(80)
                .healthPercentage(80)
                .cycle(500)
                .oemHealthPercentage(0)
                .cocoHealthPercentage(0)
                .currentCapacity(1500)
                .designedCapacity(2000)
                .cocoCurrentCapacity(0)
                .cocoDesignedCapacity(0)
                .isCharging(true)
                .source(BatterySource.BS01)
                .batteryResistance(0.0)
                .build();

        when(batteryInfoService.getBatteryHealthFromBatteryCycles(eq(device))).thenReturn(expectedBatteryInfo);
        AndroidBatteryInfoNeededEvent event = new AndroidBatteryInfoNeededEvent(listener, device);
        listener.onEvent(event);

        verify(eventPublisher).publishEvent(argThat((AndroidBatteryInfoSuccessEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getDevice().getId());
            Assertions.assertEquals(expectedBatteryInfo, ((AndroidDevice) arg.getDevice()).getBatteryInfo());
            Assertions.assertEquals(80,
                    (arg.getDevice()).getBatteryPercentage());
            Assertions.assertEquals(74,
                    (arg.getDevice()).getBatteryStateHealth());
            Assertions.assertEquals(0,
                    (arg.getDevice()).getBatteryInfo().getCycle());
            return true;
        }));
    }

    @Test
    @DisplayName("Adjust battery info when there is no battery warning and health is less than 80")
    public void getBatteryInfoWithoutBatteryWarning() throws IOException {
        AndroidDevice device = new AndroidDevice();
        device.setId("device_id");
        device.setMake("Samsung");
        device.setModelNo("A1234");
        when(deviceConnectionTracker.getDevice(any())).thenReturn(device);
        when(inMemoryStore.getCurrentLanguage()).thenReturn("Japanese");
        BatteryInfo expectedBatteryInfo = BatteryInfo.builder()
                .batteryPercentage(80)
                .healthPercentage(73)
                .cycle(500)
                .oemHealthPercentage(0)
                .cocoHealthPercentage(0)
                .currentCapacity(1500)
                .designedCapacity(2000)
                .cocoCurrentCapacity(0)
                .cocoDesignedCapacity(0)
                .isCharging(true)
                .source(BatterySource.BS01)
                .batteryResistance(0.0)
                .build();
        when(batteryInfoService.getBatteryHealthFromAsoc(eq(device))).thenReturn(expectedBatteryInfo);
        when(batteryInfoService.getBatteryMessageWithOCR(device.getId(), "Japanese"))
                .thenReturn(Pair.of(Boolean.FALSE, Boolean.FALSE));

        AndroidBatteryInfoNeededEvent event = new AndroidBatteryInfoNeededEvent(listener, device);
        listener.onEvent(event);

        verify(batteryInfoService, never()).getBatteryHealthFromCycleCount(any());
        verify(batteryInfoService, never()).getBatteryHealthFromFgCycles(any());
        verify(batteryInfoService, never()).getBatteryHealthFromBatteryCycles(any());
        verify(eventPublisher).publishEvent(argThat((AndroidBatteryInfoSuccessEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getDevice().getId());
            Assertions.assertEquals(expectedBatteryInfo, (arg.getDevice()).getBatteryInfo());
            Assertions.assertEquals(84,
                    (arg.getDevice()).getBatteryInfo().getHealthPercentage());
            return true;
        }));
    }

    @Test
    public void testWhenInBatteryIsSetAsNo() {
        AndroidDevice device = new AndroidDevice();
        device.setId("device_id");
        device.setMake("Samsung");
        device.setModelNo("A1234");
        when(deviceConnectionTracker.getDevice(any())).thenReturn(device);

        Map<String, AndroidConfigResponse> androidDeviceFeatures = new HashMap<>();
        androidDeviceFeatures.put(device.getMake() + device.getModelNo(), new AndroidConfigResponse());
        AndroidBatteryInfoNeededEvent event = new AndroidBatteryInfoNeededEvent(listener, device);
        listener.onEvent(event);

        verify(inMemoryStore, never()).getCurrentLanguage();
        verify(eventPublisher, never()).publishEvent(any(AndroidBatteryInfoSuccessEvent.class));
    }

    @Test
    public void testWhenInBatteryIsSetAsYes() {
        AndroidDevice device = new AndroidDevice();
        device.setId("device_id");
        device.setMake("Samsung");
        device.setModelNo("A1234");
        when(deviceConnectionTracker.getDevice(any())).thenReturn(device);

        Map<String, AndroidConfigResponse> androidDeviceFeatures = new HashMap<>();
        AndroidConfigResponse configResponse = new AndroidConfigResponse();
        configResponse.setHasBuiltInBattery(true);
        androidDeviceFeatures.put(device.getMake() + device.getModelNo(), configResponse);
        BatteryInfo expectedBatteryInfo = BatteryInfo.builder()
                .batteryPercentage(80)
                .healthPercentage(90)
                .cycle(500)
                .oemHealthPercentage(0)
                .cocoHealthPercentage(0)
                .currentCapacity(1500)
                .designedCapacity(2000)
                .cocoCurrentCapacity(0)
                .cocoDesignedCapacity(0)
                .isCharging(true)
                .source(BatterySource.BS01)
                .batteryResistance(0.0)
                .build();
        when(batteryInfoService.getBatteryHealthFromAsoc(eq(device))).thenReturn(expectedBatteryInfo);

        AndroidBatteryInfoNeededEvent event = new AndroidBatteryInfoNeededEvent(listener, device);
        listener.onEvent(event);

        verify(batteryInfoService, never()).getGoogleBatteryInfo(any());
        verify(batteryInfoService).getBatteryHealthFromAsoc(any());
        verify(batteryInfoService, never()).getBatteryHealthFromUsage(any());
        verify(batteryInfoService, never()).getBatteryHealthFromFgCycles(any());
        verify(batteryInfoService, never()).getBatteryHealthFromBatteryCycles(any());

        verify(eventPublisher).publishEvent(argThat((AndroidBatteryInfoSuccessEvent arg) -> {
            Assertions.assertNotNull(arg);
            Assertions.assertEquals(device.getId(), arg.getDevice().getId());
            Assertions.assertEquals(expectedBatteryInfo, ((AndroidDevice) arg.getDevice()).getBatteryInfo());
            Assertions.assertEquals(80,
                    ((AndroidDevice) arg.getDevice()).getBatteryPercentage());
            Assertions.assertEquals(90,
                    ((AndroidDevice) arg.getDevice()).getBatteryStateHealth());
            return true;
        }));
    }
 }
