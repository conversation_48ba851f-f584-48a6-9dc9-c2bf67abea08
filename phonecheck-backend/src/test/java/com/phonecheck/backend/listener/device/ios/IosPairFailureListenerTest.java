package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.PairFailureStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosPairFailureEvent;
import com.phonecheck.model.status.PairStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IosPairFailureListenerTest {

    @Mock
    private IMqttAsyncClient mqttClient;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private DeviceStageUpdater deviceStageUpdater;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    private IosPairFailureListener listener;

    @BeforeEach
    void beforeEach() {
        listener = new IosPairFailureListener(mqttClient, objectMapper,
                eventPublisher, deviceStageUpdater,
                inMemoryStore, deviceConnectionTracker);
    }

    @Test
    public void testPairFailed() {
        IosDevice device = new IosDevice();
        device.setId("123456789");

        PairFailureStage expectedStage = PairFailureStage
                .builder()
                .id(device.getId())
                .status(PairStatus.FAILED_NO_DEVICE)
                .transactionId("123")
                .timestamp(System.currentTimeMillis())
                .build();
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(deviceConnectionTracker.getDevice(anyString())).thenReturn(device);

        listener.onEvent(new IosPairFailureEvent(listener, device, PairStatus.FAILED_NO_DEVICE));

        Mockito.verify(deviceStageUpdater).updateStage(argThat((PairFailureStage arg) -> {
            Assertions.assertEquals(expectedStage.getId(), arg.getId());
            Assertions.assertEquals(expectedStage.getStatus(), arg.getStatus());
            Assertions.assertEquals(expectedStage.getTransactionId(), arg.getTransactionId());
            return true;
        }));
    }

    @Test
    public void testPairFailedDeviceDisconnected() {
        IosDevice device = new IosDevice();
        device.setId("123456789");

        when(deviceConnectionTracker.getDevice(anyString())).thenReturn(null);

        listener.onEvent(new IosPairFailureEvent(listener, device, PairStatus.FAILED_NO_DEVICE));

        Mockito.verifyNoInteractions(deviceStageUpdater);
    }
}
