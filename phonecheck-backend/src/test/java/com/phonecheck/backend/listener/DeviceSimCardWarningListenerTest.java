package com.phonecheck.backend.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.info.android.AndroidDeviceInfoService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceSimCardWarningEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceSimCardDefectMessage;
import com.phonecheck.model.status.AuthorizationStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.InitialDefectKey;
import com.phonecheck.model.test.TestResults;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DeviceSimCardWarningListenerTest {

    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private IosDeviceInfoService iosDeviceInfoService;
    @Mock
    private AndroidDeviceInfoService androidDeviceInfoService;
    @Mock
    private DeviceTestResultDBService deviceTestResultDBService;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private DeviceSimCardWarningListener listener;

    private final ObjectMapper objectMapper = new ObjectMapper();


    @BeforeEach
    void setUp() {
        listener = new DeviceSimCardWarningListener(
                mqttClient, objectMapper, iosDeviceInfoService, androidDeviceInfoService,
                deviceTestResultDBService, inMemoryStore, deviceConnectionTracker, eventPublisher
        );
        when(mqttClient.isConnected()).thenReturn(true);

        CloudCustomizationResponse customization = CloudCustomizationResponse.builder()
                .workflow(CloudCustomizationResponse.WorkflowSettings.builder()
                        .warningMessages(CloudCustomizationResponse.WarningMessage.builder()
                                .isFailSimCardNotRemoved(true)
                                .build())
                        .build())
                .build();
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(customization);

    }

    @Test
    void testOnEvent() throws Exception {
        AndroidDevice device = new AndroidDevice();
        device.setId("123");
        device.setSerial("ANDROID123");
        Transaction trans = new Transaction();
        trans.setTransactionId(4123);
        when(inMemoryStore.getTransaction()).thenReturn(trans);
        device.setAuthorizationStatus(AuthorizationStatus.AUTHORIZED);
        DeviceTestResult testResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>());
        testResult.setTestResults(new TestResults());
        device.setDeviceTestResult(testResult);

        when(deviceConnectionTracker.getDevice("123")).thenReturn(device);
        when(androidDeviceInfoService.isSimCardCheckEnabledAndSimDetected(device)).thenReturn(true);

        listener.onEvent(new DeviceSimCardWarningEvent(this, device, false, false));

        verify(deviceTestResultDBService).updateFailedTests(
                "4123", device.getSerial(), InitialDefectKey.SIM_CARD_DETECTED.getKey());

        ArgumentCaptor<MqttMessage> argumentCaptor = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient).publish(argThat(topic -> {
            assertEquals(TopicBuilder.build(device, "sim-card", "defect"), topic);
            return true;
        }), argumentCaptor.capture());

        DeviceSimCardDefectMessage message = objectMapper.readValue(
                argumentCaptor.getValue().getPayload(), DeviceSimCardDefectMessage.class);

        Assertions.assertTrue(message.isDefectToBeAdded());
        Assertions.assertEquals(device.getId(), message.getDeviceId());
    }

    @Test
    void testOnEvent2() throws Exception {
        IosDevice device = new IosDevice();
        device.setId("456");
        device.setSerial("IOS456");
        Transaction trans = new Transaction();
        trans.setTransactionId(4123);
        when(inMemoryStore.getTransaction()).thenReturn(trans);
        DeviceTestResult testResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>());
        testResult.setTestResults(new TestResults());
        device.setDeviceTestResult(testResult);
        when(deviceConnectionTracker.getDevice("456")).thenReturn(device);
        when(iosDeviceInfoService.isSimCardCheckEnabledAndSimDetected(device)).thenReturn(true);

        listener.onEvent(new DeviceSimCardWarningEvent(this, device, false, false));

        verify(deviceTestResultDBService).updateFailedTests(
                "4123", device.getSerial(), InitialDefectKey.SIM_CARD_DETECTED.getKey());

        ArgumentCaptor<MqttMessage> argumentCaptor = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient).publish(argThat(topic -> {
            assertEquals(TopicBuilder.build(device, "sim-card", "defect"), topic);
            return true;
        }), argumentCaptor.capture());

        DeviceSimCardDefectMessage message = objectMapper.readValue(
                argumentCaptor.getValue().getPayload(), DeviceSimCardDefectMessage.class);

        Assertions.assertTrue(message.isDefectToBeAdded());
        Assertions.assertEquals(device.getId(), message.getDeviceId());
    }
}