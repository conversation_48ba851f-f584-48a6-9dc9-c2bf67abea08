package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudEsnCheckService;
import com.phonecheck.backend.util.ESNUtil;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.EsnLicenseCheckRequest;
import com.phonecheck.model.cloudapi.EsnResponse;
import com.phonecheck.model.constants.EsnFieldColor;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.customization.AutomationWorkflowStatus;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.AbstractDeviceEvent;
import com.phonecheck.model.event.device.AsyncEsnRequestEvent;
import com.phonecheck.model.ios.EsnCheckType;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.EsnResponseMessage;
import com.phonecheck.model.service.EsnCheckInfo;
import com.phonecheck.model.status.EsnStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.TimerLoggerUtil;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DeviceEsnCheckListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;

    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private CloudEsnCheckService esnCheckService;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private ESNUtil esnUtil;
    @Mock
    private DeviceStageUpdater stageUpdater;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private DeviceEsnCheckListener listener;
    private IosDevice device;

    private final ObjectMapper mapper = new ObjectMapper();

    @BeforeEach
    void beforeEach() {
        device = new IosDevice();
        device.setImei("1123");
        device.setId("123456");
        listener = new DeviceEsnCheckListener(esnCheckService, mqttClient, deviceConnectionTracker, objectMapper,
                inMemoryStore, esnUtil, stageUpdater, timerLoggerUtil, eventPublisher);
    }

    @Test
    public void testOnEsnCheck() throws MqttException {
        device.setCarrier("Verizon");

        HashMap<AutomationWorkflow, AutomationWorkflowStatus> workflowMap = new HashMap<>();
        workflowMap.put(AutomationWorkflow.TEST_RESULTS, AutomationWorkflowStatus.FAILED_REQUIRED_FIELDS);
        device.setPreviouslyRanAutomation(workflowMap);

        Mockito.when(inMemoryStore.getUserName()).thenReturn("userId");
        Mockito.when(inMemoryStore.getApiKey()).thenReturn("api");
        Mockito.when(inMemoryStore.getTesterId()).thenReturn("tester");
        Mockito.when(inMemoryStore.getWarehouseId()).thenReturn("warehouse");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        Mockito.when(esnUtil.getESNServiceId(eq("Verizon"),
                eq(EsnCheckType.ESN_CHECK))).thenReturn("9");

        EsnLicenseCheckRequest esnRequest = EsnLicenseCheckRequest.builder().
                imei(device.getImei()).
                serviceId("9").
                userId(inMemoryStore.getUserName()).
                testerId(inMemoryStore.getTesterId()).
                carrier(device.getCarrier()).
                apiKey(inMemoryStore.getApiKey()).
                meid(device.getMeid()).
                isMeid(StringUtils.isBlank(device.getMeid())).
                warehouseId(inMemoryStore.getWarehouseId()).
                deviceType(device.getDeviceType().toString()).
                checkAll(0).build();

        EsnResponse.EsnApiResponse response = EsnResponse.EsnApiResponse.builder().
                imei(device.getImei()).
                carrier("Verizon").
                api("S001").
                rawResponse("Model: Apple iPhone X 64GB in Space GrayIMEI: 1123 " +
                        "ESN Status: BlacklistLost or Stolen.Status Code: LOST STOLEN" +
                        "Part Number: IPHONEX-NVZW-CDMALDevice SKU: SKU2720145 Carrier: Verizon").
                remarks("Bad").
                chargeStatus("No").
                fallbackCall("No").
                fieldColor("Red").build();

        EsnCheckInfo esnCheckInfo = EsnCheckInfo
                .builder()
                .esnApiResults(List.of(response))
                .build();

        String expectedEsnResponse = """
                API: S001
                date : 2020-11-23 10:36:27
                by : Orange PCS Ltd
                action : Black Insert
                Country : United Kingdom
                operatingsys : iOS
                greyliststatus : No
                modelname : iPhone 12 Pro (A2407)
                responsestatus : success
                refcode : 11052023061349
                marketingname : Apple iPhone 12 Pro (A2407)
                brandname : Apple
                blackliststatus : Yes
                devicetype : Smartphone
                manufacturer : Apple Inc""";

        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        Mockito.when(esnCheckService.getEsnInfo(esnRequest, false)).thenReturn(esnCheckInfo);
        Mockito.when(esnUtil.getEsnContent(esnCheckInfo)).thenReturn(expectedEsnResponse);
        Mockito.when(esnUtil.getESNStatus(esnCheckInfo)).thenReturn(EsnStatus.ESN_BAD);
        Mockito.when(esnUtil.getESNFieldColor(esnCheckInfo)).thenReturn(EsnFieldColor.RED);
        Mockito.when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(
                CloudCustomizationResponse.builder()
                        .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder()
                                .build())
                                .build());

        final AsyncEsnRequestEvent event = new
                AsyncEsnRequestEvent(listener, device, EsnCheckType.ESN_CHECK);
        listener.onEvent(event);

        Mockito.verify(esnCheckService).getEsnInfo(esnRequest, false);
        Mockito.verify(esnUtil).getEsnContent(esnCheckInfo);

        ArgumentCaptor<AbstractDeviceEvent> captor = ArgumentCaptor.forClass(AbstractDeviceEvent.class);
        verify(eventPublisher, times(1)).publishEvent(captor.capture());

        // Make sure the esn check response MQTT message was published
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "esn", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final EsnResponseMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        EsnResponseMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(EsnStatus.ESN_BAD, message.getEsnStatus());
                assertEquals(EsnFieldColor.RED, message.getEsnFieldColor());
                assertEquals(expectedEsnResponse, message.getEsnUIResponse());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
    }

    @Test
    public void testOnEsnCheckBlacklist() throws MqttException {
        device.setCarrier("Verizon");

        Mockito.when(inMemoryStore.getUserName()).thenReturn("userId");
        Mockito.when(inMemoryStore.getApiKey()).thenReturn("api");
        Mockito.when(inMemoryStore.getTesterId()).thenReturn("tester");
        Mockito.when(inMemoryStore.getWarehouseId()).thenReturn("warehouse");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        Mockito.when(esnUtil.getESNServiceId(eq("Verizon"),
                eq(EsnCheckType.ESN_CHECK_BLACKLIST))).thenReturn("-1");

        EsnLicenseCheckRequest esnRequest = EsnLicenseCheckRequest.builder().
                imei(device.getImei()).
                serviceId("-1").
                userId(inMemoryStore.getUserName()).
                testerId(inMemoryStore.getTesterId()).
                carrier(device.getCarrier()).
                apiKey(inMemoryStore.getApiKey()).
                meid(device.getMeid()).
                isMeid(StringUtils.isBlank(device.getMeid())).
                warehouseId(inMemoryStore.getWarehouseId()).
                deviceType(device.getDeviceType().toString()).
                checkAll(0).build();

        EsnResponse.EsnApiResponse response1 = EsnResponse.EsnApiResponse.builder().
                imei(device.getImei()).
                carrier("Verizon").
                api("G001").
                rawResponse("whatever").
                remarks("Bad").
                chargeStatus("no").
                fallbackCall("ss").
                fieldColor("Red").build();

        EsnCheckInfo esnCheckInfo = EsnCheckInfo
                .builder()
                .esnApiResults(List.of(response1))
                .build();

        String expectedEsnResponse
                = "API: G001<br>" +
                "Remarks: Bad<br>" +
                "Field Color: Red<br>" +
                "IMEI: 359408085993451<br>" +
                "Carrier: not available<br>" +
                "FallBackCall: No<br>Charge Status: No<br>" +
                "Ref Code: 21072023070419<br>" +
                "Device Type: Smartphone<br>" +
                "Marketing Name: Apple iPhone X (A1901)<br>" +
                "Brand Name: Apple<br>" +
                "Manufacturer: Apple Inc<br>" +
                "Response Status: success<br>" +
                "Black List Status: Yes<br>" +
                "Grey List Status: No<br>" +
                "Operating System: iOS<br>" +
                "Model Name: iPhone X (A1901)<br>" +
                "<br>------------------------------------------------<br><br>";

        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        Mockito.when(esnCheckService.getEsnInfo(esnRequest, false)).thenReturn(esnCheckInfo);
        Mockito.when(esnUtil.getEsnContent(esnCheckInfo)).thenReturn(expectedEsnResponse);
        Mockito.when(esnUtil.getESNStatus(esnCheckInfo)).thenReturn(EsnStatus.ESN_BAD);
        Mockito.when(esnUtil.getESNFieldColor(esnCheckInfo)).thenReturn(EsnFieldColor.RED);
        Mockito.when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(
                CloudCustomizationResponse.builder()
                        .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder()
                                .build())
                        .build());

        final AsyncEsnRequestEvent event =
                new AsyncEsnRequestEvent(listener, device, EsnCheckType.ESN_CHECK_BLACKLIST);
        listener.onEvent(event);

        Mockito.verify(esnCheckService).getEsnInfo(esnRequest, false);

        // Make sure the esn check blacklist response MQTT message was published
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "esn", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final EsnResponseMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        EsnResponseMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(EsnStatus.ESN_BAD, message.getEsnStatus());
                assertEquals(EsnFieldColor.RED, message.getEsnFieldColor());
                assertEquals(expectedEsnResponse, message.getEsnUIResponse());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
    }

    @Test
    public void testOnEsnCheckAll() throws MqttException {
        Mockito.when(inMemoryStore.getUserName()).thenReturn("userId");
        Mockito.when(inMemoryStore.getApiKey()).thenReturn("api");
        Mockito.when(inMemoryStore.getTesterId()).thenReturn("tester");
        Mockito.when(inMemoryStore.getWarehouseId()).thenReturn("warehouse");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        Mockito.when(esnUtil.getESNServiceId(eq(null), eq(EsnCheckType.ESN_CHECK_ALL))).thenReturn("-1");

        EsnLicenseCheckRequest esnRequest = EsnLicenseCheckRequest.builder().
                imei(device.getImei()).
                serviceId("-1").
                userId(inMemoryStore.getUserName()).
                testerId(inMemoryStore.getTesterId()).
                carrier(device.getCarrier()).
                apiKey(inMemoryStore.getApiKey()).
                meid(device.getMeid()).
                isMeid(StringUtils.isBlank(device.getMeid())).
                warehouseId(inMemoryStore.getWarehouseId()).
                deviceType(device.getDeviceType().toString()).
                checkAll(1).build();

        EsnResponse.EsnApiResponse response1 = EsnResponse.EsnApiResponse.builder().
                imei(device.getImei()).
                carrier("Verizon").
                api("G001").
                rawResponse("whatever").
                remarks("Good").
                chargeStatus("no").
                fallbackCall("ss").
                fieldColor("green").build();

        EsnResponse.EsnApiResponse response2 = EsnResponse.EsnApiResponse.builder().
                imei(device.getImei()).
                api("S001").
                rawResponse("whatever").
                remarks("Good").
                chargeStatus("No").
                fallbackCall("No").
                fieldColor("green").build();

        EsnCheckInfo esnCheckInfo = EsnCheckInfo
                .builder()
                .esnApiResults(List.of(response1, response2))
                .build();

        String expectedEsnResponse = """
                API: G001
                date : 2020-11-23 10:36:27
                by : Orange PCS Ltd
                action : Black Insert
                Country : United Kingdom
                operatingsys : iOS
                greyliststatus : No
                modelname : iPhone 12 Pro (A2407)
                responsestatus : success
                refcode : 11052023061349
                marketingname : Apple iPhone 12 Pro (A2407)
                brandname : Apple
                blackliststatus : Yes
                devicetype : Smartphone
                manufacturer : Apple Inc""";


        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        Mockito.when(esnCheckService.getEsnInfo(esnRequest, false)).thenReturn(esnCheckInfo);
        Mockito.when(esnUtil.getEsnContent(esnCheckInfo)).thenReturn(expectedEsnResponse);
        Mockito.when(esnUtil.getESNStatus(esnCheckInfo)).thenReturn(EsnStatus.ESN_GOOD);
        Mockito.when(esnUtil.getESNFieldColor(esnCheckInfo)).thenReturn(EsnFieldColor.GREEN);
        Mockito.when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(
                CloudCustomizationResponse.builder()
                        .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder()
                                .build())
                        .build());

        final AsyncEsnRequestEvent event =
                new AsyncEsnRequestEvent(listener, device, EsnCheckType.ESN_CHECK_ALL);
        listener.onEvent(event);
        Mockito.verify(esnCheckService).getEsnInfo(esnRequest, false);
        Mockito.verify(esnUtil).getEsnContent(esnCheckInfo);

        // Make sure the esn check all response MQTT message was published
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "esn", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final EsnResponseMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        EsnResponseMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(EsnStatus.ESN_GOOD, message.getEsnStatus());
                assertEquals(EsnFieldColor.GREEN, message.getEsnFieldColor());
                assertEquals(expectedEsnResponse, message.getEsnUIResponse());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
    }

    @Test
    public void testOnEsnCheckWithCustomization1() throws MqttException {
        device.setCarrier("AT&T");

        Mockito.when(inMemoryStore.getUserName()).thenReturn("userId");
        Mockito.when(inMemoryStore.getApiKey()).thenReturn("api");
        Mockito.when(inMemoryStore.getTesterId()).thenReturn("tester");
        Mockito.when(inMemoryStore.getWarehouseId()).thenReturn("warehouse");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        Mockito.when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(
                CloudCustomizationResponse.builder()
                        .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder()
                                .configuration(CloudCustomizationResponse.ImeiCheckConfiguration.builder()
                                        .attAction("CHECK_ATT")
                                        .build())
                                .build())
                        .build()
        );

        Mockito.when(esnUtil.getESNServiceId(eq("AT&T"), eq(EsnCheckType.ESN_CHECK))).thenReturn("12");

        EsnLicenseCheckRequest esnRequest = EsnLicenseCheckRequest.builder().
                imei(device.getImei()).
                serviceId("12").
                userId(inMemoryStore.getUserName()).
                testerId(inMemoryStore.getTesterId()).
                carrier(device.getCarrier()).
                apiKey(inMemoryStore.getApiKey()).
                meid(device.getMeid()).
                isMeid(StringUtils.isBlank(device.getMeid())).
                warehouseId(inMemoryStore.getWarehouseId()).
                deviceType(device.getDeviceType().toString()).
                checkAll(0).build();

        EsnResponse.EsnApiResponse response = EsnResponse.EsnApiResponse.builder().
                imei(device.getImei()).
                carrier("Verizon").
                api("S001").
                rawResponse("Model: Apple iPhone X 64GB in Space GrayIMEI: 1123 " +
                        "ESN Status: BlacklistLost or Stolen.Status Code: LOST STOLEN" +
                        "Part Number: IPHONEX-NVZW-CDMALDevice SKU: SKU2720145 Carrier: Verizon").
                remarks("Bad").
                chargeStatus("No").
                fallbackCall("No").
                fieldColor("Red").build();

        EsnCheckInfo esnCheckInfo = EsnCheckInfo
                .builder()
                .esnApiResults(List.of(response))
                .build();

        String expectedEsnResponse = """
                API: S001
                date : 2020-11-23 10:36:27
                by : Orange PCS Ltd
                action : Black Insert
                Country : United Kingdom
                operatingsys : iOS
                greyliststatus : No
                modelname : iPhone 12 Pro (A2407)
                responsestatus : success
                refcode : 11052023061349
                marketingname : Apple iPhone 12 Pro (A2407)
                brandname : Apple
                blackliststatus : Yes
                devicetype : Smartphone
                manufacturer : Apple Inc""";

        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        Mockito.when(esnCheckService.getEsnInfo(esnRequest, false)).thenReturn(esnCheckInfo);
        Mockito.when(esnUtil.getEsnContent(esnCheckInfo)).thenReturn(expectedEsnResponse);
        Mockito.when(esnUtil.getESNStatus(esnCheckInfo)).thenReturn(EsnStatus.ESN_BAD);
        Mockito.when(esnUtil.getESNFieldColor(esnCheckInfo)).thenReturn(EsnFieldColor.RED);

        final AsyncEsnRequestEvent event = new AsyncEsnRequestEvent(listener, device,
                EsnCheckType.ESN_CHECK_THROUGH_CUSTOMIZATION);
        listener.onEvent(event);

        Mockito.verify(esnCheckService).getEsnInfo(esnRequest, false);
        Mockito.verify(esnUtil).getEsnContent(esnCheckInfo);

        // Make sure the esn check response MQTT message was published
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "esn", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final EsnResponseMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        EsnResponseMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(EsnStatus.ESN_BAD, message.getEsnStatus());
                assertEquals(EsnFieldColor.RED, message.getEsnFieldColor());
                assertEquals(expectedEsnResponse, message.getEsnUIResponse());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
    }

    @Test
    public void testOnEsnCheckWithCustomization2() throws MqttException {
        device.setCarrier("T-Mobile");

        Mockito.when(inMemoryStore.getUserName()).thenReturn("userId");
        Mockito.when(inMemoryStore.getApiKey()).thenReturn("api");
        Mockito.when(inMemoryStore.getTesterId()).thenReturn("tester");
        Mockito.when(inMemoryStore.getWarehouseId()).thenReturn("warehouse");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        Mockito.when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(
                CloudCustomizationResponse.builder()
                        .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder()
                                .configuration(CloudCustomizationResponse.ImeiCheckConfiguration.builder()
                                        .tmobileAction("CHECK_GLOBAL_BLACKLIST_ONLY")
                                        .build())
                                .build())
                        .build()
        );

        Mockito.when(esnUtil.getESNServiceId(eq("T-Mobile"),
                eq(EsnCheckType.ESN_CHECK_BLACKLIST))).thenReturn("-1");

        EsnLicenseCheckRequest esnRequest = EsnLicenseCheckRequest.builder().
                imei(device.getImei()).
                serviceId("-1").
                userId(inMemoryStore.getUserName()).
                testerId(inMemoryStore.getTesterId()).
                carrier(device.getCarrier()).
                apiKey(inMemoryStore.getApiKey()).
                meid(device.getMeid()).
                isMeid(StringUtils.isBlank(device.getMeid())).
                warehouseId(inMemoryStore.getWarehouseId()).
                deviceType(device.getDeviceType().toString()).
                checkAll(0).build();

        EsnResponse.EsnApiResponse response1 = EsnResponse.EsnApiResponse.builder().
                imei(device.getImei()).
                carrier("Verizon").
                api("G001").
                rawResponse("whatever").
                remarks("Bad").
                chargeStatus("no").
                fallbackCall("ss").
                fieldColor("Red").build();

        EsnCheckInfo esnCheckInfo = EsnCheckInfo
                .builder()
                .esnApiResults(List.of(response1))
                .build();

        String expectedEsnResponse
                = "API: G001<br>" +
                "Remarks: Bad<br>" +
                "Field Color: Red<br>" +
                "IMEI: 359408085993451<br>" +
                "Carrier: not available<br>" +
                "FallBackCall: No<br>Charge Status: No<br>" +
                "Ref Code: 21072023070419<br>" +
                "Device Type: Smartphone<br>" +
                "Marketing Name: Apple iPhone X (A1901)<br>" +
                "Brand Name: Apple<br>" +
                "Manufacturer: Apple Inc<br>" +
                "Response Status: success<br>" +
                "Black List Status: Yes<br>" +
                "Grey List Status: No<br>" +
                "Operating System: iOS<br>" +
                "Model Name: iPhone X (A1901)<br>" +
                "<br>------------------------------------------------<br><br>";

        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        Mockito.when(esnCheckService.getEsnInfo(esnRequest, false)).thenReturn(esnCheckInfo);
        Mockito.when(esnUtil.getEsnContent(esnCheckInfo)).thenReturn(expectedEsnResponse);
        Mockito.when(esnUtil.getESNStatus(esnCheckInfo)).thenReturn(EsnStatus.ESN_BAD);
        Mockito.when(esnUtil.getESNFieldColor(esnCheckInfo)).thenReturn(EsnFieldColor.RED);

        final AsyncEsnRequestEvent event =
                new AsyncEsnRequestEvent(listener, device, EsnCheckType.ESN_CHECK_THROUGH_CUSTOMIZATION);
        listener.onEvent(event);

        Mockito.verify(esnCheckService).getEsnInfo(esnRequest, false);

        // Make sure the esn check blacklist response MQTT message was published
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "esn", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final EsnResponseMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        EsnResponseMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(EsnStatus.ESN_BAD, message.getEsnStatus());
                assertEquals(EsnFieldColor.RED, message.getEsnFieldColor());
                assertEquals(expectedEsnResponse, message.getEsnUIResponse());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
    }

    @Test
    public void testOnEsnCheckWithCustomization3() throws MqttException {
        device.setCarrier("AT&T");
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        Mockito.when(inMemoryStore.getUserName()).thenReturn("userId");
        Mockito.when(inMemoryStore.getApiKey()).thenReturn("api");
        Mockito.when(inMemoryStore.getTesterId()).thenReturn("tester");
        Mockito.when(inMemoryStore.getWarehouseId()).thenReturn("warehouse");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        Mockito.when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(
                CloudCustomizationResponse.builder()
                        .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder()
                                .configuration(CloudCustomizationResponse.ImeiCheckConfiguration.builder()
                                        .attAction("CHECK_ALL_POSIBLE_DATABASES")
                                        .build())
                                .build())
                        .build()
        );

        Mockito.when(esnUtil.getESNServiceId(eq("AT&T"), any())).thenReturn("-1");

        EsnLicenseCheckRequest esnRequest = EsnLicenseCheckRequest.builder().
                imei(device.getImei()).
                serviceId("-1").
                userId(inMemoryStore.getUserName()).
                testerId(inMemoryStore.getTesterId()).
                carrier(device.getCarrier()).
                apiKey(inMemoryStore.getApiKey()).
                meid(device.getMeid()).
                isMeid(StringUtils.isBlank(device.getMeid())).
                warehouseId(inMemoryStore.getWarehouseId()).
                deviceType(device.getDeviceType().toString()).
                checkAll(1).build();

        EsnResponse.EsnApiResponse response1 = EsnResponse.EsnApiResponse.builder().
                imei(device.getImei()).
                carrier("Verizon").
                api("G001").
                rawResponse("whatever").
                remarks("Good").
                chargeStatus("no").
                fallbackCall("ss").
                fieldColor("green").build();

        EsnResponse.EsnApiResponse response2 = EsnResponse.EsnApiResponse.builder().
                imei(device.getImei()).
                api("S001").
                rawResponse("whatever").
                remarks("Good").
                chargeStatus("No").
                fallbackCall("No").
                fieldColor("green").build();

        EsnCheckInfo esnCheckInfo = EsnCheckInfo
                .builder()
                .esnApiResults(List.of(response1, response2))
                .build();

        String expectedEsnResponse = """
                API: G001
                date : 2020-11-23 10:36:27
                by : Orange PCS Ltd
                action : Black Insert
                Country : United Kingdom
                operatingsys : iOS
                greyliststatus : No
                modelname : iPhone 12 Pro (A2407)
                responsestatus : success
                refcode : 11052023061349
                marketingname : Apple iPhone 12 Pro (A2407)
                brandname : Apple
                blackliststatus : Yes
                devicetype : Smartphone
                manufacturer : Apple Inc""";

        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        Mockito.when(esnCheckService.getEsnInfo(esnRequest, false)).thenReturn(esnCheckInfo);
        Mockito.when(esnUtil.getEsnContent(esnCheckInfo)).thenReturn(expectedEsnResponse);
        Mockito.when(esnUtil.getESNStatus(esnCheckInfo)).thenReturn(EsnStatus.ESN_GOOD);
        Mockito.when(esnUtil.getESNFieldColor(esnCheckInfo)).thenReturn(EsnFieldColor.GREEN);

        final AsyncEsnRequestEvent event = new AsyncEsnRequestEvent(listener, device,
                EsnCheckType.ESN_CHECK_THROUGH_CUSTOMIZATION);
        listener.onEvent(event);

        Mockito.verify(esnCheckService).getEsnInfo(esnRequest, false);
        Mockito.verify(esnUtil).getEsnContent(esnCheckInfo);

        // Make sure the esn check all response MQTT message was published
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "esn", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final EsnResponseMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        EsnResponseMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(EsnStatus.ESN_GOOD, message.getEsnStatus());
                assertEquals(EsnFieldColor.GREEN, message.getEsnFieldColor());
                assertEquals(expectedEsnResponse, message.getEsnUIResponse());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
    }

    @Test
    public void testEsnNotPerformedForSameImeiAndSerialNumbers() {
        device.setSerial("1123");
        device.setCarrier("WIFI");

        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        EsnLicenseCheckRequest esnRequest = EsnLicenseCheckRequest.builder().
                imei(device.getImei()).
                serviceId("-1").
                userId(inMemoryStore.getUserName()).
                testerId(inMemoryStore.getTesterId()).
                carrier(device.getCarrier()).
                apiKey(inMemoryStore.getApiKey()).
                meid(device.getMeid()).
                isMeid(StringUtils.isBlank(device.getMeid())).
                warehouseId(inMemoryStore.getWarehouseId()).
                deviceType(device.getDeviceType().toString()).
                checkAll(1).build();

        final AsyncEsnRequestEvent event =
                new AsyncEsnRequestEvent(listener, device, EsnCheckType.ESN_CHECK);
        listener.onEvent(event);
        Mockito.verify(esnUtil, never()).getESNServiceId(device.getCarrier(), event.getEsnCheckType());
        Mockito.verify(esnCheckService, never()).getEsnInfo(esnRequest, false);
    }


    @Test
    public void testOnUsInsuranceBlacklist() throws MqttException {
        device.setCarrier("Verizon");

        EsnResponse.UsInsuranceBlackListInfo.BlackListInfo usInsuranceInfo =
                new EsnResponse.UsInsuranceBlackListInfo.BlackListInfo();
        usInsuranceInfo.setIMEI("123456789012345");
        usInsuranceInfo.setRequiresSim(true);
        usInsuranceInfo.setReuseSim(false);
        usInsuranceInfo.setModel("Galaxy S24 Ultra");
        usInsuranceInfo.setManufacturer("Samsung");
        usInsuranceInfo.setMarketingName("Samsung Galaxy S24 Ultra");
        usInsuranceInfo.setGSM(true);
        usInsuranceInfo.setESIMCompatible(true);
        usInsuranceInfo.setSimSlots(true);
        usInsuranceInfo.setBands("GSM 900/1800/1900");
        usInsuranceInfo.setVoLTECompatible(true);
        usInsuranceInfo.setCompatibility("Compatible");
        usInsuranceInfo.setDeviceType("Smartphone");
        usInsuranceInfo.setBlacklistStatus("Good");

        EsnResponse.UsInsuranceBlackListInfo response1 = EsnResponse.UsInsuranceBlackListInfo.builder()
                .data(usInsuranceInfo)
                .build();

        EsnCheckInfo esnCheckInfo = EsnCheckInfo
                .builder()
                .usInsuranceBlackListInfo(response1)
                .build();
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(inMemoryStore.getUserToken()).thenReturn("aasdsderfsdfdsfgdfgdfg");


        String expectedEsnResponse
                = "API: not available<br>" +
                "Remarks: not available<br>" +
                "Field Color: not available<br>" +
                "IMEI: 359408085993451<br>" +
                "Carrier: not available<br>" +
                "FallBackCall: not available<br>Charge Status: not available<br>" +
                "<br>------------------------------------------------<br><br>";
        when(mqttClient.isConnected()).thenReturn(true);
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(esnCheckService.getEsnBlacklistInfo(device.getImei(), "aasdsderfsdfdsfgdfgdfg")).thenReturn(esnCheckInfo);
        when(esnUtil.getEsnContent(esnCheckInfo)).thenReturn(expectedEsnResponse);
        when(esnUtil.getESNStatus(esnCheckInfo)).thenReturn(EsnStatus.ESN_GOOD);
        when(esnUtil.getESNFieldColor(esnCheckInfo)).thenReturn(EsnFieldColor.GREEN);

        final AsyncEsnRequestEvent event =
                new AsyncEsnRequestEvent(listener, device, EsnCheckType.ESN_CHECK_US_INSURANCE_BLACKLIST);
        listener.onEvent(event);

        verify(esnCheckService).getEsnBlacklistInfo(device.getImei(), inMemoryStore.getUserToken());

        // Make sure the esn check blacklist response MQTT message was published
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "esn", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final EsnResponseMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        EsnResponseMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(EsnStatus.ESN_GOOD, message.getEsnStatus());
                assertEquals(EsnFieldColor.GREEN, message.getEsnFieldColor());
                assertEquals(expectedEsnResponse, message.getEsnUIResponse());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
    }

    @Test
    public void testOnUsInsuranceBlacklistCustomizationEnable() throws MqttException {
        device.setCarrier("Verizon");

        EsnResponse.UsInsuranceBlackListInfo.BlackListInfo usInsuranceInfo =
                new EsnResponse.UsInsuranceBlackListInfo.BlackListInfo();
        usInsuranceInfo.setIMEI("123456789012345");
        usInsuranceInfo.setRequiresSim(true);
        usInsuranceInfo.setReuseSim(false);
        usInsuranceInfo.setModel("iPhone 12 Pro (A2407)");
        usInsuranceInfo.setManufacturer("Apple");
        usInsuranceInfo.setMarketingName("Apple iPhone 12 Pro (A2407)");
        usInsuranceInfo.setGSM(true);
        usInsuranceInfo.setESIMCompatible(true);
        usInsuranceInfo.setSimSlots(true);
        usInsuranceInfo.setBands("GSM 900/1800/1900");
        usInsuranceInfo.setVoLTECompatible(true);
        usInsuranceInfo.setCompatibility("Compatible");
        usInsuranceInfo.setDeviceType("Smartphone");
        usInsuranceInfo.setBlacklistStatus("Lost or Stolen");

        EsnResponse.UsInsuranceBlackListInfo response1 = EsnResponse.UsInsuranceBlackListInfo.builder()
                .data(usInsuranceInfo)
                .build();

        EsnCheckInfo esnCheckInfo1 = EsnCheckInfo
                .builder()
                .usInsuranceBlackListInfo(response1)
                .build();
        CloudCustomizationResponse mockedResponse = CloudCustomizationResponse.builder()
                .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder()
                        .usFinancialCarrierSetting(true)
                        .configuration(CloudCustomizationResponse.ImeiCheckConfiguration.builder()
                                .verizonAction("CHECK_ALL_POSIBLE_DATABASES")
                                .build())
                        .build())
                .build();
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(inMemoryStore.getUserToken()).thenReturn("aasdsderfsdfdsfgdfgdfg");
        Mockito.when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(mockedResponse);

        Mockito.when(inMemoryStore.getUserName()).thenReturn("userId");
        Mockito.when(inMemoryStore.getApiKey()).thenReturn("api");
        Mockito.when(inMemoryStore.getTesterId()).thenReturn("tester");
        Mockito.when(inMemoryStore.getWarehouseId()).thenReturn("warehouse");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        Mockito.when(esnUtil.getESNServiceId("Verizon", EsnCheckType.ESN_CHECK_ALL)).thenReturn("9");
        when(mqttClient.isConnected()).thenReturn(true);

        EsnLicenseCheckRequest esnRequest = EsnLicenseCheckRequest.builder().
                imei(device.getImei()).
                serviceId("9").
                userId(inMemoryStore.getUserName()).
                testerId(inMemoryStore.getTesterId()).
                carrier(device.getCarrier()).
                apiKey(inMemoryStore.getApiKey()).
                meid(device.getMeid()).
                isMeid(StringUtils.isBlank(device.getMeid())).
                warehouseId(inMemoryStore.getWarehouseId()).
                deviceType(device.getDeviceType().toString()).
                checkAll(1).build();

        EsnResponse.EsnApiResponse response2 = EsnResponse.EsnApiResponse.builder().
                imei(device.getImei()).
                carrier("Verizon").
                api("S001").
                rawResponse("Model: Apple iPhone X 64GB in Space GrayIMEI: 1123 " +
                        "ESN Status: BlacklistLost or Stolen.Status Code: LOST STOLEN" +
                        "Part Number: IPHONEX-NVZW-CDMALDevice SKU: SKU2720145 Carrier: Verizon").
                remarks("Bad").
                chargeStatus("No").
                fallbackCall("No").
                fieldColor("Red").build();

        EsnCheckInfo esnCheckInfo2 = EsnCheckInfo
                .builder()
                .esnApiResults(List.of(response2))
                .usInsuranceBlackListInfo(response1)
                .build();

        String expectedEsnResponse = """
                API: S001
                date : 2020-11-23 10:36:27
                by : Orange PCS Ltd
                action : Black Insert
                Country : United Kingdom
                operatingsys : iOS
                greyliststatus : No
                modelname : iPhone 12 Pro (A2407)
                responsestatus : success
                refcode : 11052023061349
                marketingname : Apple iPhone 12 Pro (A2407)
                brandname : Apple
                blackliststatus : Yes
                devicetype : Smartphone
                manufacturer : Apple Inc""";


        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(esnCheckService.getEsnInfo(esnRequest, false)).thenReturn(esnCheckInfo2);
        when(esnCheckService.getEsnBlacklistInfo(device.getImei(), "aasdsderfsdfdsfgdfgdfg")).thenReturn(esnCheckInfo1);
        Mockito.when(esnUtil.getEsnContent(esnCheckInfo2)).thenReturn(expectedEsnResponse);
        Mockito.when(esnUtil.getESNStatus(esnCheckInfo2)).thenReturn(EsnStatus.ESN_BAD);
        Mockito.when(esnUtil.getESNFieldColor(esnCheckInfo2)).thenReturn(EsnFieldColor.RED);

        final AsyncEsnRequestEvent event =
                new AsyncEsnRequestEvent(listener, device, EsnCheckType.ESN_CHECK_THROUGH_CUSTOMIZATION);
        listener.onEvent(event);

        verify(esnCheckService).getEsnBlacklistInfo(device.getImei(), inMemoryStore.getUserToken());
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "esn", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final EsnResponseMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        EsnResponseMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(EsnStatus.ESN_BAD, message.getEsnStatus());
                assertEquals(EsnFieldColor.RED, message.getEsnFieldColor());
                assertEquals(expectedEsnResponse, message.getEsnUIResponse());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
    }

    @Test
    public void testOnEsnCheckWhenCheckmendEnabled() throws MqttException, JsonProcessingException {
        device.setCarrier("AT&T");
        device.setSerial("4321");

        Mockito.when(inMemoryStore.isPersonalCheckMend()).thenReturn(true);
        Mockito.when(inMemoryStore.getUserName()).thenReturn("userId");
        Mockito.when(inMemoryStore.getApiKey()).thenReturn("api");
        Mockito.when(inMemoryStore.getTesterId()).thenReturn("tester");
        Mockito.when(inMemoryStore.getWarehouseId()).thenReturn("warehouse");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(mqttClient.isConnected()).thenReturn(true);
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        Mockito.when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(
                CloudCustomizationResponse.builder()
                        .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder()
                                .configuration(CloudCustomizationResponse.ImeiCheckConfiguration.builder()
                                        .attAction("CHECK_ATT")
                                        .build())
                                .build())
                        .build()
        );

        EsnLicenseCheckRequest esnRequest = EsnLicenseCheckRequest.builder().
                imei(device.getImei()).
                imei2(device.getImei2()).
                serviceId("12").
                userId(inMemoryStore.getUserName()).
                testerId(inMemoryStore.getTesterId()).
                carrier(device.getCarrier()).
                serial(device.getSerial()).
                apiKey(inMemoryStore.getApiKey()).
                meid(device.getMeid()).
                isMeid(StringUtils.isBlank(device.getMeid())).
                warehouseId(inMemoryStore.getWarehouseId()).
                deviceType(device.getDeviceType().toString()).
                checkAll(1).build();


        EsnResponse.EsnApiResponse response = EsnResponse.EsnApiResponse.builder().
                imei(device.getImei()).
                carrier("Verizon").
                api("C001").
                rawResponse("Model: Apple iPhone X 64GB in Space GrayIMEI: 1123 " +
                        "ESN Status: BlacklistLost or Stolen.Status Code: LOST STOLEN" +
                        "Part Number: IPHONEX-NVZW-CDMALDevice SKU: SKU2720145 Carrier: Verizon" +
                        "Checkmend ID: 8-FB72A2BB71B-547:E55A56FA").
                remarks("Bad").
                chargeStatus("No").
                fallbackCall("No").
                fieldColor("Red").build();

        EsnCheckInfo esnCheckInfo = EsnCheckInfo
                .builder()
                .esnApiResults(List.of(response))
                .build();

        String expectedEsnResponse = """
                API: C001
                date : 2020-11-23 10:36:27
                by : Orange PCS Ltd
                action : Black Insert
                Country : United Kingdom
                operatingsys : iOS
                greyliststatus : No
                modelname : iPhone 12 Pro (A2407)
                responsestatus : success
                refcode : 11052023061349
                marketingname : Apple iPhone 12 Pro (A2407)
                brandname : Apple
                blackliststatus : Yes
                devicetype : Smartphone
                manufacturer : Apple Inc
                checkmendId : 8-FB72A2BB71B-547:E55A56FA""";

        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        Mockito.when(esnUtil.getESNServiceId(device.getCarrier(), EsnCheckType.ESN_CHECK_ALL))
                .thenReturn("12");
        Mockito.when(esnCheckService.getEsnInfo(esnRequest, true)).thenReturn(esnCheckInfo);

        Mockito.when(esnUtil.getEsnContent(esnCheckInfo)).thenReturn(expectedEsnResponse);
        Mockito.when(esnUtil.getESNStatus(esnCheckInfo)).thenReturn(EsnStatus.ESN_BAD);
        Mockito.when(esnUtil.getESNFieldColor(esnCheckInfo)).thenReturn(EsnFieldColor.RED);


        final AsyncEsnRequestEvent event = new AsyncEsnRequestEvent(listener, device,
                EsnCheckType.ESN_CHECK_ALL);
        listener.onEvent(event);

        Mockito.verify(esnCheckService).getEsnInfo(esnRequest, true);
        Mockito.verify(esnUtil).getEsnContent(esnCheckInfo);

        // Make sure the esn check response MQTT message was published
        verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "esn", "response"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final EsnResponseMessage message = objectMapper.readValue(mqttMessage.getPayload(),
                        EsnResponseMessage.class);
                assertEquals(device.getId(), message.getId());
                assertEquals(EsnStatus.ESN_BAD, message.getEsnStatus());
                assertEquals(EsnFieldColor.RED, message.getEsnFieldColor());
                assertEquals(expectedEsnResponse, message.getEsnUIResponse());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));
    }
    @Test
    public void testEsnCheckNotPerformedForSameImeiAndSerialNumbers() {

        Device device1 = Mockito.mock(Device.class);
        device1.setImei("1123");
        device1.setSerial("1123");
        device1.setCarrier("WIFI");
        device1.setId("deviceid");

        when(deviceConnectionTracker.getDevice(device1.getId())).thenReturn(device1);

        final AsyncEsnRequestEvent event = new AsyncEsnRequestEvent(listener, device1,
                EsnCheckType.ESN_CHECK_ALL);
        listener.onEvent(event);

        Mockito.verify(stageUpdater, never())
                .updateStage(any());

    }
}
