package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudCarrierAndSimLockService;
import com.phonecheck.api.cloud.SamsungImeiService;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.dao.service.lookup.DeviceCarrierDBLookupService;
import com.phonecheck.model.cloudapi.CarrierSimLockStatusRequest;
import com.phonecheck.model.cloudapi.CarrierSimLockStatusResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.CarrierLockInfoSuccessStage;
import com.phonecheck.model.device.stage.IStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceCarrierCheckEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.DeviceCarrierCheckResponseMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.LinkedHashMap;
import java.util.List;

import static com.phonecheck.model.device.DeviceType.ANDROID;
import static com.phonecheck.model.device.DeviceType.IPHONE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DeviceCarrierCheckListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private CloudCarrierAndSimLockService cloudCarrierAndSimlockService;
    @Mock
    private SamsungImeiService samsungImeiService;
    @Mock
    private DeviceCarrierDBLookupService carrierDBLookupService;
    @Mock
    private DeviceStageUpdater stageUpdater;
    private final ObjectMapper objectMapper = new ObjectMapper();

    private IosDevice device;
    private DeviceCarrierCheckListener listener;

    @BeforeEach
    void setup() {
        device = new IosDevice();
        device.setId("123456");
        device.setImei("imei");
        device.setSerial("serial");

        listener = new DeviceCarrierCheckListener(mqttClient, deviceConnectionTracker, inMemoryStore, objectMapper,
                stageUpdater, cloudCarrierAndSimlockService, carrierDBLookupService, samsungImeiService);

    }

    @Test
    void testOnEvent() throws MqttException {
        CarrierSimLockStatusResponse response = new CarrierSimLockStatusResponse();

        LinkedHashMap<String, Object> carrierSimLockData = new LinkedHashMap<>();
        LinkedHashMap<String, String> carrierSimLockValues = new LinkedHashMap<>();
        carrierSimLockValues.put("carrier", "2174 - US Sprint/MVNO Locked Policy");
        carrierSimLockValues.put("simlock", "locked");
        carrierSimLockData.put("data", carrierSimLockValues);

        response.setRawResponse(carrierSimLockData);

        final DeviceCarrierCheckEvent deviceCarrierCheckEvent = new DeviceCarrierCheckEvent(this, device);
        when(cloudCarrierAndSimlockService.getCarrierAndSimLockStatus(any(CarrierSimLockStatusRequest.class)))
                .thenReturn(response);
        when(carrierDBLookupService.getFilteredCarrier(anyString(), anyBoolean())).thenReturn("Sprint");
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(inMemoryStore.getApiKey()).thenReturn("testkey");
        when(inMemoryStore.getUserName()).thenReturn("testuser");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(mqttClient.isConnected()).thenReturn(true);

        listener.onEvent(deviceCarrierCheckEvent);

        // verify stage was updated
        ArgumentCaptor<IStage> iStageCaptor = ArgumentCaptor.forClass(IStage.class);
        verify(stageUpdater).updateStage(iStageCaptor.capture());

        List<IStage> stageUpdaters = iStageCaptor.getAllValues();
        CarrierLockInfoSuccessStage carrierLockInfoSuccessStage = (CarrierLockInfoSuccessStage) stageUpdaters.get(0);
        assertEquals(device.getId(), carrierLockInfoSuccessStage.getId());
        assertEquals("locked", carrierLockInfoSuccessStage.getSimLockStatus());
        assertEquals("Sprint", carrierLockInfoSuccessStage.getCarrier());

        Mockito.verify(mqttClient).publish(anyString(), argThat(mqttMessage -> {
            try {
                DeviceCarrierCheckResponseMessage message =
                        objectMapper.readValue(new String(mqttMessage.getPayload()),
                                DeviceCarrierCheckResponseMessage.class);
                Assertions.assertEquals(device.getId(), message.getId());
            } catch (JsonProcessingException e) {
                return false;
            }
            return true;
        }));
    }

    @Test
    void getAndroidCarrierResponseTest() throws MqttException {
        AndroidDevice androidDevice = new AndroidDevice();
        androidDevice.setImei("126825622");
        androidDevice.setSerial("345y67u8ijdn");
        androidDevice.setCarrier("T-Mobile");
        androidDevice.setMake("Samsung");

        final DeviceCarrierCheckEvent event = new DeviceCarrierCheckEvent(this, androidDevice);
        when(deviceConnectionTracker.getDevice(androidDevice.getId())).thenReturn(androidDevice);
        when(samsungImeiService.getCarrierFromSamsungImeiCheck(any(), any()))
                .thenReturn("{\"data\":{\"carrier\":\"TestCarrier\"}}");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(mqttClient.isConnected()).thenReturn(true);

        listener.onEvent(event);

        verify(deviceConnectionTracker, times(1)).getDevice(any());
        verify(samsungImeiService, times(1)).getCarrierFromSamsungImeiCheck(any(), any());

        verify(mqttClient).publish(matches(TopicBuilder.buildGenericTopic("carrier-check", "response")),
                any(MqttMessage.class));
    }

    @Test
    void testCarrierAndSimLockCheckEventWithServerError() throws MqttException {
        CarrierSimLockStatusResponse response = new CarrierSimLockStatusResponse();

        LinkedHashMap<String, Object> carrierSimLockData = new LinkedHashMap<>();
        LinkedHashMap<String, String> carrierSimLockValues = new LinkedHashMap<>();
        carrierSimLockValues.put("carrier", "null");
        carrierSimLockData.put("data", carrierSimLockValues);
        carrierSimLockData.put("result", "Server temporarily not available, please try again");

        response.setRawResponse(carrierSimLockData);

        final DeviceCarrierCheckEvent deviceCarrierCheckEvent = new DeviceCarrierCheckEvent(this, device);
        when(cloudCarrierAndSimlockService.getCarrierAndSimLockStatus(any(CarrierSimLockStatusRequest.class)))
                .thenReturn(response);
        when(carrierDBLookupService.getFilteredCarrier(anyString(), anyBoolean())).thenReturn("Sprint");
        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(inMemoryStore.getApiKey()).thenReturn("testkey");
        when(inMemoryStore.getUserName()).thenReturn("testuser");
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(mqttClient.isConnected()).thenReturn(true);

        listener.onEvent(deviceCarrierCheckEvent);

        // verify stage was updated
        ArgumentCaptor<IStage> iStageCaptor = ArgumentCaptor.forClass(IStage.class);
        verify(stageUpdater).updateStage(iStageCaptor.capture());

        List<IStage> stageUpdaters = iStageCaptor.getAllValues();
        CarrierLockInfoSuccessStage carrierLockInfoSuccessStage = (CarrierLockInfoSuccessStage) stageUpdaters.get(0);
        assertEquals(device.getId(), carrierLockInfoSuccessStage.getId());
        assertEquals("Sprint", carrierLockInfoSuccessStage.getCarrier());
        assertNull(carrierLockInfoSuccessStage.getSimLockStatus());


        Mockito.verify(mqttClient).publish(anyString(), argThat(mqttMessage -> {
            try {
                DeviceCarrierCheckResponseMessage message =
                        objectMapper.readValue(new String(mqttMessage.getPayload()),
                                DeviceCarrierCheckResponseMessage.class);
                Assertions.assertEquals(device.getId(), message.getId());
            } catch (JsonProcessingException e) {
                return false;
            }
            return true;
        }));
    }

    @Test
    public void testCarrierCheckNotPerformedForSameImeiAndSerialNumbersForAndroid() {

        Device device1 = Mockito.mock(Device.class);
        device1.setImei("1123");
        device1.setSerial("1123");
        device1.setCarrier("WIFI");

        when(deviceConnectionTracker.getDevice(device1.getId())).thenReturn(device1);
        when(device1.getDeviceType()).thenReturn(ANDROID);

        final DeviceCarrierCheckEvent event =
                new DeviceCarrierCheckEvent(listener, device1);
        listener.onEvent(event);
        Mockito.verify(samsungImeiService, never())
                .getCarrierFromSamsungImeiCheck(any(), any());
    }

    @Test
    public void testCarrierCheckNotPerformedForSameImeiAndSerialNumbersForIos() {

        Device device1 = Mockito.mock(Device.class);
        device1.setImei("1123");
        device1.setSerial("1123");
        device1.setCarrier("WIFI");

        when(deviceConnectionTracker.getDevice(device1.getId())).thenReturn(device1);
        when(device1.getDeviceType()).thenReturn(IPHONE);

        final DeviceCarrierCheckEvent event =
                new DeviceCarrierCheckEvent(listener, device1);
        listener.onEvent(event);
        Mockito.verify(cloudCarrierAndSimlockService, never())
                .getCarrierAndSimLockStatus(any());

    }
}
