package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.Cloud3DeviceDataSyncService;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.dao.service.UphInfoDbService;
import com.phonecheck.device.connection.ios.IosDeviceRestoreService;
import com.phonecheck.info.ios.PreCheckStreamService;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosDisconnectedEvent;
import com.phonecheck.model.event.syslog.StopDeviceSysLogEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.status.WorkingStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IosDeviceDisconnectedListenerTest {
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private CloudDeviceDataSyncService cloudDeviceDataSyncService;
    @Mock
    private Cloud3DeviceDataSyncService cloud3DeviceDataSyncService;
    @Mock
    private UphInfoDbService uphInfoDbService;
    @Mock
    private DeviceAutomationQueueService automationQueueService;
    @Mock
    private PreCheckStreamService preCheckStreamService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private IosDevice device;
    private IosDeviceDisconnectedListener listener;

    @Mock
    private IosDeviceRestoreService iosDeviceRestoreService;

    @BeforeEach
    void setup() {
        device = new IosDevice();
        device.setId("123456");
        device.setEcid("9802390239023");
        device.setProductType("iPhone5,3");
        device.setProductVersion("10.0.0");
        device.setSerial("serial");
        when(deviceConnectionTracker.getDevice(eq(device.getId()))).thenReturn(device);

        listener = new IosDeviceDisconnectedListener(mqttClient, objectMapper, eventPublisher, inMemoryStore,
                deviceConnectionTracker, cloudDeviceDataSyncService, cloud3DeviceDataSyncService,
                uphInfoDbService, automationQueueService, iosDeviceRestoreService, preCheckStreamService);
    }

    @Test
    void testDeviceDisconnected() throws MqttException {
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getDeviceConnectionMode()).thenReturn(DeviceConnectionMode.PROCESS);
        when(mqttClient.isConnected()).thenReturn(true);
        final IosDisconnectedEvent event = new IosDisconnectedEvent(listener, device);
        device.setStage(DeviceStage.INFO_COLLECTION_SUCCEEDED);
        listener.onEvent(event);

        verify(eventPublisher).publishEvent(Mockito.any(StopDeviceSysLogEvent.class));
        verify(mqttClient).publish(Mockito.matches(TopicBuilder.build(device, "disconnected")),
                any(MqttMessage.class));

        verify(uphInfoDbService).updateUphOnDisconnect("serial", WorkingStatus.PENDING, null);
        verify(mqttClient).publish(Mockito.matches(TopicBuilder.buildGenericTopic("uph", "info")),
                any(MqttMessage.class));
        verify(automationQueueService).removeDeviceAutomationQueue(eq("123456"));
        verify(preCheckStreamService, never()).stopPreCheckStreamRoutine(device);
    }

    @Test
    void testEraseInProgress() throws MqttException {
        final IosDisconnectedEvent event = new IosDisconnectedEvent(listener, device);
        device.setStage(DeviceStage.ERASE_IN_PROGRESS);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        listener.onEvent(event);

        verify(mqttClient, never()).publish(Mockito.matches(TopicBuilder.build(device, "disconnected")),
                any(MqttMessage.class));
        verify(automationQueueService, never()).removeDeviceAutomationQueue(any());
    }

    @Test
    void testEraseInProgressButManuallyReleased() throws MqttException {
        when(mqttClient.isConnected()).thenReturn(true);
        final IosDisconnectedEvent event = new IosDisconnectedEvent(listener, device);
        event.setManualRelease(true);
        device.setStage(DeviceStage.ERASE_IN_PROGRESS);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        listener.onEvent(event);

        verify(eventPublisher).publishEvent(Mockito.any(StopDeviceSysLogEvent.class));
        verify(mqttClient).publish(Mockito.matches(TopicBuilder.build(device, "disconnected")),
                any(MqttMessage.class));
        verify(automationQueueService).removeDeviceAutomationQueue(eq("123456"));
    }

    @Test
    void testDeviceNotSyncedOnPairFail() throws MqttException {
        when(mqttClient.isConnected()).thenReturn(true);
        final IosDisconnectedEvent event = new IosDisconnectedEvent(listener, device);
        device.setStage(DeviceStage.PAIRING_FAILED);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        listener.onEvent(event);

        verify(cloudDeviceDataSyncService, times(0)).
                syncDeviceDisconnectOnCloud(any(), any());
        verify(cloudDeviceDataSyncService, times(0)).
                syncDeviceRecordOnCloud(any(), any(), any());

        verify(eventPublisher).publishEvent(Mockito.any(StopDeviceSysLogEvent.class));

    }

    @Test
    void testDeviceNotSyncedOnInitialConnection() throws MqttException {
        when(mqttClient.isConnected()).thenReturn(true);
        final IosDisconnectedEvent event = new IosDisconnectedEvent(listener, device);
        device.setStage(DeviceStage.INITIAL_CONNECTION);

        when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        listener.onEvent(event);

        verify(cloudDeviceDataSyncService, times(0)).
                syncDeviceDisconnectOnCloud(any(), any());
        verify(cloudDeviceDataSyncService, times(0)).
                syncDeviceRecordOnCloud(any(), any(), any());

        verify(eventPublisher).publishEvent(Mockito.any(StopDeviceSysLogEvent.class));

    }

    @Test
    void testPreCheckStreamShouldClose() throws MqttException {
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());
        when(inMemoryStore.getDeviceConnectionMode()).thenReturn(DeviceConnectionMode.PRE_CHECK);
        when(mqttClient.isConnected()).thenReturn(true);
        final IosDisconnectedEvent event = new IosDisconnectedEvent(listener, device);
        device.setStage(DeviceStage.INFO_COLLECTION_SUCCEEDED);
        listener.onEvent(event);

        verify(eventPublisher).publishEvent(Mockito.any(StopDeviceSysLogEvent.class));
        verify(mqttClient).publish(Mockito.matches(TopicBuilder.build(device, "disconnected")),
                any(MqttMessage.class));

        verify(uphInfoDbService).updateUphOnDisconnect("serial", WorkingStatus.PENDING, null);
        verify(mqttClient).publish(Mockito.matches(TopicBuilder.buildGenericTopic("uph", "info")),
                any(MqttMessage.class));
        verify(automationQueueService).removeDeviceAutomationQueue(eq("123456"));
        verify(preCheckStreamService).stopPreCheckStreamRoutine(device);
    }

}
