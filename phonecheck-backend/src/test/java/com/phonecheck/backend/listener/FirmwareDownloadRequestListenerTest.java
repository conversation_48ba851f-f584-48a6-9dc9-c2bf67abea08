package com.phonecheck.backend.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.service.FirmwareDownloadService;
import com.phonecheck.model.event.FirmwareDownloadRequestEvent;
import com.phonecheck.model.firmware.FirmwareDownloadStatus;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.FirmwareDownloadResponseMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class FirmwareDownloadRequestListenerTest {
    @InjectMocks
    private FirmwareDownloadRequestListener listener;

    private final ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient mqttClient;

    @Mock
    private InMemoryStore inMemoryStore;

    @Mock
    private FirmwareDownloadService firmwareDownloadService;

    private FirmwareModel.FirmwareResponse res = null;

    @BeforeEach
    void setup() {
        listener = new FirmwareDownloadRequestListener(mqttClient, objectMapper,
                inMemoryStore, firmwareDownloadService);
        when(mqttClient.isConnected()).thenReturn(true);
        res = new FirmwareModel.FirmwareResponse();
        res.setFileName("testfile");
        res.setDownloadStatus(FirmwareDownloadStatus.SUCCESS);
        res.setId("testFirmware");
        Map<String, FirmwareModel.FirmwareResponse> map = new HashMap<>();
        map.put("testFirmware", res);
        when(inMemoryStore.getFirmwareModels()).thenReturn(map);
    }

    @Test
    public void testWhenOnlyExtraction() throws Exception {
        res.setDownloadStatus(FirmwareDownloadStatus.DOWNLOADED_BUT_NOT_VERIFIED);

        listener.onEvent(new FirmwareDownloadRequestEvent(this, "testFirmware", false, null));

        verify(inMemoryStore).getFirmwareModels();

        ArgumentCaptor<MqttMessage> argument = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient, Mockito.times(1)).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.buildGenericTopic("firmware-download", "response"), topic);
            return true;
        }), argument.capture());

        List<MqttMessage> messages = argument.getAllValues();

        FirmwareDownloadResponseMessage outputMsg = objectMapper.readValue(messages.get(0).getPayload(),
                FirmwareDownloadResponseMessage.class);
        Assertions.assertEquals(FirmwareDownloadStatus.DOWNLOADED_BUT_NOT_VERIFIED,
                outputMsg.getFirmwareDownloadStatus());
        Assertions.assertEquals("testFirmware", outputMsg.getFirmwareId());

        outputMsg = objectMapper.readValue(messages.get(0).getPayload(),
                FirmwareDownloadResponseMessage.class);

        Assertions.assertEquals("testFirmware", outputMsg.getFirmwareId());
    }

    @Test
    public void testWhenAlreadyQueuedOrStopped() throws MqttException, IOException {
        res.setDownloadStatus(FirmwareDownloadStatus.QUEUED);

        listener.onEvent(new FirmwareDownloadRequestEvent(this, "testFirmware", false, null));

        verify(inMemoryStore).getFirmwareModels();

        ArgumentCaptor<MqttMessage> argument = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient, Mockito.times(0)).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.buildGenericTopic("firmware-download", "response"), topic);
            return true;
        }), argument.capture());

        res.setDownloadStatus(FirmwareDownloadStatus.STOP_REQUESTED);
        listener.onEvent(new FirmwareDownloadRequestEvent(this, "testFirmware", true, null));

        verify(inMemoryStore, times(2)).getFirmwareModels();

        argument = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient, Mockito.times(1)).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.buildGenericTopic("firmware-download", "response"), topic);
            return true;
        }), argument.capture());

        List<MqttMessage> messages = argument.getAllValues();

        FirmwareDownloadResponseMessage outputMsg = objectMapper.readValue(messages.get(0).getPayload(),
                FirmwareDownloadResponseMessage.class);
        Assertions.assertEquals(FirmwareDownloadStatus.STOP_REQUESTED, outputMsg.getFirmwareDownloadStatus());
        Assertions.assertEquals("testFirmware", outputMsg.getFirmwareId());
    }

    @Test
    public void testWhenDownloadNeeded() throws Exception {
        res.setDownloadStatus(FirmwareDownloadStatus.DOWNLOADING_NEEDED);

        when(firmwareDownloadService.enqueueFirmwareDownloadRequests(res)).thenReturn(true);

        listener.onEvent(new FirmwareDownloadRequestEvent(this,
                "testFirmware", false, null));

        verify(inMemoryStore, times(1)).getFirmwareModels();
        verify(firmwareDownloadService).enqueueFirmwareDownloadRequests(res);

        ArgumentCaptor<MqttMessage> argument = ArgumentCaptor.forClass(MqttMessage.class);
        verify(mqttClient, Mockito.times(1)).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.buildGenericTopic("firmware-download", "response"), topic);
            return true;
        }), argument.capture());

        List<MqttMessage> messages = argument.getAllValues();

        FirmwareDownloadResponseMessage outputMsg = objectMapper.readValue(messages.get(0).getPayload(),
                FirmwareDownloadResponseMessage.class);
        Assertions.assertEquals(FirmwareDownloadStatus.DOWNLOADING_NEEDED, outputMsg.getFirmwareDownloadStatus());
        Assertions.assertEquals("testFirmware", outputMsg.getFirmwareId());
    }

}
