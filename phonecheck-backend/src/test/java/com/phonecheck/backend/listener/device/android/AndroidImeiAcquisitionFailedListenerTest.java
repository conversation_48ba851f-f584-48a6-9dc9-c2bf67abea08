package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.service.DeviceAutomationQueueService;
import com.phonecheck.backend.service.DeviceAutomationService;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidImeiAcquisitionFailedEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.android.AndroidImeiAcquisitionFailedMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AndroidImeiAcquisitionFailedListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private DeviceStageUpdater deviceStageUpdater;
    @Mock
    private DeviceAutomationService deviceAutomationService;
    @Mock
    private DeviceAutomationQueueService automationQueueService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private AndroidImeiAcquisitionFailedListener listener;

    @BeforeEach
    void setUp() {
        listener = new AndroidImeiAcquisitionFailedListener(objectMapper,
                mqttClient, deviceConnectionTracker, inMemoryStore, deviceStageUpdater,
                deviceAutomationService, automationQueueService);
        when(inMemoryStore.getTransaction()).thenReturn(new Transaction());
        when(mqttClient.isConnected()).thenReturn(true);
    }

    @Test
    void testOnEvent() throws MqttException {
        AndroidDevice device = new AndroidDevice();
        device.setId("111");

        Mockito.when(deviceConnectionTracker.getDevice(anyString())).thenReturn(device);
        final AndroidImeiAcquisitionFailedEvent event = new AndroidImeiAcquisitionFailedEvent(
                listener, device);

        listener.onEvent(event);

        Mockito.verify(mqttClient).publish(argThat(topic -> {
            assertNotNull(topic);
            assertEquals(TopicBuilder.build(device, "imei", "acquisition", "failed"), topic);
            return true;
        }), argThat(mqttMessage -> {
            assertNotNull(mqttMessage);
            assertNotNull(mqttMessage.getPayload());
            try {
                final AndroidImeiAcquisitionFailedMessage message = objectMapper.readValue(
                        mqttMessage.getPayload(), AndroidImeiAcquisitionFailedMessage.class);
                assertEquals(event.getDevice().getId(), message.getId());
            } catch (IOException e) {
                return false;
            }

            return true;
        }));

        Mockito.verify(deviceConnectionTracker).getDevice(anyString());
    }
}
