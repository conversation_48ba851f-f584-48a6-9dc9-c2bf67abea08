package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.customization.AutomationWorkflowStatus;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.AbstractDeviceEvent;
import com.phonecheck.model.event.device.DeviceColorChangeEvent;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.transaction.Transaction;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DeviceColorChangeListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private DeviceInfoDBService deviceInfoDBService;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;
    @Mock
    private InMemoryStore inMemoryStore;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private IosDevice device;
    private DeviceColorChangeListener listener;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @BeforeEach
    void setup() {
        device = new IosDevice();
        device.setId("123456");
        device.setEcid("9802390239023");
        device.setColor("red");

        listener = new DeviceColorChangeListener(objectMapper, mqttClient, deviceInfoDBService,
                inMemoryStore, deviceConnectionTracker, eventPublisher);
    }

    @Test
    void testOnEventDeviceColorChange() {
        IosDevice iosDevice = new IosDevice();
        iosDevice.setId("123456");
        iosDevice.setColor("red");

        HashMap<AutomationWorkflow, AutomationWorkflowStatus> workflowMap = new HashMap<>();
        workflowMap.put(AutomationWorkflow.TEST_RESULTS, AutomationWorkflowStatus.FAILED_REQUIRED_FIELDS);
        iosDevice.setPreviouslyRanAutomation(workflowMap);

        when(deviceConnectionTracker.getDevice(eq(device.getId()))).thenReturn(iosDevice);
        when(inMemoryStore.getTransaction()).thenReturn(Transaction.builder().transactionId(123).build());

        listener.onEvent(new DeviceColorChangeEvent(this, device));

        assertEquals(device.getColor(), iosDevice.getColor());
        verify(deviceInfoDBService).updateDeviceColor(eq(iosDevice.getId()), eq(iosDevice.getColor()), eq("123"));

        ArgumentCaptor<AbstractDeviceEvent> captor = ArgumentCaptor.forClass(AbstractDeviceEvent.class);
        verify(eventPublisher, times(2)).publishEvent(captor.capture());
    }
}
