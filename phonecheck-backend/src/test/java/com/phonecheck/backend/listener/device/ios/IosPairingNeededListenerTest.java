package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.device.results.IosPairDeviceService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.IDeviceEvent;
import com.phonecheck.model.event.device.ios.IosPairFailureEvent;
import com.phonecheck.model.event.device.ios.IosPairSuccessEvent;
import com.phonecheck.model.event.device.ios.IosPairingNeededEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.status.PairStatus;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IosPairingNeededListenerTest {
    @Mock
    private IMqttAsyncClient mqttClient;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private IosPairDeviceService iosPairDeviceService;
    @Mock
    private DeviceConnectionTracker deviceConnectionTracker;

    @Mock
    private IosDeviceInfoService iosDeviceInfoService;

    private IosDevice device;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private IosPairingNeededListener listener;

    @BeforeEach
    void setup() {
        device = new IosDevice();
        device.setId("123456");
        device.setProductType("iPhone11,8");
        device.setSerial("46325");
        listener = new IosPairingNeededListener(eventPublisher,
                iosPairDeviceService, objectMapper, mqttClient, deviceConnectionTracker, iosDeviceInfoService);
    }

    @Test
    @DisplayName("Connected device pair event")
    void testOnEventNormal() throws InterruptedException {
        final IosPairingNeededEvent event = new IosPairingNeededEvent(listener, device, false, true);
        when(mqttClient.isConnected()).thenReturn(true);
        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);

        try {
            Mockito.when(iosPairDeviceService.pairDevice(device)).thenReturn(PairStatus.PAIRED);

            listener.onEvent(event);

            Thread.sleep(2000);

            Mockito.verify(mqttClient).publish(argThat(topic -> {
                assertNotNull(topic);
                assertEquals(TopicBuilder.build(device, "pair", "start"), topic);
                return true;
            }), argThat(mqttMessage -> {
                assertNotNull(mqttMessage);
                assertNotNull(mqttMessage.getPayload());
                return true;
            }));
        } catch (IOException | MqttException e) {
            Assertions.fail(e);
        }
    }

    @Test
    @DisplayName("Pair failure publishes event")
    void testOnEventPairFailed() {
        final IosPairingNeededEvent event = new IosPairingNeededEvent(listener, device, false, true);
        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(mqttClient.isConnected()).thenReturn(true);
        try {
            Mockito.when(iosPairDeviceService.pairDevice(device)).thenReturn(PairStatus.FAILED_NO_DEVICE);

            listener.onEvent(event);

            // Make sure the device connected event was published with the right properties
            Mockito.verify(eventPublisher).publishEvent(argThat((IDeviceEvent arg) -> {
                Assertions.assertNotNull(arg);
                Assertions.assertEquals(IosPairFailureEvent.class, arg.getClass());
                IosPairFailureEvent failureEvent = (IosPairFailureEvent) arg;
                Assertions.assertEquals(device.getId(), arg.getDevice().getId());
                Assertions.assertEquals(PairStatus.FAILED_NO_DEVICE, failureEvent.getPairStatus());
                return true;
            }));
        } catch (IOException e) {
            Assertions.fail(e);
        }
    }

    @Test
    @DisplayName("Pair Failure Untrust publishes event")
    void testOnEventPairFailedUntrust() {
        final IosPairingNeededEvent event = new IosPairingNeededEvent(listener, device, false, true);
        Mockito.when(deviceConnectionTracker.getDevice(device.getId())).thenReturn(device);
        when(mqttClient.isConnected()).thenReturn(true);
        try {
            Mockito.when(iosPairDeviceService.pairDevice(device)).thenReturn(PairStatus.FAILED_UNTRUST);

            listener.onEvent(event);

            // Make sure the device connected event was published with the right properties
            Mockito.verify(eventPublisher).publishEvent(argThat((IDeviceEvent arg) -> {
                Assertions.assertNotNull(arg);
                Assertions.assertEquals(IosPairFailureEvent.class, arg.getClass());
                IosPairFailureEvent failureEvent = (IosPairFailureEvent) arg;
                Assertions.assertEquals(device.getId(), arg.getDevice().getId());
                Assertions.assertEquals(PairStatus.FAILED_UNTRUST, failureEvent.getPairStatus());
                return true;
            }));
        } catch (IOException e) {
            Assertions.fail(e);
        }
    }

    @Test
    public void testNoDeviceFound() throws MqttException {
        final IosPairingNeededEvent event = new IosPairingNeededEvent(listener, device, false, true);

        listener.onEvent(event);

        Mockito.verify(mqttClient, Mockito.never()).publish(anyString(), any(MqttMessage.class));
        Mockito.verify(eventPublisher, Mockito.never()).publishEvent(any(IosPairFailureEvent.class));
        Mockito.verify(eventPublisher, Mockito.never()).publishEvent(any(IosPairSuccessEvent.class));
    }
}
