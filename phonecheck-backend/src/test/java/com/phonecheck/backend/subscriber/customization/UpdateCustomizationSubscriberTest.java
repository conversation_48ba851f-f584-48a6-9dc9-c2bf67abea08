package com.phonecheck.backend.subscriber.customization;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.util.RestoreExecutorQueue;
import com.phonecheck.client.customization.ClientCustomizationService;
import com.phonecheck.model.customization.LocalCustomizations;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.UpdateCustomizationsMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class UpdateCustomizationSubscriberTest {

    private ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ClientCustomizationService customizationService;
    @Mock
    private InMemoryStore inMemoryStore;
    private UpdateCustomizationsSubscriber subscriber;

    @Mock
    private RestoreExecutorQueue restoreExecutorQueue;

    @BeforeEach
    void setup() {
        subscriber = new UpdateCustomizationsSubscriber(mapper, client, customizationService, inMemoryStore,
                restoreExecutorQueue);
    }

    @Test
    void testOnCustomizationRequest() throws JsonProcessingException, InterruptedException {
        final UpdateCustomizationsMessage updateCustomizationsMessage = new UpdateCustomizationsMessage();
        updateCustomizationsMessage.setUserName("user");

        MqttTopicMessage message = new MqttTopicMessage("customization/save",
                mapper.writeValueAsString(updateCustomizationsMessage));
        subscriber.onMessage(message);

        Thread.sleep(2000);
        verify(customizationService).saveCustomizations(any(LocalCustomizations.class));
    }
}
