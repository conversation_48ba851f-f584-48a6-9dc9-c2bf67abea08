package com.phonecheck.backend.subscriber.port;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.ios.IosDisconnectedEvent;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.portmap.ManualReleaseDeviceMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class ManualDeviceReleaseRequestSubscriberTest {

    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private final ObjectMapper mapper = new ObjectMapper();
    private ManualDeviceReleaseRequestSubscriber subscriber;
    @Mock
    private InMemoryStore inMemoryStore;

    @BeforeEach
    void setup() {
        subscriber = new ManualDeviceReleaseRequestSubscriber(mapper, client, eventPublisher, inMemoryStore);
    }

    @Test
    void testOnMessageAppInstallRequest() throws JsonProcessingException {
        final ManualReleaseDeviceMessage manualReleaseDeviceMessage = new ManualReleaseDeviceMessage();
        manualReleaseDeviceMessage.setId("id");

        MqttTopicMessage message = new MqttTopicMessage(DeviceFamily.IOS + "/release/device",
                mapper.writeValueAsString(manualReleaseDeviceMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(IosDisconnectedEvent.class));
    }
}
