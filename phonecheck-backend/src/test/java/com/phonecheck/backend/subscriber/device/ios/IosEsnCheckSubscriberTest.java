package com.phonecheck.backend.subscriber.device.ios;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.AsyncEsnRequestEvent;
import com.phonecheck.model.ios.EsnCheckType;
import com.phonecheck.model.mqtt.messages.EsnRequestMessage;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class IosEsnCheckSubscriberTest {
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private IMqttAsyncClient client;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    private IosEsnCheckSubscriber subscriber;

    @BeforeEach
    void setup() {
        subscriber = new IosEsnCheckSubscriber(mapper, client, eventPublisher);
    }

    @Test
    void testOnMessageDeviceEsnRequest() throws JsonProcessingException {
        final EsnRequestMessage requestMessage = new EsnRequestMessage();
        requestMessage.setId("id");
        requestMessage.setEsnCheckType(EsnCheckType.ESN_CHECK_ALL);
        MqttTopicMessage message = new MqttTopicMessage(DeviceFamily.IOS + "/esn/request",
                mapper.writeValueAsString(requestMessage));
        subscriber.onMessage(message);

        verify(eventPublisher).publishEvent(any(AsyncEsnRequestEvent.class));
    }
}
