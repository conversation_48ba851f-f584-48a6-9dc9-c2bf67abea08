package com.phonecheck.backend.subscriber;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.command.system.mac.RestartUsbmuxdCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.RestartUsbmuxdRequestMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@AllArgsConstructor
public class RestartUsbmuxdRequestSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(RestartUsbmuxdRequestSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final CommandExecutor executor;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("restart", "usbmuxd")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        // Marshal the payload JSON string to a Java object
        try {
            final RestartUsbmuxdRequestMessage request =
                    mapper.readValue(payload, RestartUsbmuxdRequestMessage.class);
            setDeviceIdInMDC(request.getId());
            LOGGER.debug("Restart usbmuxd request: {}", payload);

            new Thread(() -> {
                try {
                    if (StringUtils.isNotBlank(request.getPassword())) {
                        String output = executor.execute(new RestartUsbmuxdCommand(request.getPassword()), 5);
                        LOGGER.info("Restarting usbmuxd. output: {}", output);
                    } else {
                        LOGGER.warn("No admin password set, will not restart usbmuxd");
                    }

                } catch (final IOException e) {
                    LOGGER.error("Error restart usbmuxd", e);
                }

            }).start();
        } catch (JsonProcessingException e) {
            // There's nothing we can do at this point to fix the message format
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, e);
        }
    }
}