package com.phonecheck.backend.subscriber.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceFamily;
import com.phonecheck.model.event.device.android.AndroidDeviceSdCardWarningEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.model.mqtt.messages.android.AndroidDeviceSdCardDetectionMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;


/**
 * Reacts when user selects DONE action on SD card detection warning box
 */
@AllArgsConstructor
@Component
public class AndroidDeviceSdCardWarningActionSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidDeviceSdCardWarningActionSubscriber.class);

    private final ObjectMapper mapper;
    private final IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildForAnyDevice(DeviceFamily.ANDROID, "sd-card", "action-done")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        final String payload = msg.getPayload();
        LOGGER.debug("SD card warning response action payload: {}", payload);
        try {
            final AndroidDeviceSdCardDetectionMessage response = mapper.readValue(payload,
                    AndroidDeviceSdCardDetectionMessage.class);
            Device device = new Device() {
            };
            device.setId(response.getId());

            AndroidDeviceSdCardWarningEvent event = new AndroidDeviceSdCardWarningEvent(this, device,
                    response.isShowSdCardDefect(), response.isOnSkipEraseFlow());

            eventPublisher.publishEvent(event);
        } catch (Exception ee) {
            LOGGER.error("Could not unmarshal {} payload {}", getTopics()[0], payload, ee);
        }
    }
}
