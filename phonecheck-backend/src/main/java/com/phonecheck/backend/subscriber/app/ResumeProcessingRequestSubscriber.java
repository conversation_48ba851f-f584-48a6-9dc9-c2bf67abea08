package com.phonecheck.backend.subscriber.app;

import com.phonecheck.model.event.app.ResumeProcessingRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import com.phonecheck.mqtt.subscriber.AbstractMqttTopicSubscriber;
import lombok.AllArgsConstructor;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * Reacts when an resume processing message is published from the UI
 * Processing is halted for OTA, and resumed when requested
 */
@Component
@AllArgsConstructor
public class ResumeProcessingRequestSubscriber extends AbstractMqttTopicSubscriber {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResumeProcessingRequestSubscriber.class);

    private IMqttAsyncClient mqttClient;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public String[] getTopics() {
        return new String[]{TopicBuilder.buildGenericTopic("resume", "processing")};
    }

    @Override
    protected IMqttAsyncClient getMqttClient() {
        return mqttClient;
    }

    @Override
    public void onMessage(final MqttTopicMessage msg) {
        try {
            LOGGER.debug("Resume update request received");
            eventPublisher.publishEvent(new ResumeProcessingRequestEvent(this));
        } catch (Exception e) {
            // There's nothing we can do at this point to fix the message
            LOGGER.error("Could not unmarshal ", e);
        }
    }
}