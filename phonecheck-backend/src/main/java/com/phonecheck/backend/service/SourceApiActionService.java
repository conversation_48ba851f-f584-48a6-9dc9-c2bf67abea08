package com.phonecheck.backend.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.api.cloud.TwoWayApiCallService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.ShopfloorCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceType;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.LabelApiCallRequestEvent;
import com.phonecheck.model.event.device.ResultsApiCallRequestEvent;
import com.phonecheck.model.event.device.android.AndroidAppInstallEvent;
import com.phonecheck.model.event.device.android.AndroidPcUtilityInstallEvent;
import com.phonecheck.model.event.device.ios.IosInitiatePeoEvent;
import com.phonecheck.model.event.grading.PerformGradingRequestEvent;
import com.phonecheck.model.event.shopfloor.PerformShopfloorRequestEvent;
import com.phonecheck.model.event.shopfloor.ProceedShopfloorRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceReprocessRequestMessage;
import com.phonecheck.model.mqtt.messages.PublishableMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.twowayapi.ApiCallType;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class SourceApiActionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SourceApiActionService.class);
    private final InMemoryStore inMemoryStore;
    private final IMqttAsyncClient mqttClient;
    private final ObjectMapper mapper;
    private final ApplicationEventPublisher eventPublisher;
    private final DeviceActionService deviceActionService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final TwoWayApiCallService twoWayApiCallService;

    /**
     * Method to ensure call for Results or Label Api base on client's customization
     * Also erase device if both Api's are not called
     *
     * @param device Device
     */
    public void callResultsOrLabelApi(final Device device) {
        /*CloudCustomizationResponse.AdvancedSettings advancedSettings =
                inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings();
        if (advancedSettings != null) {
            if (advancedSettings.isApiDataSharingEnabled() &&
                    advancedSettings.isResultsEnabled()) {
                eventPublisher.publishEvent(new ResultsApiCallRequestEvent(this, device, true));
            } else if (advancedSettings.isApiDataSharingEnabled() &&
                    advancedSettings.isLabelEnabled()) {
                eventPublisher.publishEvent(new LabelApiCallRequestEvent(this, device, null));
            }
        }*/
        deviceActionService.eraseOperationRequest(device);
    }

    /**
     * Execute business logic for source api Continue Test Action
     *
     * @param apiCallType From where Api is called
     * @param apiResponse Response of source api
     * @param device      Device
     */
    public void performSourceApiContinueTestAction(final ApiCallType apiCallType,
                                                   final ObjectNode apiResponse,
                                                   final Device device) {
        if (device.getDeviceType() == DeviceType.ANDROID) {
            continueTestActionForAndroid(apiCallType, apiResponse, device);
        } else {
            continueTestActionForIos(apiResponse, device);
        }
    }

    /**
     * Execute business logic for source api Continue Test Action For Android Device
     *
     * @param apiCallType From where Api is called
     * @param apiResponse Response of source api
     * @param device      Device
     */
    private void continueTestActionForAndroid(final ApiCallType apiCallType,
                                              final ObjectNode apiResponse,
                                              final Device device) {
        switch (apiCallType) {
            case DONT_CHECK -> {
                // do nothing in this case
                LOGGER.info("Nothing to do in this scenario");
            }
            case APP_INSTALL_SHOPFLOOR_ENABLED -> {
                if (inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings() != null &&
                        inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings().isShopfloorEnabled()) {
                    LOGGER.info("Calling shopfloor method");
                    eventPublisher.publishEvent(new PerformShopfloorRequestEvent(this, device));
                } else {
                    eventPublisher.publishEvent(new PerformGradingRequestEvent(this, device,
                            !device.isGradePerformed()));
                    eventPublisher.publishEvent(new
                            AndroidAppInstallEvent(this, (AndroidDevice) device, false));
                }
            }
            case SHOPFLOOR_SOURCE_ENABLED -> {
                LOGGER.info("Calling shopfloor method");
                eventPublisher.publishEvent(new PerformShopfloorRequestEvent(this, device));
            }
            case SHOPFLOOR_DISABLED_SOURCE_ENABLED -> {
                try {
                    String sourceChannel = (apiResponse.has("sourceChannel")) ?
                            apiResponse.get("sourceChannel").textValue() : null;

                    ShopfloorCustomizationResponse.Route route = findBestMatchedRouteForAndroid(sourceChannel,
                            device);
                    if (route != null) {
                        eventPublisher.publishEvent(new PerformGradingRequestEvent(this, device,
                                !device.isGradePerformed()));
                    } else {
                        eventPublisher.publishEvent(new PerformGradingRequestEvent(this, device,
                                !device.isGradePerformed()));
                        eventPublisher.publishEvent(new AndroidPcUtilityInstallEvent(this,
                                (AndroidDevice) device));
                        eventPublisher.publishEvent(new AndroidAppInstallEvent(this, (AndroidDevice) device,
                                false));
                    }
                } catch (Exception e) {
                    LOGGER.error("Exception occurred while continue test action for source api ", e);
                }
            }
            default -> {
                LOGGER.error("Invalid Api call type {}", apiCallType);
            }
        }
    }

    /**
     * Execute business logic for source api Continue Test Action For Ios Device
     *
     * @param apiResponse Response of source api
     * @param device      target device
     */
    private void continueTestActionForIos(final ObjectNode apiResponse, final Device device) {
        try {
            List<ShopfloorCustomizationResponse.ShopfloorObject> routes = new ArrayList<>();
            if (inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings() != null &&
                    inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings().isShopfloorEnabled()) {
                String sourceChannel = (apiResponse.has("sourceChannel")) ?
                        apiResponse.get("sourceChannel").toString() : null;
                routes = findBestMatchingRouteForIos(sourceChannel, device);
            }
            if (routes != null) {
                eventPublisher.publishEvent(new PerformShopfloorRequestEvent(this, device));
            } else {
                eventPublisher.publishEvent(new PerformGradingRequestEvent(this, device,
                        !device.isGradePerformed()));
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while continue test action for iOS ", e);
        }
    }

    /**
     * Finds the best matching route for an iOS device based on the specified source channel.
     *
     * @param sourceChannel The source channel to match against.
     * @param device        The iOS device for which the route is being determined.
     * @return The list of shopfloor objects that match the source channel, or null if no matching route is found.
     */
    private List<ShopfloorCustomizationResponse.ShopfloorObject> findBestMatchingRouteForIos(
            final String sourceChannel, final Device device) {
        List<ShopfloorCustomizationResponse.ShopfloorObject> selectedRoute;
        if (!device.isRouteAvailable()) {
            selectedRoute = getShopObjectsBySourceChannel(
                    inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getStations(), sourceChannel);
            if (!selectedRoute.isEmpty()) {
                return selectedRoute;
            }

            selectedRoute = getShopObjectsBySourceChannel(
                    inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getWarehouse(), sourceChannel);
            if (!selectedRoute.isEmpty()) {
                return selectedRoute;
            }

            selectedRoute = getShopObjectsBySourceChannel(
                    inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getMaster(), sourceChannel);
            if (!selectedRoute.isEmpty()) {
                return selectedRoute;
            }
        }
        return null;
    }

    /**
     * Retrieves shopfloor objects from the given list that match the specified source channel.
     *
     * @param shopData      The list of shopfloor objects to filter.
     * @param sourceChannel The source channel to match against.
     * @return A list of shopfloor objects that match the source channel.
     */
    private List<ShopfloorCustomizationResponse.ShopfloorObject> getShopObjectsBySourceChannel(
            final List<ShopfloorCustomizationResponse.ShopfloorObject> shopData, final String sourceChannel) {
        ArrayList<ShopfloorCustomizationResponse.ShopfloorObject> matchingRoute = new ArrayList<>();
        if (shopData != null && !shopData.isEmpty()) {
            for (ShopfloorCustomizationResponse.ShopfloorObject shopObjects : shopData) {
                if (shopObjects.getRoute().getSourceChannel().equalsIgnoreCase(
                        sourceChannel.replace("\"", ""))) {
                    matchingRoute.add(shopObjects);
                }
            }
        }
        return matchingRoute;
    }

    /**
     * Execute business logic for source api Reprocess Action
     *
     * @param device Device
     */
    public void performSourceApiReProcessAction(final Device device) {
        final String topic = TopicBuilder.build(device, "device", "reprocess", "popup", "request");
        final DeviceReprocessRequestMessage message = new DeviceReprocessRequestMessage();
        message.setId(device.getId());
        publishToMqttTopic(topic, message);
    }

    /**
     * Execute business logic for source api Reprint Action
     *
     * @param device Device
     */
    public void performReprintAction(final Device device) {
        new Thread(() -> {
            Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
            if (StringUtils.isNotBlank(deviceInTracker.getSourceApiResponse2()) && isPrintCustomizationEnabled()) {
                deviceActionService.printOperationRequest(deviceInTracker, inMemoryStore.getLocalCustomizations());
            } else {
                if (inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings() != null &&
                        inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings().isApiDataSharingEnabled()) {

                    if (inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings().isLabelEnabled()) {
                        eventPublisher.publishEvent(new LabelApiCallRequestEvent(this, deviceInTracker, null));
                    } else if (inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings().isResultsEnabled()) {
                        eventPublisher.publishEvent(new ResultsApiCallRequestEvent(this, deviceInTracker, false));
                    }
                }
            }
        }).start();
    }

    /**
     * Execute business logic for reprocess action selected from Device Reprocess Pop-up
     *
     * @param device Device
     */
    public void performReprocessAction(final Device device) {
        new Thread(() -> {
            Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
            if (deviceInTracker.getDeviceType() == DeviceType.ANDROID) {
                LOGGER.info("Calling shopfloor method");
                eventPublisher.publishEvent(new PerformShopfloorRequestEvent(this, deviceInTracker));
            } else {
                deviceInTracker.setIsErasePerformed(false);
                deviceInTracker.setSourceApiCalled(false);
                deviceInTracker.setGradingSystemGrade(StringUtils.EMPTY);
                eventPublisher.publishEvent(new IosInitiatePeoEvent(
                        this, (IosDevice) deviceInTracker, false));
                ObjectNode apiResponseObject = mapper.createObjectNode();
                try {
                    apiResponseObject = mapper.readValue(deviceInTracker.getSourceApiResponse1(), ObjectNode.class);
                    apiResponseObject = twoWayApiCallService.flattenObjectNode(apiResponseObject);
                } catch (Exception e) {
                    LOGGER.info("Exception occurred while reading apiResponse");
                }
                String sourceChannel = (apiResponseObject.has("sourceChannel")) ?
                        apiResponseObject.get("sourceChannel").toString() : null;
                List<ShopfloorCustomizationResponse.ShopfloorObject> routes =
                        findBestMatchingRouteForIos(sourceChannel, deviceInTracker);
                if (inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings() != null &&
                        inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings().isShopfloorEnabled()) {
                    if (routes != null) {
                        eventPublisher.publishEvent(new ProceedShopfloorRequestEvent(this, deviceInTracker,
                                routes, false));
                    } else {
                        try {
                            eventPublisher.publishEvent(new PerformShopfloorRequestEvent(this, deviceInTracker));
                        } catch (Exception e) {
                            LOGGER.error("Exception occurred while performing Reprocess action of source api ", e);
                        }
                    }
                }
            }
        }).start();
    }

    /**
     * Execute business logic for source api Call Result Action
     *
     * @param device      Device
     * @param apiResponse Response of source api
     */
    public void performSourceAPICallResultAction(final Device device,
                                                 final ObjectNode apiResponse) {
        CloudCustomizationResponse.AdvancedSettings advancedSettings =
                inMemoryStore.getAssignedCloudCustomization().getAdvancedSettings();
        if (advancedSettings != null && advancedSettings.isApiDataSharingEnabled()) {
            if (advancedSettings.isResultsEnabled()) {
                eventPublisher.publishEvent(new ResultsApiCallRequestEvent(
                        this, device, true));
            } else if (advancedSettings.isLabelEnabled()) {
                eventPublisher.publishEvent(new LabelApiCallRequestEvent(this, device, null));
            }
            if (device.getDeviceType() == DeviceType.IPHONE || device.getDeviceType() == DeviceType.IPAD) {
                try {
                    String statusMessage = (apiResponse.has("statusMessage")) ?
                            apiResponse.get("statusMessage").textValue() : null;
                    if (StringUtils.isNotBlank(statusMessage) &&
                            StringUtils.containsIgnoreCase(statusMessage, "EraseReset")) {
                        deviceActionService.eraseOperationRequest(device);
                    }
                } catch (Exception e) {
                    LOGGER.error("Exception occurred while source api call results action ", e);
                }
            }
        }
    }

    /**
     * Publish a message to an MQTT topic.
     *
     * @param topic          mqtt topic
     * @param requestMessage mqtt message
     */
    protected void publishToMqttTopic(final String topic, final PublishableMessage requestMessage) {
        try {
            final MqttMessage message = new MqttMessage();
            message.setPayload(mapper.writeValueAsBytes(requestMessage));
            mqttClient.publish(topic, message);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while publishing to Mqtt ", e);
        }
    }

    /**
     * This method is used to fetch route
     *
     * @param sourceChannel Source chanel
     * @param device        target device
     * @return ShopfloorCustomizationDataResponse.Route
     */
    private ShopfloorCustomizationResponse.Route findBestMatchedRouteForAndroid(final String sourceChannel,
                                                                                final Device device) {
        ShopfloorCustomizationResponse.Route matchingRoute = null;
        if (!device.isRouteAvailable()) {
            matchingRoute = findMatchingRoute(true, false, sourceChannel, device);
        }
        if (!device.isRouteAvailable() && matchingRoute == null) {
            matchingRoute = findMatchingRoute(false, true, sourceChannel, device);
        }
        if (!device.isRouteAvailable() && matchingRoute == null) {
            matchingRoute = findMatchingRoute(false, false, sourceChannel, device);
        }
        return matchingRoute;
    }

    /**
     * @param isStation     If route is of Station
     * @param isWarehouse   If route is of warehouse
     * @param sourceChannel source channel
     * @param device        target device
     * @return ShopfloorCustomizationDataResponse.Route
     */
    private ShopfloorCustomizationResponse.Route findMatchingRoute(
            final boolean isStation, final boolean isWarehouse, final String sourceChannel, final Device device) {
        List<ShopfloorCustomizationResponse.ShopfloorObject> shopData = null;
        if (isStation) {
            shopData = inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getStations();
        } else {
            eventPublisher.publishEvent(new PerformShopfloorRequestEvent(this, device));
            if (isWarehouse) {
                shopData = inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getWarehouse();
            } else {
                shopData = inMemoryStore.getShopfloorResponse().getShopfloorCustomization().getMaster();
            }
        }
        if (shopData != null && !shopData.isEmpty()) {
            for (ShopfloorCustomizationResponse.ShopfloorObject shopObjects : shopData) {
                if (shopObjects.getRoute().getSourceChannel().contains(sourceChannel)) {
                    return shopObjects.getRoute();
                }
            }
        }
        return null;
    }

    /***
     * Checks if print customization is enabled
     *
     * @return true if enabled, otherwise false
     */
    private boolean isPrintCustomizationEnabled() {
        LOGGER.info("Check if print customization enabled");
        return inMemoryStore.getAssignedCloudCustomization().getWorkflow() != null &&
                inMemoryStore.getAssignedCloudCustomization().getWorkflow().getTestResultWorkflow() != null &&
                inMemoryStore.getAssignedCloudCustomization().getWorkflow().isTestResultWorkflowEnabled() &&
                inMemoryStore.getAssignedCloudCustomization().getWorkflow().getTestResultWorkflow().contains(
                        CloudCustomizationResponse.AutomationSteps.PRINT);
    }
}