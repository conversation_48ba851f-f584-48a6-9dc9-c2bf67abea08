package com.phonecheck.backend.service;

import com.phonecheck.backend.util.download.DownloadListener;
import com.phonecheck.dao.model.TblFirmwareInfo;
import com.phonecheck.dao.service.FirmwareInfoDBService;
import com.phonecheck.model.event.FirmwareDownloadResponseEvent;
import com.phonecheck.model.firmware.FirmwareDownloadStatus;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.mount.image.UnzipFileService;
import com.phonecheck.parser.device.UnzipFileParser;
import lombok.AllArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import static com.phonecheck.backend.util.download.DownloadUtil.downloadFile;

@Service
@AllArgsConstructor
public class FirmwareDownloadService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FirmwareDownloadService.class);
    private static final BlockingQueue<FirmwareModel.FirmwareResponse> FIRMWARE_REQ_QUEUE = new LinkedBlockingQueue<>();
    private static final int MAX_RETRIES = 3;

    private final UnzipFileService unzipFileService;

    private final ApplicationEventPublisher eventPublisher;

    private final InMemoryStore inMemoryStore;

    private final FileUtil fileUtil;

    private final FirmwareInfoDBService firmwareInfoDBService;

    private final OsChecker osChecker;

    private final UnzipFileParser unzipFileParser;

    /**
     * Unzips the firmware file (.ipsw) specified by the provided File object.
     * The method checks the existence and readability of the file before attempting to unzip it.
     *
     * @param ipswFilePath The File object representing the firmware file (.ipsw) to be unzipped.
     * @param unzipPath The unzip path of firmware file
     * @return true if the unzip operation is successful, false otherwise.
     */
    public boolean unzipFirmwareZip(final File ipswFilePath, final String unzipPath) {
        if (ipswFilePath.exists() && ipswFilePath.canRead()) {
            int retries = 0;
            while (true) {
                try {
                    final File unzipFolder = new File(unzipPath);
                    if (unzipFolder.exists()) {
                        LOGGER.info("Old folder with same ECID is present at {}, hence deleting it now", unzipPath);
                        fileUtil.deleteDirectoryRecursively(unzipFolder);
                    }
                    String output = unzipFileService.unZipFirmware(ipswFilePath.getAbsolutePath(), unzipPath);
                    if (StringUtils.isNotBlank(output)) {
                        final boolean isFileExtracted = unzipFileParser.isFileExtracted(output, osChecker.isMac());
                        LOGGER.info("parser output: {}. File {} unzipped successfully to location: {}.",
                                isFileExtracted, ipswFilePath.getName(), unzipPath);
                        if (isFileExtracted) {
                            LOGGER.info("{} copying over SE folder", unzipPath);
                            copyOverSEFolderToFirmware(ipswFilePath);
                            return true;
                        }
                        return false;
                    } else {
                        if (retries < MAX_RETRIES) {
                            retries++;
                        } else {
                            break;
                        }
                        LOGGER.error("Error occurred while unzipping {}", ipswFilePath.getName());
                    }
                } catch (IOException e) {
                    LOGGER.error("Error occurred while unzipping {}", ipswFilePath.getName(), e);
                }
            }
            LOGGER.info("Firmware extraction completed for the file: {}", ipswFilePath.getName());
        } else {
            LOGGER.info("File :{} is not download correctly. Please re-download", ipswFilePath.getAbsolutePath());
        }
        return false;
    }

    public void copyOverSEFolderToFirmware(final File ipswFilePath) throws IOException {
        String srcPath = ipswFilePath.getAbsolutePath().replace(".ipsw", "") + "/Firmware/SE";
        String destPath = ipswFilePath.getAbsolutePath().replace(".ipsw", "");
        File sourceSePath = new File(srcPath);
        File destSePath = new File(destPath);
        if (sourceSePath.exists()) {
            FileUtils.copyDirectoryToDirectory(sourceSePath, destSePath);
            LOGGER.info("SE File Extraction for firmware :{}. File copied to destination", destPath);
        }
    }

    public boolean enqueueFirmwareDownloadRequests(final FirmwareModel.FirmwareResponse firmwareObj) {
        boolean result = false;
        if (!FIRMWARE_REQ_QUEUE.contains(firmwareObj)) {
            result = FIRMWARE_REQ_QUEUE.offer(firmwareObj);
        }
        return result;
    }

    public void startFirmwareDownloadThread() {
        new Thread(() -> {
            while (true) {
                try {
                    FirmwareModel.FirmwareResponse firmware = FIRMWARE_REQ_QUEUE.poll();
                    if (firmware != null) {
                        final String downloadPath = inMemoryStore.getFirmwareDownloadPath();

                        LOGGER.info("Downloading request received for firmware for id : {}, " +
                                        "firmware downloading status : {} and firmware download path: {}",
                                firmware.getId(), firmware.getDownloadStatus(), downloadPath);

                        if (FirmwareDownloadStatus.SUCCESS.equals(firmware.getDownloadStatus())) {
                            LOGGER.info("Firmware with id: {} was already downloaded hence skipping.",
                                    firmware.getId());
                            continue;
                        }

                        File firmwarePath = new File(downloadPath);

                        if (!firmwarePath.exists()) {
                            firmwarePath.mkdirs();
                        }

                        final File fileToDownload = new File(firmwarePath.getAbsolutePath(), firmware.getFileName());

                        final String urlString = firmware.getUrl();
                        final String fileName = firmware.getFileName();
                        downloadFile(urlString, fileToDownload, new DownloadListener() {
                            private boolean isStop = false;
                            private int lastProgress = -1;

                            @Override
                            public void onProgress(final int progress, final long downloaded, final long totalSize) {
                                if (FirmwareDownloadStatus.STOP_REQUESTED == firmware.getDownloadStatus()) {
                                    isStop = true;
                                    return;
                                }
                                updateStatusForFirmware(fileName, FirmwareDownloadStatus.IN_PROGRESS);
                                if (progress != lastProgress) {
                                    lastProgress = progress;
                                    LOGGER.info("progress for firmware id : {} is {}", firmware.getId(), progress);
                                    final FirmwareDownloadResponseEvent event = new FirmwareDownloadResponseEvent(
                                            this, firmware.getId(), firmware.getDownloadStatus(), progress);
                                    eventPublisher.publishEvent(event);
                                }
                            }

                            @Override
                            public void onFailure(final Exception e) {
                                int retries = firmware.getRetries();
                                if (retries < MAX_RETRIES && !isStop) {
                                    ++retries;
                                    LOGGER.info("Download failed. Retrying... (Retry {} of {})", retries, MAX_RETRIES);
                                    firmware.setRetries(retries);
                                    downloadFile(urlString, fileToDownload, this);
                                } else {
                                    LOGGER.info("Download firmware failed after {} retries", MAX_RETRIES, e);
                                    updateStatusForFirmware(fileName, FirmwareDownloadStatus.DOWNLOADING_FAILED);
                                    final FirmwareDownloadResponseEvent event = new FirmwareDownloadResponseEvent(
                                            this, firmware.getId(), firmware.getDownloadStatus(), -1);
                                    eventPublisher.publishEvent(event);
                                }
                            }

                            @Override
                            public boolean stopDownloading() {
                                return isStop;
                            }

                            @Override
                            public void onComplete() {
                                int progress = -1;
                                updateStatusForFirmware(fileName, FirmwareDownloadStatus.SUCCESS);
                                final FirmwareDownloadResponseEvent event = new FirmwareDownloadResponseEvent(
                                        this, firmware.getId(), firmware.getDownloadStatus(), progress);
                                eventPublisher.publishEvent(event);
                                LOGGER.info("Firmware file (ipsw) download completed for the id:{}", firmware.getId());
                            }

                            @Override
                            public void onStop() {
                                LOGGER.info("Stop is called for the firmware id :{}", firmware.getId());
                                updateStatusForFirmware(fileName, FirmwareDownloadStatus.DOWNLOADING_STOPPED);
                                final FirmwareDownloadResponseEvent event = new FirmwareDownloadResponseEvent(
                                        this, firmware.getId(), firmware.getDownloadStatus(), 0);
                                eventPublisher.publishEvent(event);
                            }
                        });
                    }
                } catch (Exception ee) {
                    LOGGER.error("Error while processing firmware download request.", ee);
                }
            }
        }).start();
    }

    public void updateStatusForFirmware(final String fileName, final FirmwareDownloadStatus status) {
        List<FirmwareModel.FirmwareResponse> firmwareList = inMemoryStore.getFirmwareModels().values()
                .stream().filter(s -> fileName.equals(s.getFileName())).toList();

        for (FirmwareModel.FirmwareResponse firmware : firmwareList) {
            firmware.setDownloadStatus(status);
        }
    }

    public void extractIpswFile(final FirmwareModel.FirmwareResponse firmware) {
        final File fileToDownload = getFirmwareIpswPath(firmware.getFileName());
        final File ipswFolder = new File(fileToDownload.getAbsolutePath().replace(".ipsw", ""));
        if (unzipFirmwareZip(fileToDownload, ipswFolder.getAbsolutePath())) {
            updateStatusForFirmware(firmware.getFileName(), FirmwareDownloadStatus.EXTRACTED);
        } else {
            updateStatusForFirmware(firmware.getFileName(), FirmwareDownloadStatus.EXTRACTION_FAILED);
        }
    }


    private File getFirmwareIpswPath(final String fileName) {
        File firmwarePath = new File(inMemoryStore.getFirmwareDownloadPath());
        if (!firmwarePath.exists()) {
            firmwarePath.mkdirs();
        }
        return new File(firmwarePath.getAbsolutePath(), fileName);
    }

    public void loadFirmwareInfoFromDB() {
        Map<String, FirmwareModel.FirmwareResponse> firmwareMap = inMemoryStore.getFirmwareModels();

        for (Map.Entry<String, FirmwareModel.FirmwareResponse> firmwareEntry : firmwareMap.entrySet()) {
            TblFirmwareInfo firmwareFrmDB = firmwareInfoDBService.getFirmwareInfo(firmwareEntry.getKey());

            FirmwareModel.FirmwareResponse firmwareFromApi = firmwareEntry.getValue();
            if (firmwareFrmDB == null) {
                LOGGER.info("No data present in the DB for the firmware:{}", firmwareEntry.getKey());
                continue;
            }
            LOGGER.info("Data found for key:{} and folder hash is:{}, file name:{}, DB version:{}, api version:{}",
                    firmwareFrmDB.getFirmwareId(), firmwareFrmDB.getFolderHash(), firmwareFrmDB.getFileName(),
                    firmwareFrmDB.getVersion(), firmwareFromApi.getVersion());

            if (StringUtils.isNotBlank(firmwareFrmDB.getVersion())
                    && !firmwareFrmDB.getVersion().equals(firmwareFromApi.getVersion())) {
                deleteOldFirmware(firmwareFrmDB.getFileName());
                firmwareInfoDBService.deleteFirmwareInfo(firmwareFrmDB);
                LOGGER.info("Deleted DB entry for the firmware id:{}", firmwareFrmDB.getFirmwareId());
            } else {
                if (StringUtils.isBlank(firmwareFrmDB.getVersion())) {
                    firmwareFrmDB.setVersion(firmwareFromApi.getVersion());
                    firmwareFrmDB.setFileName(firmwareFromApi.getFileName());
                    firmwareInfoDBService.saveFirmwareInfo(firmwareFrmDB);
                }
                firmwareFromApi.setFolderMd5Hash(firmwareFrmDB.getFolderHash());
            }
        }
    }

    private void saveFirmwareInfoInDB(final FirmwareModel.FirmwareResponse firmware) {
        TblFirmwareInfo firmwareInfo = new TblFirmwareInfo();
        firmwareInfo.setFirmwareId(firmware.getId());
        firmwareInfo.setFileHash(firmware.getMd5Sum());
        firmwareInfo.setFolderHash(firmware.getFolderMd5Hash());
        firmwareInfo.setFileName(firmware.getFileName());
        firmwareInfo.setVersion(firmware.getVersion());
        firmwareInfoDBService.saveFirmwareInfo(firmwareInfo);
    }

    /**
     * Saves MD5 hash to all the objects having same file name
     * For each firmware with same file name, a row will be inserted in local DB
     *
     * @param currentFirmware Firmware being processed currently
     * @param md5Hash         : MD5 hash of firmware folder
     */
    private void saveMd5HashForFolder(final FirmwareModel.FirmwareResponse currentFirmware, final String md5Hash) {
        List<FirmwareModel.FirmwareResponse> firmwareList = inMemoryStore.getFirmwareModels().values()
                .stream().filter(s -> currentFirmware.getFileName().equals(s.getFileName())).toList();

        for (FirmwareModel.FirmwareResponse firmware : firmwareList) {
            firmware.setFolderMd5Hash(md5Hash);
            saveFirmwareInfoInDB(firmware);
        }
    }

    /**
     * checks of firmware folder or ipsw file is already downloaded
     *
     * @param fileName The firmware file name
     * @return FirmwareDownloadStatus : status post verification
     */
    public FirmwareDownloadStatus getFirmwareStatusOnLoad(final String fileName) {
        FirmwareDownloadStatus currentStatus = FirmwareDownloadStatus.DOWNLOADING_NEEDED;
        File ipswFile = getFirmwareIpswPath(fileName);
        if (ipswFile.exists()) {
            currentStatus = FirmwareDownloadStatus.SUCCESS;
        }
        return currentStatus;
    }

    /**
     * If firmware folder is present and if DB entry for the same is null, then
     * those firmwares were downloaded with build prior to 3.8.5. We assume those to
     * be in good state.
     *
     * @param firmware current firmware
     * @return boolean : true if firmware was downloaded before , false otherwise
     */
    public boolean isFirmwareDownloadedFromPreviousVersion(final FirmwareModel.FirmwareResponse firmware) {
        boolean isFirmwareOk = false;
        File ipswFile = getFirmwareIpswPath(firmware.getFileName());
        final File ipswFolder = new File(ipswFile.getAbsolutePath().replace(".ipsw", ""));
        LOGGER.info("Firmware Id:{}, folder path:{}", firmware.getId(), ipswFolder.getAbsolutePath());
        if (ipswFolder.exists() && StringUtils.isBlank(firmware.getFolderMd5Hash())) {
            LOGGER.info("Firmware: {} is already downloaded by prior version so marking it safe", firmware.getId());
            isFirmwareOk = true;
        }
        return isFirmwareOk;
    }

    private void deleteOldFirmware(final String fileName) {
        LOGGER.info("Firmware file to be deleted :{}", fileName);
        //Starting new thread to delete all the old firmwares and ipsw files
        File ipswFile = getFirmwareIpswPath(fileName);
        final File ipswFolder = new File(ipswFile.getAbsolutePath().replace(".ipsw", ""));

        LOGGER.info("Folder to be deleted path:{}, .ipsw file path:{}", ipswFolder.getAbsolutePath(),
                ipswFile.getAbsoluteFile());

        if (ipswFolder.exists()) {
            LOGGER.info("Deleting old firmware present at:{}", ipswFolder.getAbsolutePath());
            fileUtil.deleteDirectoryRecursively(ipswFolder);
        }
        LOGGER.info("Deleting ipsw file : {}", ipswFile.delete());
    }
}
