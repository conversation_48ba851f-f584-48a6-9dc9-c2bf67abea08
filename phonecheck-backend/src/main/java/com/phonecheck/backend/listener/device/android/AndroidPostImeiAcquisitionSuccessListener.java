package com.phonecheck.backend.listener.device.android;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.AndroidShopfloorService;
import com.phonecheck.backend.service.VendorCriteriaService;
import com.phonecheck.device.results.file.push.BatteryService;
import com.phonecheck.model.cloudapi.ShopfloorCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceProcessStatus;
import com.phonecheck.model.device.DeviceRunningProcesses;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceMeidInfoEvent;
import com.phonecheck.model.event.device.android.*;
import com.phonecheck.model.event.grading.PerformGradingRequestEvent;
import com.phonecheck.model.event.shopfloor.PerformShopfloorRequestEvent;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.util.CustomizationUtil;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.phonecheck.model.twowayapi.TwoWayApiConstants.DEFAULT_SOURCE_CHANNEL;

@Component
public class AndroidPostImeiAcquisitionSuccessListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidPostImeiAcquisitionSuccessListener.class);

    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;
    private final ApplicationEventPublisher eventPublisher;
    private final BatteryService batteryService;
    private final VendorCriteriaService vendorCriteriaService;
    private final CustomizationUtil customizationUtil;

    private final AndroidShopfloorService androidShopfloorService;


    public AndroidPostImeiAcquisitionSuccessListener(final ObjectMapper objectMapper,
                                                     final IMqttAsyncClient mqttClient,
                                                     final ApplicationEventPublisher eventPublisher,
                                                     final DeviceConnectionTracker deviceConnectionTracker,
                                                     final InMemoryStore inMemoryStore,
                                                     final BatteryService batteryService,
                                                     final VendorCriteriaService vendorCriteriaService,
                                                     final CustomizationUtil customizationUtil,
                                                     final AndroidShopfloorService androidShopfloorService) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.eventPublisher = eventPublisher;
        this.inMemoryStore = inMemoryStore;
        this.batteryService = batteryService;
        this.vendorCriteriaService = vendorCriteriaService;
        this.customizationUtil = customizationUtil;
        this.androidShopfloorService = androidShopfloorService;
    }

    @EventListener
    public void onEvent(final AndroidPostImeiAcquisitionSuccessEvent event) {
        final AndroidDevice device = (AndroidDevice) event.getDevice();
        setDeviceIdMDC(device.getId());
        boolean shouldInstallApps = event.isShouldInstallApps();

        final AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Post IMEI acquisition event received, but device has been disconnected, stopping processing.");
            return;
        }
        LOGGER.info("Android post imei acquisition success event received");


        if (customizationUtil.isShopfloorCustomizationEnabled() && !deviceInTracker.isSourceApiCalled()) {
            callShopfloorEventForTheDevice(deviceInTracker);
        }

        //Create a battery API file
        createBatteryApiFile(inMemoryStore.getLicenseId(),
                inMemoryStore.getTransaction().getTransactionId(), device);

        // Perform client customization
        final AndroidPerformCustomizationEvent performCustomizationEvent =
                new AndroidPerformCustomizationEvent(this, deviceInTracker);
        eventPublisher.publishEvent(performCustomizationEvent);


        // Async event to fetch meid info
        final DeviceMeidInfoEvent meidInfoCollectionEvent
                = new DeviceMeidInfoEvent(this, deviceInTracker);
        eventPublisher.publishEvent(meidInfoCollectionEvent);

        // Async event for Knox info collection
        final AndroidKnoxInfoEvent knoxInfoEvent
                = new AndroidKnoxInfoEvent(this, deviceInTracker);
        eventPublisher.publishEvent(knoxInfoEvent);

        // Async event for MDM info collection
        final AndroidMdmAcquisitionEvent mdmAcquisitionEvent
                = new AndroidMdmAcquisitionEvent(this, deviceInTracker);
        eventPublisher.publishEvent(mdmAcquisitionEvent);

        if (shouldInstallApps && waitForBatteryInfoReceived(deviceInTracker)) {
            // Async event to install Pc Utility
            final AndroidPcUtilityInstallEvent androidPcUtilityInstallEvent
                    = new AndroidPcUtilityInstallEvent(this, deviceInTracker);
            eventPublisher.publishEvent(androidPcUtilityInstallEvent);

            // Synchronized event to install phonecheck application
            final AndroidAppInstallEvent androidAppInstallEvent
                    = new AndroidAppInstallEvent(this, deviceInTracker, false);
            eventPublisher.publishEvent(androidAppInstallEvent);
        }

        vendorCriteriaService.calculateVendorCriteria(device);
    }

    /**
     * Creates BatteryApi.json file in syncable files folder
     *
     * @param licenseId     tester license id
     * @param transactionId current transaction id
     * @param device        Android object
     */
    protected void createBatteryApiFile(final int licenseId,
                                        final int transactionId,
                                        final AndroidDevice device) {
        try {
            batteryService.createBatteryApiFileForAndroid(licenseId, transactionId, device);
        } catch (IOException e) {
            LOGGER.error("Failed to create BatteryApi.json file", e);
        }
    }

    /**
     * Checks if the device should wait for battery running process to complete.
     *
     * @param device the device being processed
     * @return True if battery info is collected or 1 min passed
     */
    private boolean waitForBatteryInfoReceived(final Device device) {
        try {
            if (StringUtils.equalsIgnoreCase(inMemoryStore.getCurrentLanguage(), "Japanese")) {
                int retryCounter = 0;
                do {
                    if (device.getDeviceProcessStatusMap().getOrDefault(DeviceRunningProcesses.BATTERY,
                            DeviceProcessStatus.WAITING) == DeviceProcessStatus.COMPLETED) {
                        LOGGER.info("Battery process has been completed, continue processing");
                        break;
                    } else {
                        List<String> waitingProcessNames =
                                device.getDeviceProcessStatusMap().entrySet().stream()
                                        .filter(entry -> entry.getKey() == DeviceRunningProcesses.BATTERY &&
                                                entry.getValue() == DeviceProcessStatus.WAITING)
                                        .map(entry -> entry.getKey().name())
                                        .collect(Collectors.toList());

                        LOGGER.info("Waiting for battery process to complete: {}", waitingProcessNames);
                    }
                    retryCounter++;
                    Thread.sleep(1000);
                } while (retryCounter < 60);
            }
        } catch (Exception e) {
            LOGGER.error("Error while waiting for battery info retrieval");
        }
        return true;
    }


    /**
     * This method calls shopfloor event for the device
     * @param device device in action
    * */

    private void  callShopfloorEventForTheDevice(final AndroidDevice device) {
        ShopfloorCustomizationResponse.Route route = androidShopfloorService.findBestMatchedRouteForAndroid(
                    DEFAULT_SOURCE_CHANNEL, device);

        LOGGER.info("calling shopfloor event with best route :{}", route);

        if (route != null) {
            LOGGER.info("Raising shopfloor request event");
            eventPublisher.publishEvent(new PerformShopfloorRequestEvent(this, device));
        } else {
            LOGGER.info("Raising grading request event");
            eventPublisher.publishEvent(new PerformGradingRequestEvent(this, device,
                    !device.isGradePerformed()));
        }

    }
}
