package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudCarrierAndSimLockService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.util.DeviceUtil;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.cloudapi.SimLockLicenseManagerRequest;
import com.phonecheck.model.cloudapi.SimLockLicenseManagerResponse;
import com.phonecheck.model.constants.ErrorConstants;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.customization.AutomationWorkflowStatus;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.stage.SimLockInfoSuccessStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceSimLockCheckEvent;
import com.phonecheck.model.event.device.DeviceTestResultAutomationEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.IosSimLockCheckResponseMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Initiates sim lock check
 */
@Component
public class DeviceSimLockCheckListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceSimLockCheckListener.class);
    private static final String OFF = "Off";
    private static final String LOCKED = "Locked";
    private static final String UNLOCKED = "Unlocked";
    private static final String SERVER_ERROR = "Server temporarily not available, please try again";

    private final ObjectMapper objectMapper;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;
    private final DeviceStageUpdater stageUpdater;
    private final CloudCarrierAndSimLockService cloudCarrierAndSimlockService;
    private final ApplicationEventPublisher eventPublisher;

    public DeviceSimLockCheckListener(final IMqttAsyncClient mqttClient,
                                      final DeviceConnectionTracker deviceConnectionTracker,
                                      final InMemoryStore inMemoryStore,
                                      final ObjectMapper objectMapper,
                                      final DeviceStageUpdater stageUpdater,
                                      final CloudCarrierAndSimLockService cloudCarrierAndSimlockService,
                                      final ApplicationEventPublisher eventPublisher) {
        super(mqttClient, objectMapper);
        this.objectMapper = objectMapper;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
        this.stageUpdater = stageUpdater;
        this.cloudCarrierAndSimlockService = cloudCarrierAndSimlockService;
        this.eventPublisher = eventPublisher;
    }

    @EventListener
    public void onEvent(final DeviceSimLockCheckEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("SimLock check to be performed but device has been disconnected, stopping processing.");
            return;
        }

        if (DeviceUtil.isNonImeiDevice(deviceInTracker)) {
            LOGGER.info("Sim-lock check is not performed for non IMEI device with device type: {}",
                    device.getDeviceType());
            return;
        }

        LOGGER.info("Performing sim lock check for device.");

        try {
            SimLockLicenseManagerResponse rawResponse;
            boolean isLicenseExpired = false;

            if (StringUtils.isNotBlank(deviceInTracker.getSimLockRawResponse())
                    && !StringUtils.containsIgnoreCase(deviceInTracker.getSimLockRawResponse(), SERVER_ERROR)) {
                rawResponse =
                        objectMapper.readValue(deviceInTracker.getSimLockRawResponse(),
                                SimLockLicenseManagerResponse.class);
            } else {
                SimLockLicenseManagerRequest request = SimLockLicenseManagerRequest.builder()
                        .carrier(deviceInTracker.getCarrier())
                        .apiKey(inMemoryStore.getApiKey())
                        .deviceId(deviceInTracker.getImei())
                        .userId(inMemoryStore.getUserName())
                        .deviceType(deviceInTracker.getDeviceType().name())
                        .build();

                rawResponse = cloudCarrierAndSimlockService.getSimLockStatus(request);
            }

            LOGGER.info("Sim lock check response: {}", rawResponse);

            if (rawResponse != null
                    && !Boolean.TRUE.equals(rawResponse.getIsLicenseExpired())) {

                String strJsonRawResponse = objectMapper.writeValueAsString(rawResponse);
                deviceInTracker.setSimLockRawResponse(strJsonRawResponse);

                final String remarks = rawResponse.getRemarks();
                final String simLockStatus;

                if (StringUtils.containsIgnoreCase(rawResponse.getRawResponse(), SERVER_ERROR)) {
                    simLockStatus = SERVER_ERROR;
                } else if (remarks == null || remarks.equals(OFF)) {
                    simLockStatus = UNLOCKED;
                } else {
                    simLockStatus = LOCKED;
                }

                deviceInTracker.setSimLock(LOCKED.equalsIgnoreCase(simLockStatus));

                // If we get the unlocked carrier then display unlocked
                // else display the carrier name
                if (StringUtils.containsIgnoreCase(simLockStatus, UNLOCKED)) {
                    deviceInTracker.setCarrier(UNLOCKED);
                }


                // Notify UI
                final String topic = TopicBuilder.build(device, "simlock-check", "response");

                IosSimLockCheckResponseMessage message = new IosSimLockCheckResponseMessage();
                message.setId(device.getId());
                message.setSimLockResponse(rawResponse);
                message.setCarrier(deviceInTracker.getCarrier());
                message.setSimLocked(deviceInTracker.getSimLock());
                message.setErrorServerResponse(StringUtils.equalsAnyIgnoreCase(simLockStatus, SERVER_ERROR));
                publishToMqttTopic(topic, message);

                // Update sim lock status in database
                final SimLockInfoSuccessStage simLockInfoSuccessStage =
                        SimLockInfoSuccessStage.builder()
                                .id(device.getId())
                                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                                .simLockStatus(simLockStatus)
                                .simLockResponse(strJsonRawResponse)
                                .carrier(deviceInTracker.getCarrier())
                                .timestamp(System.currentTimeMillis())
                                .build();
                stageUpdater.updateStage(simLockInfoSuccessStage);

                if (deviceInTracker.getPreviouslyRanAutomation().containsKey(AutomationWorkflow.TEST_RESULTS) &&
                        deviceInTracker.getPreviouslyRanAutomation().get(AutomationWorkflow.TEST_RESULTS)
                                == AutomationWorkflowStatus.FAILED_REQUIRED_FIELDS && simLockStatus.equals(UNLOCKED)) {
                    LOGGER.info("Test result automation was failed due to required fields, " +
                            "Performing automation again.");
                    eventPublisher.publishEvent(new DeviceTestResultAutomationEvent(this, device));
                }

            } else {
                isLicenseExpired = rawResponse != null
                        && Boolean.TRUE.equals(rawResponse.getIsLicenseExpired());

                LOGGER.warn("Sim lock check license expired status : {}", isLicenseExpired);
                if (isLicenseExpired) {
                    notifyError(deviceInTracker, ErrorConstants.SIMLOCK_LICENSE_EXPIRED.getLocalizedKey());
                } else {
                    notifyError(deviceInTracker, ErrorConstants.SIMLOCK_CHECK_ERROR.getLocalizedKey());
                }
            }

        } catch (Exception e) {
            LOGGER.error("Exception occurred while checking simlock.", e);
            notifyError(deviceInTracker, ErrorConstants.SIMLOCK_CHECK_ERROR.getLocalizedKey());
        }
    }

}