package com.phonecheck.backend.listener.transaction;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudDeviceDataSyncService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceInfoDBService;
import com.phonecheck.model.event.transaction.TransactionDetailsEditEvent;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class TransactionDetailsEditListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(TransactionDetailsEditListener.class);

    private final DeviceInfoDBService deviceInfoDBService;
    private final CloudDeviceDataSyncService cloudDeviceDataSyncService;

    public TransactionDetailsEditListener(final IMqttAsyncClient mqttClient,
                                          final ObjectMapper objectMapper,
                                          final DeviceInfoDBService deviceInfoDBService,
                                          final CloudDeviceDataSyncService cloudDeviceDataSyncService) {
        super(mqttClient, objectMapper);
        this.deviceInfoDBService = deviceInfoDBService;
        this.cloudDeviceDataSyncService = cloudDeviceDataSyncService;
    }

    @EventListener
    public void onEvent(final TransactionDetailsEditEvent event) {
        LOGGER.info("Received event for edit transaction details: {}", event);
        deviceInfoDBService.updateDeviceTransactionInfo(event.getDevice(), event.getTransactionId());

        // if transaction was edited that was not a manual entry then there is a chance of overriding existing fields
        // in that case just update the editable fields values
        if (event.getDevice().isManualEntry()) {
            cloudDeviceDataSyncService.syncDeviceRecordToCloud(event.getDevice(), event.getTransaction());
        } else {
            cloudDeviceDataSyncService.updateTransactionOnCloud(event.getDevice(), event.getTransaction());
        }

    }
}
