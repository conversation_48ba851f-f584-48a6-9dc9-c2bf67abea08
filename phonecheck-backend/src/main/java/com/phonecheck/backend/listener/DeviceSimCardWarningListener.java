package com.phonecheck.backend.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.dao.service.DeviceTestResultDBService;
import com.phonecheck.info.android.AndroidDeviceInfoService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.DeviceSimCardWarningEvent;
import com.phonecheck.model.event.device.android.AndroidEraseRequestEvent;
import com.phonecheck.model.event.device.ios.IosEraseRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.DeviceSimCardDefectMessage;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.InitialDefectKey;
import com.phonecheck.model.util.TestResultsUtil;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DeviceSimCardWarningListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceSimCardWarningListener.class);
    private final IosDeviceInfoService iosDeviceInfoService;
    private final AndroidDeviceInfoService androidDeviceInfoService;
    private final DeviceTestResultDBService deviceTestResultDBService;
    private final InMemoryStore inMemoryStore;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final ApplicationEventPublisher eventPublisher;

    private static final int MAX_RETRIES = 5;

    protected DeviceSimCardWarningListener(final IMqttAsyncClient mqttClient,
                                           final ObjectMapper objectMapper,
                                           final IosDeviceInfoService iosDeviceInfoService,
                                           final AndroidDeviceInfoService androidDeviceInfoService,
                                           final DeviceTestResultDBService deviceTestResultDBService,
                                           final InMemoryStore inMemoryStore,
                                           final DeviceConnectionTracker deviceConnectionTracker,
                                           final ApplicationEventPublisher eventPublisher) {
        super(mqttClient, objectMapper);
        this.iosDeviceInfoService = iosDeviceInfoService;
        this.androidDeviceInfoService = androidDeviceInfoService;
        this.deviceTestResultDBService = deviceTestResultDBService;
        this.inMemoryStore = inMemoryStore;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.eventPublisher = eventPublisher;
    }

    @EventListener
    public void onEvent(final DeviceSimCardWarningEvent event) {
        Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Sim card warning action cannot be processed. " +
                    "Seems device has been disconnected, stopping processing");
            return;
        }

        boolean isSimCardDefectRequired = false;

        boolean isSimCardDetected;

        boolean failSimCardNotRemoved = inMemoryStore.getAssignedCloudCustomization()
                .getWorkflow().getWarningMessages().isFailSimCardNotRemoved();

        if (deviceInTracker instanceof AndroidDevice androidDevice) {
            isSimCardDetected = checkIfSimCardPresentInAndroidWithRetries(androidDevice);
            if (!isSimCardDetected && event.isEraseRequired()) {
                eventPublisher.publishEvent(new AndroidEraseRequestEvent(this, deviceInTracker));
            }
        } else if (deviceInTracker instanceof IosDevice iosDevice) {
            isSimCardDetected = iosDeviceInfoService.isSimCardCheckEnabledAndSimDetected(iosDevice);
            if (!isSimCardDetected && event.isEraseRequired()) {
                eventPublisher.publishEvent(new IosEraseRequestEvent(this, deviceInTracker));
            }
        } else {
            LOGGER.warn("Unknown device type, skipping SIM card check");
            return;
        }

        if (isSimCardDetected && failSimCardNotRemoved) {
            LOGGER.info("failSimCardNotRemoved is true, adding SIM card defect");
            DeviceSimCardDefectMessage defectMsg = new DeviceSimCardDefectMessage();
            defectMsg.setDeviceId(deviceInTracker.getId());
            defectMsg.setDefectToBeAdded(true);
            publishToMqttTopic(TopicBuilder.build(deviceInTracker, "sim-card", "defect"), defectMsg);
            isSimCardDefectRequired = true;
        } else {
            LOGGER.info("SIM card not found or failSimCardNotRemoved is false," +
                    " so SIM card defect not required in failed tests");
        }

        LOGGER.info("Defect list post sim-card action processing: {}", isSimCardDefectRequired);

        final List<String> failedTests = deviceInTracker.getDeviceTestResult().getTestResults().getFailed();
        if (isSimCardDefectRequired) {
            if (!failedTests.contains(InitialDefectKey.SIM_CARD_DETECTED.getKey())) {
                failedTests.add(InitialDefectKey.SIM_CARD_DETECTED.getKey());
            }
        } else {
            failedTests.remove(InitialDefectKey.SIM_CARD_DETECTED.getKey());
        }

        deviceTestResultDBService.updateFailedTests(
                String.valueOf(inMemoryStore.getTransaction().getTransactionId()),
                deviceInTracker.getSerial(),
                TestResultsUtil.listToCommaSeparatedString(failedTests)
        );
    }

    private boolean checkIfSimCardPresentInAndroidWithRetries(final AndroidDevice device) {
        boolean result = false;
        try {
            Thread.sleep(2000);

            int trial = 0;
            while (trial <= MAX_RETRIES && !result) {
                result = androidDeviceInfoService.isSimCardCheckEnabledAndSimDetected(device);

                trial++;
            }
        } catch (Exception e) {
            LOGGER.error("Error occurred while detecting sim for android phone", e);
        }

        return result;
    }
}
