package com.phonecheck.backend.listener.grading;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.phonecheck.api.cloud.GradingService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.listener.device.ResultsApiCallListener;
import com.phonecheck.model.cloudapi.GradingResponse;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.grading.GetActiveGradingRequestEvent;
import com.phonecheck.model.event.grading.SetGradingAnswersEvent;
import com.phonecheck.model.grading.GradingAnswers;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.GetFinalGradeResponseMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Listener class that handles grading-related request events.
 * This class extends {@link AbstractListener} and processes events asynchronously.
 */
@Component
public class GradingRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(ResultsApiCallListener.class);
    private static final String METHOD_NAME = "methodName";
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final GradingService gradingService;
    private final InMemoryStore inMemoryStore;

    public GradingRequestListener(final IMqttAsyncClient mqttClient,
                                  final ObjectMapper objectMapper,
                                  final DeviceConnectionTracker deviceConnectionTracker,
                                  final GradingService gradingService,
                                  final InMemoryStore inMemoryStore) {
        super(mqttClient, objectMapper);
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.gradingService = gradingService;
        this.inMemoryStore = inMemoryStore;
    }

    /**
     * Handles the {@link GetActiveGradingRequestEvent}.
     * This method is called asynchronously when the event is published.
     *
     * @param event the event containing the request details
     */
    @Async
    @EventListener
    public void onEvent(final GetActiveGradingRequestEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());
        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Get active grading but device has been disconnected, stopping processing.");
            return;
        }
        LOGGER.info("Get active grading request");
        String activeGrading = gradingService.getActiveGradingMethod(event.getProfileId(), inMemoryStore.getMasterId());
        LOGGER.info("Get active grading request response {}", activeGrading);
        setActiveGradingGrade(deviceInTracker, activeGrading, event.getSubGrades());
    }

    /**
     * Sets the active grading grade for a given device by encoding the grade hierarchy and sub-grades,
     * then retrieving and publishing the final grade through the grading service.
     *
     * @param deviceInTracker target device
     * @param activeGrading   the JSON string containing the active grading details, including the grade hierarchy
     * @param subGrades       a list of sub-grade strings associated with the active grading
     */
    private void setActiveGradingGrade(final Device deviceInTracker, final String activeGrading,
                                       final List<String> subGrades) {
        try {
            JSONObject activeStatusObject = new JSONObject(activeGrading);
            String methodName = activeStatusObject.getString(METHOD_NAME);
            GradingResponse.GradeData gradeData = new Gson().fromJson(activeGrading,
                    GradingResponse.GradeData.class);
            String afterEncodeGradeHierarchy = URLEncoder.encode(gradeData.getGradeHierarchy().toString(),
                            StandardCharsets.UTF_8)
                    .replaceAll("\\+", "%20")
                    .replaceAll("\\%21", "!")
                    .replaceAll("\\%27", "'")
                    .replaceAll("\\%28", "(")
                    .replaceAll("\\%29", ")")
                    .replaceAll("\\%7E", "~");
            JSONObject subGrade = new JSONObject();
            subGrade.put("subGrades", subGrades);
            String afterEncodeSubGrades = URLEncoder.encode(subGrade.toString(), StandardCharsets.UTF_8)
                    .replaceAll("\\+", "%20")
                    .replaceAll("\\%21", "!")
                    .replaceAll("\\%27", "'")
                    .replaceAll("\\%28", "(")
                    .replaceAll("\\%29", ")")
                    .replaceAll("\\%7E", "~");

            String finalGrade = gradingService.getFinalGrade(methodName, afterEncodeGradeHierarchy,
                    afterEncodeSubGrades);
            LOGGER.info("get final grade response {}", finalGrade);
            final String topic = TopicBuilder.build(deviceInTracker, "get", "final", "grade", "response");
            final GetFinalGradeResponseMessage requestMessage = new GetFinalGradeResponseMessage();
            requestMessage.setId(deviceInTracker.getId());
            requestMessage.setFinalGrade(finalGrade);
            publishToMqttTopic(topic, requestMessage);
        } catch (JSONException e) {
            LOGGER.info("Exception occurred while get set active grade", e);
        }
    }

    /**
     * Handles the {@link SetGradingAnswersEvent}.
     * This method is called asynchronously when the event is published.
     *
     * @param event the event containing the request details
     */
    @Async
    @EventListener
    public void onEvent(final SetGradingAnswersEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());
        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Set grading answers but device has been disconnected, stopping processing.");
            return;
        }
        LOGGER.info("Setting grading answers to the device now.");
        deviceInTracker.setGradingAnswers(event.getGradingAnswers());
        setGradingAnswers(event);
    }

    /**
     * Processes the grading answers from the event and sends them to the grading service.
     *
     * @param event the event containing the grading answers
     */
    private void setGradingAnswers(final SetGradingAnswersEvent event) {
        GradingAnswers gradingAnswers = new GradingAnswers();
        gradingAnswers.setLicenseID(inMemoryStore.getLicenseId());
        gradingAnswers.setTransactionID(inMemoryStore.getTransaction().getTransactionId());
        gradingAnswers.setSerial(event.getDeviceSerial());
        gradingAnswers.setMasterID(inMemoryStore.getMasterId());
        gradingAnswers.setModel(event.getDeviceModel());
        gradingAnswers.setUserName(inMemoryStore.getUserName());
        gradingAnswers.setGradingAnswers(event.getGradingAnswers());
        gradingService.gradingSystemAnswers(gradingAnswers);
    }
}