package com.phonecheck.backend.listener.device;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.cloud.CloudEsnCheckService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.util.DeviceUtil;
import com.phonecheck.backend.util.ESNUtil;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.EsnLicenseCheckRequest;
import com.phonecheck.model.constants.EsnFieldColor;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.customization.AutomationWorkflowStatus;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.stage.EsnInfoSuccessStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.AbstractDeviceEsnRequestEvent;
import com.phonecheck.model.event.device.AsyncEsnRequestEvent;
import com.phonecheck.model.event.device.DeviceTestResultAutomationEvent;
import com.phonecheck.model.event.device.SynchronousEsnRequestEvent;
import com.phonecheck.model.ios.EsnCheckType;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.EsnResponseMessage;
import com.phonecheck.model.service.EsnCheckInfo;
import com.phonecheck.model.status.EsnStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.TimerLoggerUtil;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Component
public class DeviceEsnCheckListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceEsnCheckListener.class);
    private final ObjectMapper objectMapper;
    private final CloudEsnCheckService esnCheckService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;
    private final ESNUtil esnUtil;
    private final DeviceStageUpdater stageUpdater;
    private final TimerLoggerUtil timerLoggerUtil;
    private final ApplicationEventPublisher eventPublisher;

    public DeviceEsnCheckListener(final CloudEsnCheckService esnCheckService,
                                  final IMqttAsyncClient mqttClient,
                                  final DeviceConnectionTracker deviceConnectionTracker,
                                  final ObjectMapper objectMapper,
                                  final InMemoryStore inMemoryStore,
                                  final ESNUtil esnUtil,
                                  final DeviceStageUpdater stageUpdater,
                                  final TimerLoggerUtil timerLoggerUtil,
                                  final ApplicationEventPublisher eventPublisher) {
        super(mqttClient, objectMapper);
        this.objectMapper = objectMapper;
        this.esnCheckService = esnCheckService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
        this.esnUtil = esnUtil;
        this.stageUpdater = stageUpdater;
        this.timerLoggerUtil = timerLoggerUtil;
        this.eventPublisher = eventPublisher;
    }

    /**
     * Handles device ESN request event
     *
     * @param event AsyncDeviceEsnRequestEvent
     */
    @Async
    @EventListener
    public void onEvent(final AsyncEsnRequestEvent event) {
        performEsnCheck(event);
    }

    /**
     * Only for synchronous ESN check for special cases
     * @param event SynchronousEsnRequestEvent
     */
    @EventListener
    public void onEvent(final SynchronousEsnRequestEvent event) {
        performEsnCheck(event);
    }

    /**
     * Perform ESN check
     * @param event
     */
    private void performEsnCheck(final AbstractDeviceEsnRequestEvent event) {
        final Device device = event.getDevice();
        setDeviceIdMDC(device.getId());

        final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("ESN requested but the device has been disconnected, stopping the process.");
            return;
        }

        if (DeviceUtil.isNonImeiDevice(deviceInTracker)) {
            LOGGER.info("ESN check is not performed for non IMEI device with device type: {}",
                    device.getDeviceType());
            return;
        }
        LocalDateTime esnStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(), "ESN start", esnStartTime);

        LOGGER.info("Initiate ESN retrieval request for the device");
        // if imei and serial values are same then we don't need to check ESN
        // because if check ESN by serial then it gives wrong response
        EsnCheckInfo esnCheckInfo = null;
        esnCheckInfo = getEsnInfoFromCloud(deviceInTracker, event.getEsnCheckType());

        if (esnCheckInfo == null) {
            LOGGER.info("ESN info retrieval not required, as no ESN customization was found.");
            return;
        }

        LOGGER.info("ESN api response {}", esnCheckInfo);
        timerLoggerUtil.printTimerLog(device.getId(), "ESN end", esnStartTime);

        try {
            deviceInTracker.setEsnRawResponse(objectMapper.writeValueAsString(esnCheckInfo));
            deviceInTracker.setUsInsuranceBlackListResponse(esnCheckInfo.getUsInsuranceBlackListInfo() != null ?
                    objectMapper.writeValueAsString(esnCheckInfo.getUsInsuranceBlackListInfo()) : null);
        } catch (JsonProcessingException e) {
            timerLoggerUtil.printTimerLog(device.getId(),
                    "Exception while converting ESN", esnStartTime);
            LOGGER.error("Exception occurred while converting EsnCheckInfo back to json.", e);
        }

        if (esnCheckInfo.getEsnApiResults() != null
                || esnCheckInfo.getUsInsuranceBlackListInfo() != null ||
        esnCheckInfo.getTracFoneStraightTalkAPIResponse() != null) {

            getOverallEsnStatus(esnCheckInfo, deviceInTracker);

            // Update esn status in database
            final EsnInfoSuccessStage esnInfoSuccessStage = EsnInfoSuccessStage.builder()
                    .id(device.getId())
                    .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                    .esnStatus(deviceInTracker.getEsnStatus())
                    .esnResponse(deviceInTracker.getEsnRawResponse())
                    .usInsuranceBlackListResponse(deviceInTracker.getUsInsuranceBlackListResponse())
                    .timestamp(System.currentTimeMillis())
                    .build();
            stageUpdater.updateStage(esnInfoSuccessStage);

            if (deviceInTracker.getPreviouslyRanAutomation().containsKey(AutomationWorkflow.TEST_RESULTS) &&
                    deviceInTracker.getPreviouslyRanAutomation().get(AutomationWorkflow.TEST_RESULTS)
                            == AutomationWorkflowStatus.FAILED_REQUIRED_FIELDS) {
                LOGGER.info("Test result automation was failed due to required fields, Performing automation again.");
                eventPublisher.publishEvent(new DeviceTestResultAutomationEvent(this, device));
            }

        } else {
            String errorMessage = esnCheckInfo.getEsnLicensesExpiredResponse() != null ?
                    "esnLicenseExpired" :
                    "esnCheckFailed";
            notifyError(device, errorMessage);
        }
    }

    /**
     * Method to check device ESN from cloud. It calculates the ESN check type based on user customizations and checks
     * ESN through cloud API, it returns null if ESN check fails
     *
     * @param device       target device
     * @param esnCheckType received EsnCheckType
     * @return EsnCheckInfo
     */
    private EsnCheckInfo getEsnInfoFromCloud(final Device device, final EsnCheckType esnCheckType) {
        String serviceId;
        boolean checkForAllCarriersRequired;
        boolean isCheckMendEnabled = inMemoryStore.isPersonalCheckMend();
        EsnCheckInfo blackListInfo = null;
        EsnCheckInfo tracfoneStraightTalkInfo = null;
        final CloudCustomizationResponse cloudCustomization = inMemoryStore.getAssignedCloudCustomization();
        EsnCheckType customisedEsnCheckType = null;

        if (esnCheckType.equals(EsnCheckType.ESN_CHECK_THROUGH_CUSTOMIZATION)) {
            customisedEsnCheckType = getCustomizedEsnCheckType(device);
        }

        switch (esnCheckType) {
            case ESN_CHECK, ESN_CHECK_BLACKLIST, ESN_CHECK_ALL -> { // Event raised from menu bar options
                serviceId = esnUtil.getESNServiceId(device.getCarrier(), esnCheckType);
                checkForAllCarriersRequired = esnCheckType == EsnCheckType.ESN_CHECK_ALL;
            }
            case ESN_CHECK_THROUGH_CUSTOMIZATION -> {
                if (cloudCustomization.getImeiCheck().isUsFinancialCarrierSetting() && customisedEsnCheckType != null) {
                    blackListInfo = esnCheckService.getEsnBlacklistInfo(device.getImei(), inMemoryStore.getUserToken());
                }
                if (customisedEsnCheckType != null) {
                    serviceId = esnUtil.getESNServiceId(device.getCarrier(), customisedEsnCheckType);
                    checkForAllCarriersRequired = customisedEsnCheckType == EsnCheckType.ESN_CHECK_ALL;
                } else {
                    return null;
                }
            }
            case ESN_CHECK_US_INSURANCE_BLACKLIST -> {
                return esnCheckService.getEsnBlacklistInfo(device.getImei(), inMemoryStore.getUserToken());
            }
            default -> {
                return null;
            }
        }

        if (esnCheckType == EsnCheckType.ESN_CHECK) {
            tracfoneStraightTalkInfo = getTrackFoneApIResp(cloudCustomization, device, EsnCheckType.ESN_CHECK);
        } else {
            tracfoneStraightTalkInfo = getTrackFoneApIResp(cloudCustomization, device, customisedEsnCheckType);
        }

        EsnCheckInfo esnCheckInfo = null;
        EsnLicenseCheckRequest esnRequest = EsnLicenseCheckRequest.builder()
                .imei(device.getImei())
                .imei2(device.getImei2())
                .serviceId(serviceId)
                .userId(inMemoryStore.getUserName())
                .testerId(inMemoryStore.getTesterId())
                .carrier(device.getCarrier())
                .serial(device.getSerial())
                .apiKey(inMemoryStore.getApiKey())
                .meid(device.getMeid())
                .isMeid(StringUtils.isBlank(device.getMeid()))
                .warehouseId(inMemoryStore.getWarehouseId())
                .deviceType(device.getDeviceType().toString())
                .checkAll(checkForAllCarriersRequired ? 1 : 0)
                .build();

        LOGGER.info("ESN api request isCheckMendEnabled: {} \nBody: {}", isCheckMendEnabled, esnRequest);

        esnCheckInfo = esnCheckService.getEsnInfo(esnRequest, isCheckMendEnabled);

        return EsnCheckInfo.builder()
                .esnApiResults(esnCheckInfo != null ? esnCheckInfo.getEsnApiResults() : null)
                .usInsuranceBlackListInfo(blackListInfo != null ?
                        blackListInfo.getUsInsuranceBlackListInfo() : null)
                .tracFoneStraightTalkAPIResponse(tracfoneStraightTalkInfo != null ?
                        tracfoneStraightTalkInfo.getTracFoneStraightTalkAPIResponse() : null)
                .build();
    }

    /**
     * Method to get customized ESN check type, user has selected custom configurations(CUSTOM) for esn/imei check
     *
     * @param device target device
     * @return EsnCheckType
     */
    private EsnCheckType getCustomizedEsnCheckType(final Device device) {
        final CloudCustomizationResponse cloudCustomization = inMemoryStore.getAssignedCloudCustomization();
        final CloudCustomizationResponse.ImeiCheckConfiguration imeiCheckConfiguration =
                cloudCustomization.getImeiCheck().getConfiguration();

        EsnCheckType esnCheckType;
        String requiredAction;

        if (imeiCheckConfiguration != null) {
            if (StringUtils.isNotBlank(device.getCarrier())) {
                if (StringUtils.containsIgnoreCase(device.getCarrier(), "unlocked")) {
                    requiredAction = imeiCheckConfiguration.getUnlockedAction();
                } else if (StringUtils.containsIgnoreCase(device.getCarrier(), "verizon")) {
                    requiredAction = imeiCheckConfiguration.getVerizonAction();
                } else if (StringUtils.containsIgnoreCase(device.getCarrier().replace("-", ""), "tmobile")) {
                    requiredAction = imeiCheckConfiguration.getTmobileAction();
                } else if (StringUtils.containsIgnoreCase(device.getCarrier().replace("&", ""), "att")) {
                    requiredAction = imeiCheckConfiguration.getAttAction();
                } else if (isCarrierTracFoneOrStraightTalk(device.getCarrier())) {
                    requiredAction = imeiCheckConfiguration.getTracfoneAction();
                } else { // Not Supported or unknown
                    requiredAction = imeiCheckConfiguration.getNotSupportedOrUnknownCarrierAction();
                }
            } else { // Not Supported or unknown
                requiredAction = imeiCheckConfiguration.getNotSupportedOrUnknownCarrierAction();
            }
        } else {
            requiredAction = "TAKE_NO_ACTION";
        }

        switch (requiredAction) {
            case "TAKE_NO_ACTION" -> {
                esnCheckType = null;
            }
            case "CHECK_GLOBAL_BLACKLIST_ONLY" -> {
                esnCheckType = EsnCheckType.ESN_CHECK_BLACKLIST;
            }
            case "CHECK_ALL_POSIBLE_DATABASES" -> {
                esnCheckType = EsnCheckType.ESN_CHECK_ALL;
            }
            default -> {
                esnCheckType = EsnCheckType.ESN_CHECK;
            }
        }

        return esnCheckType;
    }

    /**
     * Get the overall esn status for the device and send it to UI
     * Esn status = bad if response contains bad
     * Esn status = good if response contains good and no bad
     * Esn status = n/a if response is null or contains n/a and does not contain any bad or good
     *
     * @param esnApiResponses esn responses for different Apis
     * @param device          target device
     */
    private void getOverallEsnStatus(final EsnCheckInfo esnApiResponses,
                                     final Device device) {
        EsnStatus esnStatus = esnUtil.getESNStatus(esnApiResponses);
        EsnFieldColor esnFieldColor = esnUtil.getESNFieldColor(esnApiResponses);
        String esnUIResponse = esnUtil.getEsnContent(esnApiResponses);
        // save esn status in device tracker for further use e.g cloud sync device details
        device.setEsnStatus(esnStatus.getKey());

        // Notify UI
        final String topic = TopicBuilder.build(device, "esn", "response");

        EsnResponseMessage responseMessage = new EsnResponseMessage();
        responseMessage.setId(device.getId());
        responseMessage.setEsnStatus(esnStatus);
        responseMessage.setEsnFieldColor(esnFieldColor);
        responseMessage.setEsnUIResponse(esnUIResponse);
        publishToMqttTopic(topic, responseMessage);
    }

    /**
     * Get the TracFone Api response as an EsnCheckInfo obj
     * @param cloudCustomization
     * @param device
     * @param customisedEsnCheckType
     * @return EsnCheckInfo object with Tracfone resp in it
     */
    private EsnCheckInfo getTrackFoneApIResp(final CloudCustomizationResponse cloudCustomization,
                                             final Device device, final EsnCheckType customisedEsnCheckType) {
        if (cloudCustomization.getImeiCheck().isAutomaticIMEICheck() && customisedEsnCheckType != null &&
                (isCarrierTracFoneOrStraightTalk(device.getCarrier()))) {
            LOGGER.info("Getting {} carrier eligibility ", device.getCarrier());
            return esnCheckService.
                    getTracfoneStraightTalkEligibility(device.getImei(), inMemoryStore.getMasterToken());
        }
        return null;
    }

    /**
     * Checks if carrier is tracfone or straight talk
     * @param carrier
     * @return true if carrier is tracfone or straight talk
     */
    private boolean isCarrierTracFoneOrStraightTalk(final String carrier) {
        return StringUtils.isNotBlank(carrier) &&
                (StringUtils.containsIgnoreCase(carrier, "tracfone") ||
                StringUtils.containsIgnoreCase(carrier.replace(" ", ""),
                        "straighttalk"));
    }
}
