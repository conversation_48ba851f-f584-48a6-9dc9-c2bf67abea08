package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.dao.service.DeviceStageUpdater;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.stage.MountImageFailureStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosDiskImageMountFailureEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ios.IosDiskImageMountFailureMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Notifies broker that dev image mounting failed
 */
@Component
public class IosDiskImageMountFailureListener extends AbstractListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(IosDiskImageMountFailureListener.class);
    private final InMemoryStore inMemoryStore;
    private final DeviceStageUpdater deviceStageUpdater;
    private final DeviceConnectionTracker deviceConnectionTracker;

    public IosDiskImageMountFailureListener(final IMqttAsyncClient mqttClient, final ObjectMapper objectMapper,
                                            final InMemoryStore inMemoryStore,
                                            final DeviceStageUpdater deviceStageUpdater,
                                            final DeviceConnectionTracker deviceConnectionTracker) {
        super(mqttClient, objectMapper);
        this.inMemoryStore = inMemoryStore;
        this.deviceStageUpdater = deviceStageUpdater;
        this.deviceConnectionTracker = deviceConnectionTracker;
    }

    @EventListener
    public void onEvent(final IosDiskImageMountFailureEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Mount image failed but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("Disk image mount failed for IOS device");

        final String topic = TopicBuilder.build(device, "mount-image", "failure");

        // Notify UI
        final IosDiskImageMountFailureMessage message = new IosDiskImageMountFailureMessage();
        message.setId(device.getId());
        publishToMqttTopic(topic, message);

        MountImageFailureStage stage = MountImageFailureStage.builder()
                .id(device.getId())
                .transactionId(String.valueOf(inMemoryStore.getTransaction().getTransactionId()))
                .timestamp(System.currentTimeMillis())
                .build();

        // update database and in memory cache with the latest device state
        LOGGER.debug("Updating device to stage {} in data store", stage.getStage().name());
        deviceStageUpdater.updateStage(stage);

        deviceInTracker.setStage(DeviceStage.DEV_IMAGE_MOUNT_FAILED);
    }
}