package com.phonecheck.backend.listener.deviceoperations;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.device.exportlogs.BulkExportLogsService;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.deviceoperations.BulkExportLogsRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.BulkExportLogsResponseMessage;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class BulkExportLogsListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(BulkExportLogsListener.class);
    private final BulkExportLogsService bulkExportLogsService;

    public BulkExportLogsListener(final ObjectMapper objectMapper,
                                  final IMqttAsyncClient mqttClient,
                                  final BulkExportLogsService bulkExportLogsService) {
        super(mqttClient, objectMapper);
        this.bulkExportLogsService = bulkExportLogsService;
    }

    /**
     * Event from UI to change export logs of selected devices
     *
     * @param event DeviceExportLogsEvent from UI
     */
    @Async
    @EventListener
    public void onEvent(final BulkExportLogsRequestEvent event) {
        final List<Device> listOfSelectedDevices = event.getListOfSelectedDevices();
        bulkExportLogsService.exportLogs(listOfSelectedDevices);
        String topic = TopicBuilder.buildGenericTopic("export-logs", "response");
        publishToMqttTopic(topic, new BulkExportLogsResponseMessage());
    }
}