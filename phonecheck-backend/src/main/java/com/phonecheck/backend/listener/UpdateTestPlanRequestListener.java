package com.phonecheck.backend.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.device.results.file.push.TestPlanService;
import com.phonecheck.model.event.customization.UpdateTestPlanRequestEvent;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class UpdateTestPlanRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpdateTestPlanRequestListener.class);
    private final InMemoryStore inMemoryStore;
    private final TestPlanService testPlanService;


    protected UpdateTestPlanRequestListener(final IMqttAsyncClient mqttClient,
                                            final ObjectMapper objectMapper,
                                            final InMemoryStore inMemoryStore,
                                            final TestPlanService testPlanService) {
        super(mqttClient, objectMapper);
        this.inMemoryStore = inMemoryStore;
        this.testPlanService = testPlanService;
    }

    @Async
    @EventListener
    public void onEvent(final UpdateTestPlanRequestEvent event) {
        inMemoryStore.setDeviceConnectionMode(event.getDeviceConnectionMode());
        // recreate test plan file on mode switching
        try {
            testPlanService.createTestPlanFiles(inMemoryStore.getAssignedCloudCustomization());
        } catch (final IOException e) {
            LOGGER.error("Failed to update test plan files", e);
        }
    }
}
