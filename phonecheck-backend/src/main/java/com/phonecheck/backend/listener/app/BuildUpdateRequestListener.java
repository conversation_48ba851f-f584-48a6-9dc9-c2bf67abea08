package com.phonecheck.backend.listener.app;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.service.OtaService;
import com.phonecheck.model.event.BuildUpdateRequestEvent;
import com.phonecheck.model.mqtt.helper.TopicBuilder;
import com.phonecheck.model.mqtt.messages.ExitApplicationMessage;
import com.phonecheck.model.store.InMemoryStore;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class BuildUpdateRequestListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(BuildUpdateRequestListener.class);
    private final OtaService otaService;
    private final InMemoryStore inMemoryStore;

    public BuildUpdateRequestListener(final IMqttAsyncClient mqttClient,
                                      final ObjectMapper objectMapper,
                                      final OtaService otaService,
                                      final InMemoryStore inMemoryStore) {
        super(mqttClient, objectMapper);
        this.otaService = otaService;
        this.inMemoryStore = inMemoryStore;
    }

    @Async
    @EventListener
    public void onEvent(final BuildUpdateRequestEvent event) {
        LOGGER.info("Build update request received.");
        otaService.downloadUpdates(inMemoryStore.getBuildUpdateFileUrl());
        if (!inMemoryStore.isResumeProcessingForOta()) {
            try {
                Thread.sleep(2000);
                publishToMqttTopic(TopicBuilder.buildGenericTopic("ota", "exit", "application"),
                        new ExitApplicationMessage());
            } catch (InterruptedException e) {
                //nothing to do
            }
        }
    }
}