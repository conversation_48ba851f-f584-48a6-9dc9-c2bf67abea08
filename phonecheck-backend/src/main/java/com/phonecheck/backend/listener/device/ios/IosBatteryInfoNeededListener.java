package com.phonecheck.backend.listener.device.ios;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.app.profile.ConfigProfileService;
import com.phonecheck.backend.listener.AbstractListener;
import com.phonecheck.backend.util.pairing.IosPairingUtil;
import com.phonecheck.dao.service.lookup.BatteryInfoDBLookupService;
import com.phonecheck.device.results.IosTestResultsService;
import com.phonecheck.info.DeviceTestPlanUtil;
import com.phonecheck.info.ios.IosBatteryInfoService;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.battery.BatterySource;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.DeviceType;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.ios.IosBatteryInfoFailureEvent;
import com.phonecheck.model.event.device.ios.IosBatteryInfoNeededEvent;
import com.phonecheck.model.event.device.ios.IosBatteryInfoSuccessEvent;
import com.phonecheck.model.ios.IosConfigProfile;
import com.phonecheck.model.ios.ProcessName;
import com.phonecheck.model.status.MountStatus;
import com.phonecheck.model.status.PairStatus;
import com.phonecheck.model.status.SetupDoneStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.InitialDefectKey;
import com.phonecheck.model.util.TimerLoggerUtil;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

/**
 * Initiates Battery info data collection
 */
@Component
public class IosBatteryInfoNeededListener extends AbstractListener {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosBatteryInfoNeededListener.class);
    private static final int RETRIES_FOR_BATTERY_STATE_HEALTH = 5;
    private final ApplicationEventPublisher eventPublisher;
    private final ConfigProfileService configProfileService;
    private final BatteryInfoDBLookupService batteryInfoDBLookupService;
    private final IosBatteryInfoService iosBatteryInfoService;
    private final IosDeviceInfoService iosDeviceInfoService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;
    private final TimerLoggerUtil timerLoggerUtil;
    private final IosPairingUtil iosPairingUtil;
    private final IosTestResultsService iosTestResultsService;
    private final DeviceTestPlanUtil deviceTestPlanUtil;

    public IosBatteryInfoNeededListener(final IMqttAsyncClient mqttClient,
                                        final ObjectMapper objectMapper,
                                        final ApplicationEventPublisher eventPublisher,
                                        final ConfigProfileService configProfileService,
                                        final BatteryInfoDBLookupService batteryInfoDBLookupService,
                                        final IosBatteryInfoService iosBatteryInfoService,
                                        final IosDeviceInfoService iosDeviceInfoService,
                                        final DeviceConnectionTracker deviceConnectionTracker,
                                        final InMemoryStore inMemoryStore,
                                        final TimerLoggerUtil timerLoggerUtil,
                                        final IosPairingUtil iosPairingUtil,
                                        final IosTestResultsService iosTestResultsService,
                                        final DeviceTestPlanUtil deviceTestPlanUtil) {
        super(mqttClient, objectMapper);
        this.eventPublisher = eventPublisher;
        this.configProfileService = configProfileService;
        this.batteryInfoDBLookupService = batteryInfoDBLookupService;
        this.iosBatteryInfoService = iosBatteryInfoService;
        this.iosDeviceInfoService = iosDeviceInfoService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
        this.timerLoggerUtil = timerLoggerUtil;
        this.iosPairingUtil = iosPairingUtil;
        this.iosTestResultsService = iosTestResultsService;
        this.deviceTestPlanUtil = deviceTestPlanUtil;
    }


    @EventListener
    public void onEvent(final IosBatteryInfoNeededEvent event) {
        final IosDevice device = (IosDevice) event.getDevice();
        setDeviceIdMDC(device.getId());

        final IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
        if (deviceInTracker == null) {
            LOGGER.warn("Battery info collection needed but device has been disconnected, stopping processing.");
            return;
        }

        LOGGER.info("Attempting to retrieve battery info.");

        LocalDateTime batteryStartTime = LocalDateTime.now();
        timerLoggerUtil.printTimerLog(device.getId(), "Battery info collection start", batteryStartTime);

        // By default
        BatterySource batterySource = BatterySource.OM_DIAGNOSTIC;

        // For devices with os below IOS 11.3 other than iPhone i.e. iPods and iPads we can get correct battery health
        // from diagnostics so, we don't need to try retrieving it from Syslog
        if (deviceInTracker.getOsMajorVersion() >= 11.3 && deviceInTracker.getDeviceType() == DeviceType.IPHONE) {
            if (deviceInTracker.getSetupDoneStatus() == SetupDoneStatus.NOT_DONE) { // Hello Screen
                // Push BatteryLife.mobile-config profile in device to get battery health data from syslog
                if (pushBatteryProfile(deviceInTracker)) {
                    batterySource = BatterySource.PUSHING_PROFILE;
                }
            } else if (deviceInTracker.getSetupDoneStatus() == SetupDoneStatus.DONE &&
                    deviceInTracker.getImageMountStatus() == MountStatus.SUCCESS) { // Home Screen
                // Kill powerD process in device to get battery health data from syslog
                if (killPowerProcess(deviceInTracker)) {
                    batterySource = BatterySource.KILLING_POWER_D;
                }
            } else {
                LOGGER.warn("SetupDone Status: {}, Image mount Status: {}",
                        deviceInTracker.getSetupDoneStatus(), deviceInTracker.getImageMountStatus());
            }
        }
        if (deviceInTracker.isRestartedFor16() && deviceInTracker.getDeviceType() == DeviceType.IPHONE &&
                deviceInTracker.getBatteryStateHealth() == 0 && deviceInTracker.getPreviousBatteryStateHealth() == 0) {
            LOGGER.info("Device is restarted and both current and previous battery health are 0.");
            batterySource = BatterySource.KILLING_POWER_D;
            waitForBatteryInfoUpdateOnRestartedIphone(deviceInTracker);
            if (deviceInTracker.getBatteryStateHealth() == 0 && deviceInTracker.getPreviousBatteryStateHealth() == 0) {
                LOGGER.info("Battery health values still 0. Updated batterySource to OM_DIAGNOSTIC.");
                batterySource = BatterySource.OM_DIAGNOSTIC;
            }
        }

        BatteryInfo batteryInfo = null;
        try {
            // Retrieve Battery info from iDeviceDiagnostic exe
            // We will calculate it every time
            batteryInfo = iosBatteryInfoService.getBatteryInfoViaIoReg(deviceInTracker);

            if (batteryInfo == null) {
                LOGGER.warn("Lockdown error encountered during battery info retrieval for device");
                if (!device.isPairAttempted()) {
                    device.setPairAttempted(true);
                    PairStatus pairStatus = iosPairingUtil.checkAndNotifyUiIfNotPaired(device);
                    if (PairStatus.PAIRED.equals(pairStatus)) {
                        batteryInfo = iosBatteryInfoService.getBatteryInfoViaIoReg(deviceInTracker);
                        if (batteryInfo == null) {
                            LOGGER.info("Device requires reconnect due to lockdown error ");
                            if (deviceInTracker.isEraseInProgress()) {
                                LOGGER.warn("Device is currently erasing, skipping to show reconnect popup on UI");
                                return;
                            }
                            iosPairingUtil.showReconnectPopup(device);
                        }
                    } else {
                        if (deviceInTracker.isEraseInProgress()) {
                            LOGGER.warn("Device is currently erasing, skipping to show reconnect popup on UI");
                            return;
                        }
                        LOGGER.info("Device pairing failed, reconnect required");
                        iosPairingUtil.showReconnectPopup(device);
                    }
                } else {
                    LOGGER.info("Device already attempted pairing, reconnect required");
                    iosPairingUtil.showReconnectPopup(device);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while loading battery info from IO registry.", e);
        }

        updateChargingStatus(batteryInfo, device);

        if (deviceInTracker.getOsMajorVersion() >= 11.3 && deviceInTracker.getDeviceType() == DeviceType.IPHONE) {
            if (batterySource == BatterySource.OM_DIAGNOSTIC) {
                // If push profile or power-d not executed,
                // then retrieve data from DB and set only source and percentage from DB
                BatteryInfo dbBatteryInfo = batteryInfoDBLookupService.getBatteryInfoFromDatabase(
                        inMemoryStore.getTransaction().getTransactionId(),
                        deviceInTracker.getId());

                // Retrieve battery source from DB
                if (dbBatteryInfo != null && dbBatteryInfo.getSource() != null) {
                    batterySource = dbBatteryInfo.getSource();
                }
                // Retrieve battery health percentage from DB if it's not 0
                if (dbBatteryInfo != null && batteryInfo != null &&
                        dbBatteryInfo.getHealthPercentage() != 0) {
                    batteryInfo.setHealthPercentage(dbBatteryInfo.getHealthPercentage());
                }

            } else {
                // if Push profile or power-d executed
                // Get battery percentage from sysLogs and set in device info
                int batteryStateHealth = deviceInTracker.getBatteryStateHealth() != 0
                        ? deviceInTracker.getBatteryStateHealth()
                        : deviceInTracker.getPreviousBatteryStateHealth();

                // Update battery info which we retrieve from iDeviceDiagnostic
                // This will only update if device is IPhone
                if (batteryStateHealth != 0 && batteryInfo != null) {
                    batteryInfo.setHealthPercentage(batteryStateHealth);
                }
            }
        }

        // Set battery source into battery info and set battery info into device
        if (batteryInfo != null) {
            batteryInfo.setSource(batterySource);
            deviceInTracker.setBatteryInfo(batteryInfo);
        }

        // Publish event
        // In case of failure : if device erase in progress or booting in progress, no need to notify to UI
        if (deviceInTracker.getBatteryInfo() != null && deviceInTracker.getBatteryInfo().getHealthPercentage() != 0) {
            timerLoggerUtil.printTimerLog(device.getId(), "Battery info collection end", batteryStartTime);
            eventPublisher.publishEvent(new IosBatteryInfoSuccessEvent(this, deviceInTracker));
        } else if (deviceInTracker.getIsBootCompleted() == null
                || Boolean.TRUE.equals(deviceInTracker.getIsBootCompleted())) {
            timerLoggerUtil.printTimerLog(device.getId(), "Battery info collection Failed", batteryStartTime);
            eventPublisher.publishEvent(new IosBatteryInfoFailureEvent(this, deviceInTracker));
        }
    }

    /**
     * Push battery profile
     *
     * @param device target device
     * @return status
     */
    private boolean pushBatteryProfile(final IosDevice device) {
        int pushBatteryProfileCounter = 0;

        do {
            try {
                File batteryProfileFile = iosBatteryInfoService.getBatteryLifeProfileFile();
                if (batteryProfileFile.exists()) {
                    if (Boolean.TRUE.equals(configProfileService
                            .iosPushProfile(device, batteryProfileFile, IosConfigProfile.BATTERY))) {
                        LOGGER.info("Battery life configuration profile was installed on the device");

                        // Wait for 5 seconds after pushing battery profile to load battery health from syslog
                        try {
                            Thread.sleep(5000);
                        } catch (InterruptedException e) {
                            // do nothing
                        }

                        if (device.getBatteryStateHealth() != 0 || device.getPreviousBatteryStateHealth() != 0) {
                            LOGGER.info("Battery Health successfully parsed from Syslog, Battery Health: {}",
                                    device.getBatteryStateHealth() != 0
                                            ? device.getBatteryStateHealth()
                                            : device.getPreviousBatteryStateHealth());
                            return true;
                        }

                        // Remove already pushed battery profile
                        if (configProfileService.removeProfile(device, IosConfigProfile.BATTERY.getId())) {
                            LOGGER.info("Battery life configuration profile removed from the device.");
                        }
                    } else {
                        LOGGER.warn("Battery life configuration profile failed to push.");
                    }
                } else {
                    LOGGER.warn("Battery life configuration not set for the session! Won't push battery profile.");
                    break;
                }
            } catch (Exception e) {
                LOGGER.error("Exception occurred while getting battery profile file.", e);
            }

            pushBatteryProfileCounter++;
        } while (pushBatteryProfileCounter < RETRIES_FOR_BATTERY_STATE_HEALTH);

        return false;
    }

    /**
     * Kills powerD process
     *
     * @param device target device
     * @return status
     */
    private boolean killPowerProcess(final IosDevice device) {
        int killPowerProcessCounter = 0;

        do {
            try {
                // Get the process id for powerD
                Map<ProcessName, String> processNameIdMap = iosDeviceInfoService.getDeviceProcessIds(device,
                        ProcessName.POWERD);

                // Kill the processes
                if (processNameIdMap != null && processNameIdMap.containsKey(ProcessName.POWERD)) {
                    iosDeviceInfoService.killProcess(device, processNameIdMap.get(ProcessName.POWERD));
                    LOGGER.info("Killed powerD process from the device.");
                }
            } catch (IOException e) {
                LOGGER.error("Exception occurred while killing powerD process from the device", e);
            }

            // Wait for 5 seconds after killing powerD process to load battery health from syslog
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                // do nothing
            }

            if (device.getBatteryStateHealth() != 0 || device.getPreviousBatteryStateHealth() != 0) {
                LOGGER.info("Battery Health successfully parsed from Syslog, Battery Health: {}",
                        device.getBatteryStateHealth() != 0
                                ? device.getBatteryStateHealth()
                                : device.getPreviousBatteryStateHealth());
                return true;
            }

            killPowerProcessCounter++;
        } while (killPowerProcessCounter < RETRIES_FOR_BATTERY_STATE_HEALTH);

        return false;
    }

    /**
     * Removes the charging status from the battery info if it is not needed.
     * Logs messages based on different conditions:
     * - If battery info is null, logs a message and returns.
     * - If the device is in PreCheck mode, logs a message and returns.
     * - If the charging test key is not in the test plan, sets charging status to null.
     * - Otherwise, logs that charging status should be added on the UI.
     *
     * @param batteryInfo the battery information to update
     * @param device      the iOS device associated with the battery info
     */
    private void updateChargingStatus(final BatteryInfo batteryInfo, final IosDevice device) {
        if (batteryInfo == null) {
            LOGGER.info("Found battery info is null, while updating charging status in battery info");
            return;
        }

        if (DeviceConnectionMode.PRE_CHECK == inMemoryStore.getDeviceConnectionMode()) {
            LOGGER.info("Test plan not supported in PreCheck mode, add charging status Pass/Fail on UI");
            return;
        }

        if (deviceTestPlanUtil.isChargingKeyExistInTestPlan(device)) {
            LOGGER.info("Charging test key not found in test plan in default mode, no need to add on UI");
            batteryInfo.setIsCharging(null);
            //delete the test key from passed / failed test results.
            Optional.ofNullable(device)
                    .map(IosDevice::getDeviceTestResult)
                    .map(DeviceTestResult::getTestResults)
                    .ifPresent(testResults -> {
                        Optional.ofNullable(testResults.getPassed())
                                .ifPresent(passed -> passed.remove(InitialDefectKey.BATTERY_CHARGING_STATUS));

                        Optional.ofNullable(testResults.getFailed())
                                .ifPresent(failed -> failed.remove(InitialDefectKey.BATTERY_CHARGING_STATUS));
                    });

        } else {
            LOGGER.info("Charging test found in test plan in default mode, add charging status Pass/Fail on UI");
            iosTestResultsService.updateBatteryChargingTestInPassFailResult(device);
        }
    }

    /**
     * Waits for battery to be received when device restarted
     *
     * @param device target device
     */
    private void waitForBatteryInfoUpdateOnRestartedIphone(final IosDevice device) {
        int batteryHealthRetryCounter = 0;
        do {
            if (device.getBatteryStateHealth() != 0 || device.getPreviousBatteryStateHealth() != 0) {
                LOGGER.info("Battery Health successfully parsed from Syslog, Battery Health: {}",
                        device.getBatteryStateHealth() != 0
                                ? device.getBatteryStateHealth()
                                : device.getPreviousBatteryStateHealth());
                return;
            }
            // Wait for 5 seconds
            LOGGER.info("Battery health not received yet, retry count {}", batteryHealthRetryCounter);
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                // do nothing
            }
            batteryHealthRetryCounter++;
        } while (batteryHealthRetryCounter < RETRIES_FOR_BATTERY_STATE_HEALTH);
    }
}
