package com.phonecheck.labelmakercheck.utility;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Component
public class DeviceTestResultsLabelConfigUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceTestResultsLabelConfigUtil.class);
    private List<Map<String, Object>> columnsWidth;
    private double fontSize;
    private double imageHeight;
    private double imageWidth;
    private int hBoxWidth;

    /**
     * Initializes the data for different canvas sizes and their corresponding column widths.
     */
    public void initializeCanvasData() {
        columnsWidth = new ArrayList<>();
        addCanvasColumnWidthData("57x32mm", 110);
        addCanvasColumnWidthData("58x43mm", 110);
        addCanvasColumnWidthData("62x29mm", 110);
        addCanvasColumnWidthData("36x89mm", 110);
        addCanvasColumnWidthData("59.9x38mm", 110);
        addCanvasColumnWidthData("101x62mm", 108);
        addCanvasColumnWidthData("95x55mm", 108);
    }

    /**
     * Adds data for a specific canvas size and column width.
     *
     * @param canvas      the canvas size as a string (e.g., "3.5x1.5")
     * @param columnWidth the width of the column for the given canvas size
     */
    private void addCanvasColumnWidthData(final String canvas, final int columnWidth) {
        Map<String, Object> data = new HashMap<>();
        data.put("canvasSize", canvas);
        data.put("columnWidth", columnWidth);
        columnsWidth.add(data);
    }

    /**
     * Retrieves the column width for a given key from the data list.
     *
     * @param data the list of data maps
     * @param key  the key to search for
     * @return the column width if found, otherwise null
     */
    public Object getDataByKey(final List<Map<String, Object>> data, final String key) {
        for (Map<String, Object> item : data) {
            if (item.containsKey("canvasSize")) {
                if (item.get("canvasSize").equals(key)) {
                    return item.get("columnWidth");
                }
            }
        }
        return null;
    }

    /**
     * Adjusts the font size based on the flow pane width and the specified canvas size.
     *
     * @param flowPaneWidth the width of the flow pane
     * @param canvas        the canvas size as a string (e.g., "3.5x1.5")
     */
    public void adjustFontSize(final double flowPaneWidth, final String canvas) {
        String[] dimensionArray = canvas.split("x");
        if (StringUtils.isNotBlank(canvas)) {
            float width = Float.parseFloat(dimensionArray[0]);
            String height = dimensionArray[1];
            initializeCanvasData();
            hBoxWidth = getColumnWidth(width, height, canvas);
        }

        int numberOfHBoxes = (int) (flowPaneWidth / hBoxWidth);

        double availableWidthPerHBox = flowPaneWidth / numberOfHBoxes;
        fontSize = availableWidthPerHBox / 12;

        imageWidth = fontSize * 1.5;
        imageHeight = fontSize * 1.5;
    }

    /**
     * Retrieves the column width based on the canvas dimensions.
     *
     * @param width  the width of the canvas
     * @param height the height of the canvas as a string
     * @param canvas the canvas size as a string (e.g., "3.5x1.5")
     * @return the column width for the given dimensions
     */
    private int getColumnWidth(final float width, final String height, final String canvas) {
        if (height.toLowerCase().endsWith("mm")) {
            return (int) getDataByKey(getColumnsWidth(), canvas);
        } else if (width == 2) {
            return 110;
        } else if (width == 3 || width == 3.5) {
            return 110;
        } else if (width == 4) {
            return 108;
        } else {
            LOGGER.info("Condition not met for height: {}, width: {}, canvas: {}", height, width, canvas);
            return 0;
        }
    }
}
