server.port=9090

# application properties
applicationName=LabelMakerCheck
applicationVersion=1.0.0

# logging configurations
logging.level.com.phonecheck=INFO
logging.path=${user.home}/Library/Application Support/PhoneCheck3/logs

# spring configurations
spring.main.lazy-initialization=true
spring.main.web-application-type=servlet
spring.jmx.enabled=false
spring.task.execution.pool.core-size=64
spring.task.execution.pool.max-size=1024
spring.task.execution.pool.queue-capacity=1024
spring.task.execution.thread-name-prefix=ExecutorThread-
server.tomcat.max-threads=1024

spring.datasource.url=jdbc:h2:${user.home}/Library/Application Support/PhoneCheck3/db/data;AUTO_SERVER=TRUE;LOCK_TIMEOUT=4000
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=Ph0neCheck!
spring.sql.init.mode=never
spring.datasource.hikari.maximum-pool-size=50
spring.h2.console.enabled=false

# api endpoints
cloud.service.base.url=https://cloudportal.phonecheck.com/cloud/
eu.cloud.service.base.url=https://eu-cloudportal.phonecheck.com/
cloud.phonecheck.service.base.url=https://cloudportal.phonecheck.com/phone-check/
eu.cloud.phonecheck.service.base.url=https://eu-cloudportal.phonecheck.com/phone-check/
phonecheck.api.base.url=https://api.phonecheck.com/v2/
eu.phonecheck.api.base.url=https://eu-api.phonecheck.com/v2/
brightstar.phonecheck.api.base.url=http://brightstar.phonecheck.com/api/
cloud.vpp.base.url=https://vpp.itunes.apple.com/mdm/
japanese.telephony.url=https://www.tele.soumu.go.jp/giteki/
rest.template.timeout=60000