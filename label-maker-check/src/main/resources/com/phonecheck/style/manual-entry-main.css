.root {
    -fx-background-radius: 10;
    -fx-background-color: transparent;
    -fx-border-radius: 10;
    -fx-border-color: gray;
    -fx-border-width: 0;
    -fx-effect: dropshadow(gaussian, grey, 10, 0, 0, 0);
}
.label-bold-text {
    -fx-font-size: 15px;
    -fx-font-family: "Arial";
    -fx-font-weight: bold
 }
 .scroll-pane .scroll-bar:horizontal {
     -fx-opacity: 0;
     -fx-scaling: 0;
 }
 .button-bold-text:pressed {
     -fx-background-color: #C1C2C4;
 }

.combo-box {
    -fx-background-color: transparent;
}

.combo-box .arrow-button {
    -fx-background-color: transparent;
    -fx-background-insets: 0;
}

.combo-box .arrow {
    -fx-background-color: black;
}

.combo-box .list-cell {
    -fx-background-color: white;
}

.combo-box .list-cell:hover {
    -fx-background-color: rgb(83, 120, 169);
    -fx-text-fill: white;
}

.combo-box .text-field {
    -fx-background-color: transparent;
}

.combo-box .text-field .prompt-text {
    -fx-background-color: transparent;
}

.combo-box .text-field .content {
    -fx-background-color: transparent;
}

.combo-box .list-view .list-cell:selected {
    -fx-background-color: rgb(83, 120, 169);
}

#submitButtonManualEntry {
    -fx-text-fill: white;
    -fx-border-radius: 5;
    -fx-background-color: #007AFF;
}

#submitButtonManualEntry .disabled {
    -fx-text-fill: black;
    -fx-border-color: grey;
    -fx-border-radius: 5;
    -fx-background-color: transparent;
}
