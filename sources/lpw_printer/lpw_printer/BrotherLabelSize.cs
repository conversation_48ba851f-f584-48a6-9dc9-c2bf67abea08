using System.Collections.Generic;
using System.Linq;
using static lpw_printer.LabelSizeHelper;

namespace lpw_printer
{
    /// <summary>
    /// Represents Brother label sizes with orientation and dimensions for specific label types.
    /// </summary>
    internal class BrotherLabelSize
    {
        /// <summary>
        /// A collection of predefined Brother label sizes.
        /// </summary>
        private static readonly List<BrotherLabelSize> AllSizes = new List<BrotherLabelSize>
        {
            new BrotherLabelSize("1x2 or 2x1", "54mm x 29mm", "29mm"),
            new BrotherLabelSize("1x3 or 3x1", "29mm x 90mm", "29mm"),
            new BrotherLabelSize("1.25x4 or 4x1.25", "38mm x 90mm", "38mm"),
            new BrotherLabelSize("1.5x3.5 or 3.5x1.5", "38mm x 90mm", "38mm"),
            new BrotherLabelSize("2x3 or 3x2", "62mm x 75mm", "62mm"),
            new BrotherLabelSize("2x4 or 4x2", "62mm x 100mm", "62mm"),
            new BrotherLabelSize("3x4 or 4x3", "38mm x 2", "38mm x 2"),
            new BrotherLabelSize("4x6 or 6x4", "102mm x 152mm", "102mm"),

            new BrotherLabelSize("29x62 or 62x29", "62mm x 29mm", "29mm"),
            new BrotherLabelSize("32x57 or 57x32", "62mm", "38mm"),
            new BrotherLabelSize("36x89 or 89x36", "38mm x 90mm", "38mm"),
            new BrotherLabelSize("38x59.9 or 59.9x38", "62mm", "38mm"),
            new BrotherLabelSize("54x70 or 70x54", "29mm x 2", "54mm"),
            new BrotherLabelSize("55x95 or 95x55", "62mm x 100mm", "62mm"),
            new BrotherLabelSize("62x101 or 101x62", "62mm x 100mm", "62mm"),
            new BrotherLabelSize("70x150 or 150x70", "50mm x 3", "38mm x 2"),
        };

        /// <summary>
        /// Gets the label size dimensions.
        /// </summary>
        public string Size { get; private set; }

        /// <summary>
        /// Gets the display label name for the label size.
        /// </summary>
        public string LabelName { get; private set; }

        /// <summary>
        /// Gets the display tape name for the label size.
        /// </summary>
        public string TapeName { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="BrotherLabelSize"/> class with specified size, name, and orientation.
        /// </summary>
        /// <param name="size">The size of the label in dimensions (WxH).</param>
        /// <param name="labelName">The display label name of the label size.</param>
        /// <param name="tapeName">The display tape name of the label size.</param>
        private BrotherLabelSize(string size, string labelName, string tapeName)
        {
            Size = size;
            LabelName = labelName;
            TapeName = tapeName;
        }

        /// <summary>
        /// Finds and returns a Brother label size based on the provided size string.
        /// </summary>
        /// <param name="size">Size string to search for.</param>
        /// <returns>A <see cref="BrotherLabelSize"/> matching the provided size, or null if no match is found.</returns>
        public static BrotherLabelSize FindBySize(string size)
        {
            return AllSizes.FirstOrDefault(labelSize => labelSize.Size.Contains(size));
        }
    }
}
