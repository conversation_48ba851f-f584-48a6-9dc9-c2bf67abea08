<doc>
    <assembly>
        <name>SkiaSharp.Views.WPF</name>
    </assembly>
    <members>
        <member name="T:SkiaSharp.Views.WPF.SKElement">
            <summary>A visual element that can be drawn on using SkiaSharp drawing commands.</summary>
            <remarks />
        </member>
        <member name="C:SkiaSharp.Views.WPF.SKElement">
            <summary>Creates a new instance of the <see cref="T:SkiaSharp.Views.WPF.SKElement" /> view.</summary>
            <remarks />
        </member>
        <member name="P:SkiaSharp.Views.WPF.SKElement.CanvasSize">
            <summary>Gets the current canvas size.</summary>
            <value />
            <remarks>The canvas size may be different to the view size as a result of the current device's pixel density.</remarks>
        </member>
        <member name="P:SkiaSharp.Views.WPF.SKElement.IgnorePixelScaling">
            <summary>Gets or sets a value indicating whether the drawing canvas should be resized on high resolution displays.</summary>
            <value />
            <remarks>By default, when false, the canvas is resized to 1 canvas pixel per display pixel. When true, the canvas is resized to device independent pixels, and then stretched to fill the view. Although performance is improved and all objects are the same size on different display densities, blurring and pixelation may occur.</remarks>
        </member>
        <member name="M:SkiaSharp.Views.WPF.SKElement.OnPaintSurface(SkiaSharp.Views.Desktop.SKPaintSurfaceEventArgs)">
            <param name="e">The event arguments that contain the drawing surface and information.</param>
            <summary>Implement this to draw on the canvas.</summary>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.SKElement.OnRender(System.Windows.Media.DrawingContext)">
            <param name="drawingContext">The drawing instructions for a specific element. This context is provided to the layout system.</param>
            <summary>When overridden in a derived class, participates in rendering operations that are directed by the layout system. The rendering instructions for this element are not used directly when this method is invoked, and are instead preserved for later asynchronous use by layout and drawing.</summary>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.SKElement.OnRenderSizeChanged(System.Windows.SizeChangedInfo)">
            <param name="sizeInfo">Details of the old and new size involved in the change.</param>
            <summary>Raises the SizeChanged event, using the specified information as part of the eventual event data.</summary>
            <remarks />
        </member>
        <member name="E:SkiaSharp.Views.WPF.SKElement.PaintSurface">
            <summary>Occurs when the the canvas needs to be redrawn.</summary>
            <remarks />
        </member>
        <member name="T:SkiaSharp.Views.WPF.WPFExtensions">
            <summary>Various extension methods to convert between SkiaSharp types and Windows types.</summary>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToColor(SkiaSharp.SKColor)">
            <param name="color">The SkiaSharp color.</param>
            <summary>Converts a SkiaSharp color into a Windows color.</summary>
            <returns>Returns a Windows color.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToPoint(SkiaSharp.SKPoint)">
            <param name="point">The SkiaSharp point.</param>
            <summary>Converts a SkiaSharp point into a Windows point.</summary>
            <returns>Returns a Windows point.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToRect(SkiaSharp.SKRect)">
            <param name="rect">The SkiaSharp rectangle.</param>
            <summary>Converts a SkiaSharp rectangle into a Windows rectangle.</summary>
            <returns>Returns a Windows rectangle.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToSize(SkiaSharp.SKSize)">
            <param name="size">The SkiaSharp size.</param>
            <summary>Converts a SkiaSharp size into a Windows size.</summary>
            <returns>Returns a Windows size.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToSKBitmap(System.Windows.Media.Imaging.BitmapSource)">
            <param name="bitmap">The Windows BitmapSource to convert.</param>
            <summary>Converts a Windows BitmapSource into a SkiaSharp bitmap.</summary>
            <returns>Returns a copy of the bitmap data as a SkiaSharp bitmap.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToSKColor(System.Windows.Media.Color)">
            <param name="color">The Windows color.</param>
            <summary>Converts a Windows color into a SkiaSharp color.</summary>
            <returns>Returns a SkiaSharp color.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToSKImage(System.Windows.Media.Imaging.BitmapSource)">
            <param name="bitmap">The Windows BitmapSource to convert.</param>
            <summary>Converts a Windows BitmapSource into a SkiaSharp image.</summary>
            <returns>Returns a copy of the bitmap data as a SkiaSharp image.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToSKPixmap(System.Windows.Media.Imaging.BitmapSource,SkiaSharp.SKPixmap)">
            <param name="bitmap">The Windows BitmapSource to convert.</param>
            <param name="pixmap">The SkiaSharp pixmap to hold the copy of the bitmap data.</param>
            <summary>Converts a Windows BitmapSource into a SkiaSharp pixmap.</summary>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToSKPoint(System.Windows.Point)">
            <param name="point">The Windows point.</param>
            <summary>Converts a Windows point into a SkiaSharp point.</summary>
            <returns>Returns a SkiaSharp point.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToSKRect(System.Windows.Rect)">
            <param name="rect">The Windows rectangle</param>
            <summary>Converts a Windows rectangle into a SkiaSharp rectangle.</summary>
            <returns>Returns a SkiaSharp rectangle.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToSKSize(System.Windows.Size)">
            <param name="size">The Windows size.</param>
            <summary>Converts a Windows size into a SkiaSharp size.</summary>
            <returns>Returns a SkiaSharp size.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToWriteableBitmap(SkiaSharp.SKBitmap)">
            <param name="skiaBitmap">The SkiaSharp bitmap.</param>
            <summary>Converts a SkiaSharp bitmap into a Windows WriteableBitmap.</summary>
            <returns>Returns a copy of the bitmap data as a Windows WriteableBitmap.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToWriteableBitmap(SkiaSharp.SKImage)">
            <param name="skiaImage">The SkiaSharp image.</param>
            <summary>Converts a SkiaSharp image into a Windows WriteableBitmap.</summary>
            <returns>Returns a copy of the image data as a Windows WriteableBitmap.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToWriteableBitmap(SkiaSharp.SKPixmap)">
            <param name="pixmap">The SkiaSharp pixmap.</param>
            <summary>Converts a SkiaSharp pixmap into a Windows WriteableBitmap.</summary>
            <returns>Returns a copy of the pixel data as a Windows WriteableBitmap.</returns>
            <remarks />
        </member>
        <member name="M:SkiaSharp.Views.WPF.WPFExtensions.ToWriteableBitmap(SkiaSharp.SKPicture,SkiaSharp.SKSizeI)">
            <param name="picture">The SkiaSharp picture.</param>
            <param name="dimensions">The dimensions of the picture.</param>
            <summary>Converts a SkiaSharp picture into a Windows WriteableBitmap.</summary>
            <returns>Returns a copy of the picture as a Windows WriteableBitmap.</returns>
            <remarks />
        </member>
    </members>
</doc>
