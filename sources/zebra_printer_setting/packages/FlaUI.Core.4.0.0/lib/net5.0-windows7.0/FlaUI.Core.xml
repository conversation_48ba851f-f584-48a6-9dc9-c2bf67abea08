<?xml version="1.0"?>
<doc>
    <assembly>
        <name>FlaUI.Core</name>
    </assembly>
    <members>
        <member name="T:FlaUI.Core.ActionDisposable">
            <summary>
            An <see cref="T:System.IDisposable"/> implementation that executes an <see cref="T:System.Action"/> upon disposal.
            </summary>
        </member>
        <member name="M:FlaUI.Core.ActionDisposable.#ctor(System.Action)">
            <summary>
            Constructs a new disposable with the given action used for disposal.
            </summary>
            <param name="disposeAction">The action that is called upon disposal.</param>
        </member>
        <member name="M:FlaUI.Core.ActionDisposable.Dispose">
            <summary>
            Calls the defined <see cref="T:System.Action"/>.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Application">
            <summary>
            Wrapper for an application which should be automated.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Application._process">
            <summary>
            The process of this application.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Application._disposed">
            <summary>
            Flag to indicate if Dispose has already been called.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Application.CloseTimeout">
            <summary>
            The timeout to wait to close an application gracefully.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Application.IsStoreApp">
            <summary>
            Flag to indicate, if the application is a windows store app.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Application.ProcessId">
            <summary>
            The process id of the application.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Application.Name">
            <summary>
            The name of the application's process.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Application.MainWindowHandle">
            <summary>
            The current handle (Win32) of the application's main window.
            Can be IntPtr.Zero if no main window is currently available.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Application.HasExited">
            <summary>
            Gets a value indicating whether the associated process has been terminated.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Application.ExitCode">
            <summary>
            Gets the value that the associated process specified when it terminated.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Application.#ctor(System.Int32,System.Boolean)">
            <summary>
            Creates an application object with the given process id.
            </summary>
            <param name="processId">The process id.</param>
            <param name="isStoreApp">Flag to define if it's a store app or not.</param>
        </member>
        <member name="M:FlaUI.Core.Application.#ctor(System.Diagnostics.Process,System.Boolean)">
            <summary>
            Creates an application object with the given process.
            </summary>
            <param name="process">The process.</param>
            <param name="isStoreApp">Flag to define if it's a store app or not.</param>
        </member>
        <member name="M:FlaUI.Core.Application.Close(System.Boolean)">
            <summary>
            Closes the application. Force-closes it after a small timeout.
            </summary>
            <param name="killIfCloseFails">A flag to indicate if the process should be killed if closing fails within the <see cref="P:FlaUI.Core.Application.CloseTimeout"/>.</param>
            <returns>Returns true if the application was closed normally and false if it could not be closed gracefully.</returns>
        </member>
        <member name="M:FlaUI.Core.Application.Kill">
            <summary>
            Kills the applications and waits until it is closed.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Application.Dispose">
            <summary>
            Disposes the application.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Application.Dispose(System.Boolean)">
            <summary>
            Disposes the application.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Application.Attach(System.Int32)">
            <summary>
            Attaches to a given process id.
            </summary>
            <param name="processId">The id of the process to attach to.</param>
            <returns>An application instance which is attached to the process.</returns>
        </member>
        <member name="M:FlaUI.Core.Application.Attach(System.Diagnostics.Process)">
            <summary>
            Attaches to a given process.
            </summary>
            <param name="process">The process to attach to.</param>
            <returns>An application instance which is attached to the process.</returns>
        </member>
        <member name="M:FlaUI.Core.Application.Attach(System.String,System.Int32)">
            <summary>
            Attaches to a running process which has the given executable.
            </summary>
            <param name="executable">The executable of the process to attach to.</param>
            <param name="index">Defines the index of the process to use in case multiple are found.</param>
            <returns>An application instance which is attached to the process.</returns>
        </member>
        <member name="M:FlaUI.Core.Application.AttachOrLaunch(System.Diagnostics.ProcessStartInfo)">
            <summary>
            Attaches or launches the given process.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Application.Launch(System.String,System.String)">
            <summary>
            Launches the given executable.
            </summary>
            <param name="executable">The executable to launch.</param>
            <param name="arguments">Arguments to executable</param>
        </member>
        <member name="M:FlaUI.Core.Application.Launch(System.Diagnostics.ProcessStartInfo)">
            <summary>
            Launches an application with the given process information.
            </summary>
            <param name="processStartInfo">The process information used to launch the application.</param>
        </member>
        <member name="M:FlaUI.Core.Application.LaunchStoreApp(System.String,System.String)">
            <summary>
            Launches a store application.
            </summary>
            <param name="appUserModelId">The app id of the application to launch.</param>
            <param name="arguments">The arguments to pass to the application.</param>
        </member>
        <member name="M:FlaUI.Core.Application.WaitWhileBusy(System.Nullable{System.TimeSpan})">
            <summary>
            Waits as long as the application is busy.
            </summary>
            <param name="waitTimeout">An optional timeout. If null is passed, the timeout is infinite.</param>
            <returns>True if the application is idle, false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.Application.WaitWhileMainHandleIsMissing(System.Nullable{System.TimeSpan})">
            <summary>
            Waits until the main handle is set.
            </summary>
            <param name="waitTimeout">An optional timeout. If null is passed, the timeout is infinite.</param>
            <returns>True a main window handle was found, false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.Application.GetMainWindow(FlaUI.Core.AutomationBase,System.Nullable{System.TimeSpan})">
            <summary>
            Gets the main window of the applications process.
            </summary>
            <param name="automation">The automation object to use.</param>
            <param name="waitTimeout">An optional timeout. If null is passed, the timeout is infinite.</param>
            <returns>The main window object as <see cref="T:FlaUI.Core.AutomationElements.Window" /> or null if no main window was found within the timeout.</returns>
        </member>
        <member name="M:FlaUI.Core.Application.GetAllTopLevelWindows(FlaUI.Core.AutomationBase)">
            <summary>
            Gets all top level windows from the application.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationBase">
            <summary>
            Base class for the native automation object.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationBase.#ctor(FlaUI.Core.IPropertyLibrary,FlaUI.Core.IEventLibrary,FlaUI.Core.IPatternLibrary,FlaUI.Core.ITextAttributeLibrary)">
            <summary>
            Creates a new <see cref="T:FlaUI.Core.AutomationBase"/> instance.
            </summary>
            <param name="propertyLibrary">The property library to use.</param>
            <param name="eventLibrary">The event library to use.</param>
            <param name="patternLibrary">The pattern library to use.</param>
            <param name="textAttributeLibrary">The text attribute library to use.</param>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.PropertyLibrary">
            <summary>
            Provides a library with the existing <see cref="T:FlaUI.Core.Identifiers.PropertyId"/>s.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.EventLibrary">
            <summary>
            Provides a library with the existing <see cref="T:FlaUI.Core.Identifiers.EventId"/>s.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.PatternLibrary">
            <summary>
            Provides a library with the existing <see cref="T:FlaUI.Core.Identifiers.PatternId"/>s.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.TextAttributeLibrary">
            <summary>
            Provides a library with the existing <see cref="T:FlaUI.Core.Identifiers.TextAttributeId"/>s.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.ConditionFactory">
            <summary>
            Provides a factory to create conditions for searching.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.OverlayManager">
            <summary>
            Provides a manager for displaying overlays.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.TreeWalkerFactory">
            <summary>
            Provides a factory to create <see cref="T:FlaUI.Core.ITreeWalker"/>s.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.AutomationType">
            <summary>
            The <see cref="P:FlaUI.Core.AutomationBase.AutomationType"/> of the automation implementation.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.NotSupportedValue">
            <summary>
            Object which represents the "Not Supported" value.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.MixedAttributeValue">
            <summary>
            Object which represents a mixed attribute value in a textpattern.
            For example if the text contains multiple fonts, the FontName attribute will return this value.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.TransactionTimeout">
            <summary>
            Specifies the length of time that UI Automation will wait for a provider to respond to a client request for information about an automation element.
            The default is 20 seconds.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.ConnectionTimeout">
            <summary>
            Specifies the length of time that UI Automation will wait for a provider to respond to a client request for an automation element.
            The default is two seconds.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.ConnectionRecoveryBehavior">
            <summary>
            Indicates whether an accessible technology client adjusts provider request timeouts when the provider is non-responsive.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationBase.CoalesceEvents">
            <summary>
            Gets or sets whether an accessible technology client receives all events, or a subset where duplicate events are detected and filtered.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationBase.GetDesktop">
            <summary>
            Gets the desktop (root) element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationBase.FromPoint(System.Drawing.Point)">
            <summary>
            Creates an <see cref="T:FlaUI.Core.AutomationElements.AutomationElement" /> from a given point.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationBase.FromHandle(System.IntPtr)">
            <summary>
            Creates an <see cref="T:FlaUI.Core.AutomationElements.AutomationElement" /> from a given windows handle (HWND).
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationBase.FocusedElement">
            <summary>
            Gets the currently focused element as an <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>.
            </summary>
            <returns></returns>
        </member>
        <member name="M:FlaUI.Core.AutomationBase.RegisterFocusChangedEvent(System.Action{FlaUI.Core.AutomationElements.AutomationElement})">
            <summary>
            Registers for a focus changed event.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationBase.UnregisterFocusChangedEvent(FlaUI.Core.EventHandlers.FocusChangedEventHandlerBase)">
            <summary>
            Unregisters the given focus changed event handler.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationBase.UnregisterAllEvents">
            <summary>
            Removes all registered event handlers.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationBase.Compare(FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Compares two automation elements for equality.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationBase.Dispose">
            <summary>
            Cleans up the resources.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.AutomationElementExtensions">
            <summary>
            Contains extension methods for <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>s.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsButton(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Button"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsCalendar(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Calendar"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsCheckBox(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.CheckBox"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsComboBox(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.ComboBox"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsDataGridView(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.DataGridView"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsDateTimePicker(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.DateTimePicker"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsLabel(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Label"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsGrid(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Grid"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsGridRow(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.GridRow"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsGridCell(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.GridCell"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsGridHeader(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.GridHeader"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsGridHeaderItem(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.GridHeaderItem"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsHorizontalScrollBar(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsListBox(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.ListBox"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsListBoxItem(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.ListBoxItem"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsMenu(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Menu"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsMenuItem(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.MenuItem"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsProgressBar(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.ProgressBar"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsRadioButton(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.RadioButton"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsSlider(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Slider"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsSpinner(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Spinner"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsTab(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Tab"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsTabItem(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.TabItem"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsTextBox(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.TextBox"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsThumb(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Thumb"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsTitleBar(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.TitleBar"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsToggleButton(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.ToggleButton"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsTree(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Tree"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsTreeItem(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.TreeItem"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsVerticalScrollBar(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsWindow(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Converts the element to a <see cref="T:FlaUI.Core.AutomationElements.Window"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.AsType(FlaUI.Core.AutomationElements.AutomationElement,System.Type)">
            <summary>
            Method to convert the element to the given type.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.As``1(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Generic method to convert the element to the given type.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.DrawHighlight``1(``0)">
            <summary>
            Draws a red highlight around the element.
            </summary>
            <param name="self">The element to highlight.</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.DrawHighlight``1(``0,System.Drawing.Color)">
            <summary>
            Draws a manually colored highlight around the element.
            </summary>
            <param name="self">The element to highlight.</param>
            <param name="color">The color to draw the highlight.</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.DrawHighlight``1(``0,System.Boolean,System.Drawing.Color,System.Nullable{System.TimeSpan})">
            <summary>
            Draw a highlight around the element with the given settings.
            </summary>
            <param name="self">The element to highlight.</param>
            <param name="blocking">Flag to indicate if further execution waits until the highlight is removed.</param>
            <param name="color">The color to draw the highlight.</param>
            <param name="duration">The duration how long the highlight is shown.</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.WaitUntilClickable``1(``0,System.Nullable{System.TimeSpan})">
            <summary>
            Waits until the element has a clickable point.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElementExtensions.WaitUntilEnabled``1(``0,System.Nullable{System.TimeSpan})">
            <summary>
            Waits until the element is enabled.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.AutomationElement">
            <summary>
            Wrapper object for each ui element which is should be automated.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a new instance which wraps around the given <see cref="P:FlaUI.Core.AutomationElements.AutomationElement.FrameworkAutomationElement"/>.
            </summary>
            <param name="frameworkAutomationElement">The <see cref="P:FlaUI.Core.AutomationElements.AutomationElement.FrameworkAutomationElement"/> to wrap.</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.#ctor(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Creates a new instance which wraps the <see cref="P:FlaUI.Core.AutomationElements.AutomationElement.FrameworkAutomationElement"/> of the given <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>.
            </summary>
            <param name="automationElement">The <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/> which <see cref="P:FlaUI.Core.AutomationElements.AutomationElement.FrameworkAutomationElement"/> should be wrapped.</param>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.FrameworkAutomationElement">
            <summary>
            Object which contains the native wrapper element (UIA2 or UIA3) for this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.Parent">
            <summary>
            Get the parent <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.Automation">
            <summary>
            The current used automation object.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.ConditionFactory">
            <summary>
            Shortcut to the condition factory for the current automation.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.AutomationType">
            <summary>
            The current <see cref="P:FlaUI.Core.AutomationElements.AutomationElement.AutomationType" /> for this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.Patterns">
            <summary>
            Standard UIA patterns of this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.Properties">
            <summary>
            Standard UIA properties of this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.CachedChildren">
            <summary>
            Gets the cached children for this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.CachedParent">
            <summary>
            Gets the cached parent for this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.IsAvailable">
            <summary>
            A flag that indicates if the element is still available. Can be false if the element is already unloaded from the ui.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.FrameworkType">
            <summary>
            The direct framework type of the element.
            Results in "FrameworkType.Unknown" if it couldn't be resolved.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.AutomationId">
            <summary>
            The automation id of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.Name">
            <summary>
            The name of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.ClassName">
            <summary>
            The class name of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.ControlType">
            <summary>
            The control type of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.IsEnabled">
            <summary>
            Flag if the element is enabled or not.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.IsOffscreen">
            <summary>
            Flag if the element is off-screen or on-screen(visible).
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.BoundingRectangle">
            <summary>
            The bounding rectangle of this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.ActualWidth">
            <summary>
            The width of this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.ActualHeight">
            <summary>
            The height of this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.ItemStatus">
            <summary>
            The item status of this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.AutomationElement.HelpText">
            <summary>
            The help text of this element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.Click(System.Boolean)">
            <summary>
            Performs a left click on the element.
            </summary>
            <param name="moveMouse">Flag to indicate, if the mouse should move slowly (true) or instantly (false).</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.DoubleClick(System.Boolean)">
            <summary>
            Performs a double left click on the element.
            </summary>
            <param name="moveMouse">Flag to indicate, if the mouse should move slowly (true) or instantly (false).</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.RightClick(System.Boolean)">
            <summary>
            Performs a right click on the element.
            </summary>
            <param name="moveMouse">Flag to indicate, if the mouse should move slowly (true) or instantly (false).</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.RightDoubleClick(System.Boolean)">
            <summary>
            Performs a double right click on the element.
            </summary>
            <param name="moveMouse">Flag to indicate, if the mouse should move slowly (true) or instantly (false).</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.Focus">
            <summary>
            Sets the focus to a control. If the control is a window, brings it to the foreground.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FocusNative">
            <summary>
            Sets the focus by using the Win32 SetFocus() method.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.SetForeground">
            <summary>
            Brings a window to the foreground.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.Capture">
            <summary>
            Captures the object as screenshot in <see cref="T:System.Drawing.Bitmap"/> format.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.CaptureToFile(System.String)">
            <summary>
            Captures the object as screenshot directly into the given file.
            </summary>
            <param name="filePath">The filepath where the screenshot should be saved.</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.GetClickablePoint">
            <summary>
            Gets a clickable point of the element.
            </summary>
            <exception cref="T:FlaUI.Core.Exceptions.NoClickablePointException">Thrown when no clickable point was found</exception>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.TryGetClickablePoint(System.Drawing.Point@)">
            <summary>
            Tries to get a clickable point of the element.
            </summary>
            <param name="point">The clickable point or null, if no point was found</param>
            <returns>True if a point was found, false otherwise</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.RegisterActiveTextPositionChangedEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.ITextRange})">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.RegisterAutomationEvent(FlaUI.Core.Identifiers.EventId,FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Identifiers.EventId})">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.RegisterPropertyChangedEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Identifiers.PropertyId,System.Object},FlaUI.Core.Identifiers.PropertyId[])">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.RegisterStructureChangedEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Definitions.StructureChangeType,System.Int32[]})">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.RegisterNotificationEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Definitions.NotificationKind,FlaUI.Core.Definitions.NotificationProcessing,System.String,System.String})">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.RegisterTextEditTextChangedEventHandler(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Definitions.TextEditChangeType,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Definitions.TextEditChangeType,System.String[]})">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.GetSupportedPatterns">
            <summary>
            Gets the available patterns for an element via properties.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.IsPatternSupported(FlaUI.Core.Identifiers.PatternId)">
            <summary>
            Checks if the given pattern is available for the element via properties.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.GetSupportedPatternsDirect">
            <summary>
            Gets the available patterns for an element via UIA method.
            Does not work with cached elements and might be unreliable.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.IsPatternSupportedDirect(FlaUI.Core.Identifiers.PatternId)">
            <summary>
            Checks if the given pattern is available for the element via UIA method.
            Does not work with cached elements and might be unreliable.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.GetSupportedPropertiesDirect">
            <summary>
            Gets the available properties for an element via UIA method.
            Does not work with cached elements and might be unreliable.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.IsPropertySupportedDirect(FlaUI.Core.Identifiers.PropertyId)">
            <summary>
            Method to check if the element supports the given property via UIA method.
            Does not work with cached elements and might be unreliable.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.GetCurrentMetadataValue(FlaUI.Core.Identifiers.PropertyId,System.Int32)">
            <summary>
            Gets metadata from the UI Automation element that indicates how the information should be interpreted.
            </summary>
            <param name="targetId">The property to retrieve.</param>
            <param name="metadataId">Specifies the type of metadata to retrieve.</param>
            <returns>The metadata.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.Equals(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Compares two elements.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.ToString">
            <summary>
            Overrides the string representation of the element with something useful.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.ExecuteInPattern``1(``0,System.Boolean,System.Action{``0})">
            <summary>
            Executes the given action on the given pattern.
            </summary>
            <typeparam name="TPattern">The type of the pattern.</typeparam>
            <param name="pattern">The pattern.</param>
            <param name="throwIfNotSupported">Flag to indicate if an exception should be thrown if the pattern is not supported.</param>
            <param name="action">The action to execute on the pattern</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.ExecuteInPattern``2(``0,System.Boolean,System.Func{``0,``1})">
            <summary>
            Executes the given func on the given pattern returning the received value.
            </summary>
            <typeparam name="TPattern">The type of the pattern.</typeparam>
            <typeparam name="TRet">The type of the return value.</typeparam>
            <param name="pattern">Zhe pattern.</param>
            <param name="throwIfNotSupported">Flag to indicate if an exception should be thrown if the pattern is not supported.</param>
            <param name="func">The function to execute on the pattern.</param>
            <returns>The value received from the pattern or the default if the pattern is not supported.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.SetFocus">
            <summary>
            Sets focus onto control using UIA native element
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAll(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirst(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAllWithOptions(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase,FlaUI.Core.Definitions.TreeTraversalOptions,FlaUI.Core.AutomationElements.AutomationElement)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstWithOptions(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase,FlaUI.Core.Definitions.TreeTraversalOptions,FlaUI.Core.AutomationElements.AutomationElement)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAt(FlaUI.Core.Definitions.TreeScope,System.Int32,FlaUI.Core.Conditions.ConditionBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstNested(FlaUI.Core.Conditions.ConditionBase[])">
            <summary>
            Finds the first element by iterating thru all conditions.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAllNested(FlaUI.Core.Conditions.ConditionBase[])">
            <summary>
            Finds all elements by iterating thru all conditions.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstByXPath(System.String)">
            <summary>
            Finds for the first item which matches the given xpath.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAllByXPath(System.String)">
            <summary>
            Finds all items which match the given xpath.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstChild">
            <summary>
            Finds the first child.
            </summary>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstChild(System.String)">
            <summary>
            Finds the first child with the given automation id.
            </summary>
            <param name="automationId">The automation id.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstChild(FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Finds the first child with the condition.
            </summary>
            <param name="condition">The condition.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstChild(System.Func{FlaUI.Core.Conditions.ConditionFactory,FlaUI.Core.Conditions.ConditionBase})">
            <summary>
            Finds the first child with the condition.
            </summary>
            <param name="conditionFunc">The condition method.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAllChildren">
            <summary>
            Finds all children.
            </summary>
            <returns>The found elements or an empty list if no elements were found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAllChildren(FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Finds all children with the condition.
            </summary>
            <param name="condition">The condition.</param>
            <returns>The found elements or an empty list if no elements were found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAllChildren(System.Func{FlaUI.Core.Conditions.ConditionFactory,FlaUI.Core.Conditions.ConditionBase})">
            <summary>
            Finds all children with the condition.
            </summary>
            <param name="conditionFunc">The condition method.</param>
            <returns>The found elements or an empty list if no elements were found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstDescendant">
            <summary>
            Finds the first descendant.
            </summary>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstDescendant(System.String)">
            <summary>
            Finds the first descendant with the given automation id.
            </summary>
            <param name="automationId">The automation id.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstDescendant(FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Finds the first descendant with the condition.
            </summary>
            <param name="condition">The condition.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstDescendant(System.Func{FlaUI.Core.Conditions.ConditionFactory,FlaUI.Core.Conditions.ConditionBase})">
            <summary>
            Finds the first descendant with the condition.
            </summary>
            <param name="conditionFunc">The condition method.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAllDescendants">
            <summary>
            Finds all descendants.
            </summary>
            <returns>The found elements or an empty list if no elements were found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAllDescendants(FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Finds all descendants with the condition.
            </summary>
            <param name="condition">The condition.</param>
            <returns>The found elements or an empty list if no elements were found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAllDescendants(System.Func{FlaUI.Core.Conditions.ConditionFactory,FlaUI.Core.Conditions.ConditionBase})">
            <summary>
            Finds all descendants with the condition.
            </summary>
            <param name="conditionFunc">The condition method.</param>
            <returns>The found elements or an empty list if no elements were found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindFirstNested(System.Func{FlaUI.Core.Conditions.ConditionFactory,System.Collections.Generic.IList{FlaUI.Core.Conditions.ConditionBase}})">
            <summary>
            Finds the first element by iterating thru all conditions.
            </summary>
            <param name="conditionFunc">The condition method.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindAllNested(System.Func{FlaUI.Core.Conditions.ConditionFactory,System.Collections.Generic.IList{FlaUI.Core.Conditions.ConditionBase}})">
            <summary>
            Finds all elements by iterating thru all conditions.
            </summary>
            <param name="conditionFunc">The condition method.</param>
            <returns>The found elements or an empty list if no elements were found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindChildAt(System.Int32,FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Finds the child at the given position with the condition.
            </summary>
            <param name="index">The index of the child to find.</param>
            <param name="condition">The condition.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.AutomationElement.FindChildAt(System.Int32,System.Func{FlaUI.Core.Conditions.ConditionFactory,FlaUI.Core.Conditions.ConditionBase})">
            <summary>
            Finds the child at the given position with the condition.
            </summary>
            <param name="index">The index of the child to find.</param>
            <param name="conditionFunc">The condition method.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Button">
            <summary>
            Class to interact with a button element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Button.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.Button"/> element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Calendar">
            <summary>
            Class to interact with a calendar element. Not supported for Windows Forms calendar.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Calendar.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.Calendar"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Calendar.SelectedDates">
            <summary>
            Gets the selected dates in the calendar. For Win32 multiple selection calendar the returned array has two
            dates, the first date and the last date of the selected range. For WPF calendar the returned array contains
            all selected dates.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Calendar.SelectDate(System.DateTime)">
            <summary>
            Deselects other selected dates and selects the specified date.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Calendar.SelectRange(System.DateTime[])">
            <summary>
            For WPF calendar with SelectionMode="MultipleRange" this method deselects other selected dates and selects the specified range.
            For any other type of SelectionMode it deselects other selected dates and selects only the last date in the range.
            For Win32 multiple selection calendar the "dates" parameter should contain two dates, the first and the last date of the range to be selected.
            For Win32 single selection calendar this method selects only the second date from the "dates" array.
            For WPF calendar all dates should be specified in the "dates" parameter, not only the first and the last date of the range.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Calendar.AddToSelection(System.DateTime)">
            <summary>
            For WPF calendar with SelectionMode="MultipleRange" this method adds the specified date to current selection.
            For any other type of SelectionMode it deselects other selected dates and selects the specified date.
            This method is supported only for WPF calendar.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Calendar.AddRangeToSelection(System.DateTime[])">
            <summary>
            For WPF calendar with SelectionMode="MultipleRange" this method adds the specified range to current selection.
            For any other type of SelectionMode it deselects other selected dates and selects only the last date in the range.
            This method is supported only for WPF calendar.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.CheckBox">
            <summary>
            Class to interact with a checkbox element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.CheckBox.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.CheckBox"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.CheckBox.IsChecked">
            <summary>
            Gets or sets if the checkbox is checked.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.CheckBox.Text">
            <summary>
            Gets the text of the element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.ComboBox">
            <summary>
            Class to interact with a combobox element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ComboBox.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.ComboBox"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.AnimationDuration">
            <summary>
            Timespan to wait until the animation for opening/closing is finished.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.ValuePattern">
            <summary>
            Pattern object for the <see cref="T:FlaUI.Core.Patterns.IValuePattern"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.SelectionPattern">
            <summary>
            Pattern object for the <see cref="T:FlaUI.Core.Patterns.ISelectionPattern"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.EditableText">
            <summary>
            The text of the editable element inside the combobox.
            Only works if the combobox is editable.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.IsEditable">
            <summary>
            Flag which indicates, if the combobox is editable or not.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.IsReadOnly">
            <summary>
            Flag which indicates, if the combobox is read-only or not.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.EditableItem">
            <summary>
            Gets the editable element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.Value">
            <summary>
            Getter / setter for the selected value.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.SelectedItems">
            <summary>
            Gets all selected items.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.SelectedItem">
            <summary>
            Gets the first selected item or null otherwise.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.Items">
            <summary>
            Gets all items.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBox.ExpandCollapseState">
            <summary>
            Gets the <see cref="P:FlaUI.Core.AutomationElements.ComboBox.ExpandCollapseState"/> of the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ComboBox.Expand">
            <summary>
            Expands the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ComboBox.Collapse">
            <summary>
            Collapses the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ComboBox.Select(System.Int32)">
            <summary>
            Select an item by index.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ComboBox.Select(System.String)">
            <summary>
            Select the first item which matches the given text.
            </summary>
            <param name="textToFind">The text to search for.</param>
            <returns>The first found item or null if no item matches.</returns>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.ComboBoxItem">
            <summary>
            Class to interact with a combobox item element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ComboBoxItem.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.ComboBoxItem"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ComboBoxItem.Text">
            <summary>
            Gets the text of the element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.DataGridView">
            <summary>
            Class to interact with a WinForms DataGridView
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.DataGridView.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.DataGridView"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.DataGridView.HasAddRow">
            <summary>
            Flag to indicate if the grid has the "Add New Item" row or not.
            This needs to be set as FlaUI cannot find out if this is the case or not.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.DataGridView.Header">
            <summary>
            Gets the header element or null if the header is disabled.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.DataGridView.Rows">
            <summary>
            Gets all the data rows.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.DataGridViewHeader">
            <summary>
            Class to interact with a WinForms DataGridView header.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.DataGridViewHeader.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.DataGridViewHeader"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.DataGridViewHeader.Columns">
            <summary>
            Gets the header items.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.DataGridViewHeaderItem">
            <summary>
            Class to interact with a WinForms DataGridView header item.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.DataGridViewHeaderItem.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.DataGridViewHeaderItem"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.DataGridViewHeaderItem.Text">
            <summary>
            Gets the text of the header item.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.DataGridViewRow">
            <summary>
            Class to interact with a WinForms DataGridView row.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.DataGridViewRow.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.DataGridViewRow"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.DataGridViewRow.Cells">
            <summary>
            Gets all cells.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.DataGridViewCell">
            <summary>
            Class to interact with a WinForms DataGridView cell.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.DataGridViewCell.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.DataGridViewCell"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.DataGridViewCell.ValuePattern">
            <summary>
            Pattern object for the <see cref="T:FlaUI.Core.Patterns.IValuePattern"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.DataGridViewCell.Value">
            <summary>
            Gets or sets the value in the cell.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.DateTimePicker">
            <summary>
            Class to interact with a DateTimePicker element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.DateTimePicker.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.DateTimePicker"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.DateTimePicker.SelectedDate">
            <summary>
            Gets or sets the selected date in the DateTimePicker.
            For Win32, setting SelectedDate to null will uncheck the DateTimePicker control and disable it. Also for Win32, if the control is unchecked then SelectedDate will return null.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Grid">
            <summary>
            Element for grids and tables.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Grid.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a grid object from a given element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.GridPattern">
            <summary>
            Provides direct access to the grid pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.TablePattern">
            <summary>
            Provides direct access to the table pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.SelectionPattern">
            <summary>
            Provides direct access to the selection pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.RowCount">
            <summary>
            Gets the total row count.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.ColumnCount">
            <summary>
            Gets the total column count.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.ColumnHeaders">
            <summary>
            Gets all column header elements.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.RowHeaders">
            <summary>
            Gets all row header elements.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.RowOrColumnMajor">
            <summary>
            Gets whether the data should be read primarily by row or by column.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.Header">
            <summary>
            Gets the header item.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.Rows">
            <summary>
            Returns the rows which are currently visible to UIA. Might not be the full list (eg. in virtualized lists)!
            Use <see cref="M:FlaUI.Core.AutomationElements.Grid.GetRowByIndex(System.Int32)" /> to make sure to get the correct row.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.SelectedItems">
            <summary>
            Gets all selected items.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Grid.SelectedItem">
            <summary>
            Gets the first selected item or null otherwise.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Grid.Select(System.Int32)">
            <summary>
            Select a row by index.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Grid.Select(System.Int32,System.String)">
            <summary>
            Select the first row by text in the given column.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Grid.AddToSelection(System.Int32)">
            <summary>
            Add a row to the selection by index.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Grid.AddToSelection(System.Int32,System.String)">
            <summary>
            Add a row to the selection by text in the given column.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Grid.RemoveFromSelection(System.Int32)">
            <summary>
            Remove a row from the selection by index.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Grid.RemoveFromSelection(System.Int32,System.String)">
            <summary>
            Remove a row from the selection by text in the given column.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Grid.GetRowByIndex(System.Int32)">
            <summary>
            Get a row by index.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Grid.GetRowByValue(System.Int32,System.String)">
            <summary>
            Get a row by text in the given column.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Grid.GetRowsByValue(System.Int32,System.String,System.Int32)">
            <summary>
            Get all rows where the value of the given column matches the given value.
            </summary>
            <param name="columnIndex">The column index to check.</param>
            <param name="value">The value to check.</param>
            <param name="maxItems">Maximum numbers of items to return, 0 for all.</param>
            <returns>List of found rows.</returns>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.GridHeader">
            <summary>
            Header element for grids and tables.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.GridHeader.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a grid header object out of a given element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.GridHeader.Columns">
            <summary>
            Gets all header items from the grid header.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.GridHeaderItem">
            <summary>
            Header item for grids and tables.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.GridHeaderItem.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a grid header item object out of a given element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.GridHeaderItem.Text">
            <summary>
            Gets the text of the grid header item.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.GridRow">
            <summary>
            Row element for grids and tables.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.GridRow.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a grid row object out of a given element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.GridRow.ScrollItemPattern">
            <summary>
            Provides direct access to the scroll item pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.GridRow.Cells">
            <summary>
            Gets all the cells from the row.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.GridRow.Header">
            <summary>
            Gets the header item of the row.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.GridRow.FindCellByText(System.String)">
            <summary>
            Find a cell by a given text.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.GridRow.ScrollIntoView">
            <summary>
            Scrolls the row into view.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.GridCell">
            <summary>
            Cell element for grids and tables.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.GridCell.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a grid cell object out of a given element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.GridCell.GridItemPattern">
            <summary>
            Provides direct access to the grid item pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.GridCell.TableItemPattern">
            <summary>
            Provides direct access to the table item pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.GridCell.ContainingGrid">
            <summary>
            Gets the grid that contains this cell.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.GridCell.ContainingRow">
            <summary>
            Gets the row that contains this cell.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.GridCell.Value">
            <summary>
            Gets the value of this cell.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventIds">
            <summary>
            Interface for a class that contains all automation element event ids.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventSubscriber">
            <summary>
            Interface for methods to subscribe to events on an <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventSubscriber.RegisterActiveTextPositionChangedEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.ITextRange})">
            <summary>
            Registers a active text position changed event.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventSubscriber.RegisterAutomationEvent(FlaUI.Core.Identifiers.EventId,FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Identifiers.EventId})">
            <summary>
            Registers the given automation event.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventSubscriber.RegisterPropertyChangedEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Identifiers.PropertyId,System.Object},FlaUI.Core.Identifiers.PropertyId[])">
            <summary>
            Registers a property changed event with the given property.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventSubscriber.RegisterStructureChangedEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Definitions.StructureChangeType,System.Int32[]})">
            <summary>
            Registers a structure changed event.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventSubscriber.RegisterNotificationEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Definitions.NotificationKind,FlaUI.Core.Definitions.NotificationProcessing,System.String,System.String})">
            <summary>
            Registers a notification event.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventSubscriber.RegisterTextEditTextChangedEventHandler(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Definitions.TextEditChangeType,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Definitions.TextEditChangeType,System.String[]})">
            <summary>
            Registers a text edit text changed event.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventUnsubscriber">
            <summary>
            Interface for methods to unsubscribe to events on an <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventUnsubscriber.UnregisterActiveTextPositionChangedEventHandler(FlaUI.Core.EventHandlers.ActiveTextPositionChangedEventHandlerBase)">
            <summary>
            Unregisters the given active text position changed event handler.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventUnsubscriber.UnregisterAutomationEventHandler(FlaUI.Core.EventHandlers.AutomationEventHandlerBase)">
            <summary>
            Unregisters the given automation event handler.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventUnsubscriber.UnregisterPropertyChangedEventHandler(FlaUI.Core.EventHandlers.PropertyChangedEventHandlerBase)">
            <summary>
            Unregisters the given property changed event handler.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventUnsubscriber.UnregisterStructureChangedEventHandler(FlaUI.Core.EventHandlers.StructureChangedEventHandlerBase)">
            <summary>
            Unregisters the given structure changed event handler.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventUnsubscriber.UnregisterNotificationEventHandler(FlaUI.Core.EventHandlers.NotificationEventHandlerBase)">
            <summary>
            Unregisters the given notification event handler.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementEventUnsubscriber.UnregisterTextEditTextChangedEventHandler(FlaUI.Core.EventHandlers.TextEditTextChangedEventHandlerBase)">
            <summary>
            Unregisters the given text edit text changed event handler.
            </summary> 
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementFinder">
            <summary>
            Interface for the base methods to find <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>s on an <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementFinder.FindAll(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Finds all elements in the given scope with the given condition.
            </summary>
            <param name="treeScope">The scope to search.</param>
            <param name="condition">The condition to use.</param>
            <returns>The found elements or an empty list if no elements were found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementFinder.FindFirst(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Finds the first element in the given scope with the given condition.
            </summary>
            <param name="treeScope">The scope to search.</param>
            <param name="condition">The condition to use.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementFinder.FindAllWithOptions(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase,FlaUI.Core.Definitions.TreeTraversalOptions,FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Find all matching elements in the specified order.
            </summary>
            <param name="treeScope">A combination of values specifying the scope of the search.</param>
            <param name="condition">A condition that represents the criteria to match.</param>
            <param name="traversalOptions">Value specifying the tree navigation order.</param>
            <param name="root">An element with which to begin the search.</param>
            <returns>The found elements or an empty list if no elements were found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementFinder.FindFirstWithOptions(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase,FlaUI.Core.Definitions.TreeTraversalOptions,FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Finds the first matching element in the specified order.
            </summary>
            <param name="treeScope">A combination of values specifying the scope of the search.</param>
            <param name="condition">A condition that represents the criteria to match.</param>
            <param name="traversalOptions">Value specifying the tree navigation order.</param>
            <param name="root">An element with which to begin the search.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementFinder.FindAt(FlaUI.Core.Definitions.TreeScope,System.Int32,FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Finds the element with the given index with the given condition.
            </summary>
            <param name="treeScope">The scope to search.</param>
            <param name="index">The index of the element to return (0-based).</param>
            <param name="condition">The condition to use.</param>
            <returns>The found element or null if no element was found.</returns>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementPatternAvailabilityPropertyIds">
            <summary>
            Interface for a class that contains all automation element pattern availability property ids.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Infrastructure.IAutomationElementPropertyIds">
            <summary>
            Interface for a class that contains all automation element property ids.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Label">
            <summary>
            Class to interact with a label element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Label.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.Label"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Label.Text">
            <summary>
            Gets the text of the element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.ListBox">
            <summary>
            Class to interact with a list box element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ListBox.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.ListBox"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ListBox.SelectionPattern">
            <summary>
            Pattern object for the <see cref="T:FlaUI.Core.Patterns.ISelectionPattern"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ListBox.Items">
            <summary>
            Returns all the list box items
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ListBox.SelectedItems">
            <summary>
            Gets all selected items.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ListBox.SelectedItem">
            <summary>
            Gets the first selected item or null otherwise.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ListBox.Select(System.Int32)">
            <summary>
            Selects an item by index.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ListBox.Select(System.String)">
            <summary>
            Selects an item by text.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ListBox.AddToSelection(System.Int32)">
            <summary>
            Add a row to the selection by index.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ListBox.AddToSelection(System.String)">
            <summary>
            Add a row to the selection by text.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ListBox.RemoveFromSelection(System.Int32)">
            <summary>
            Remove a row from the selection by index.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ListBox.RemoveFromSelection(System.String)">
            <summary>
            Remove a row from the selection by text.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.ListBoxItem">
            <summary>
            Class to interact with a list box item element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ListBoxItem.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.ListBoxItem"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ListBoxItem.Text">
            <summary>
            Gets the text of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ListBoxItem.ScrollItemPattern">
            <summary>
            Pattern object for the <see cref="T:FlaUI.Core.Patterns.IScrollItemPattern"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ListBoxItem.ScrollIntoView">
            <summary>
            Scrolls the element into view.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ListBoxItem.IsChecked">
            <summary>
            Gets or sets if the listbox item is checked, if checking is supported
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Menu">
            <summary>
            Class to interact with a menu or menubar element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Menu.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.Menu"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Menu.Items">
            <summary>
            Gets all <see cref="T:FlaUI.Core.AutomationElements.MenuItem"/> which are inside this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Menu.IsWin32Menu">
            <summary>
            Flag to indicate if the menu is a Win32 menu because that one needs special handling
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.MenuItem">
            <summary>
            Class to interact with a menu item element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.MenuItem.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.MenuItem"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.MenuItem.IsWin32Menu">
            <summary>
            Flag to indicate if the containing menu is a Win32 menu because that one needs special handling
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.MenuItem.Text">
            <summary>
            Gets the text of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.MenuItem.Items">
            <summary>
            Gets all <see cref="T:FlaUI.Core.AutomationElements.MenuItem"/> which are inside this element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.MenuItem.Invoke">
            <summary>
            Invokes the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.MenuItem.Expand">
            <summary>
            Expands the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.MenuItem.Collapse">
            <summary>
            Collapses the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.MenuItem.IsChecked">
            <summary>
            Gets or sets if a menu item is checked or unchecked, if checking is supported.
            For some applications, like WPF, setting this property doesn't execute the action that happens when a user clicks the menu item, only the checked state is changed.
            For WPF and Windows Forms applications, if you want to execute the action too, you need to use Invoke() method.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.MenuItems">
            <summary>
            Represents a list of <see cref="T:FlaUI.Core.AutomationElements.MenuItem"/> elements.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.MenuItems.#ctor(System.Collections.Generic.IEnumerable{FlaUI.Core.AutomationElements.MenuItem})">
            <summary>
            Creates the list of <see cref="T:FlaUI.Core.AutomationElements.MenuItem"/> elements.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.MenuItems.Length">
            <summary>
            Gets the number of elements in the list.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.MenuItems.Item(System.String)">
            <summary>
            Gets the <see cref="T:FlaUI.Core.AutomationElements.MenuItem"/> with the given text.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.PatternElements.ExpandCollapseAutomationElement">
            <summary>
            An element that supports the <see cref="T:FlaUI.Core.Patterns.IExpandCollapsePattern"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.ExpandCollapseAutomationElement.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
             Creates an expand/collapse element object from a given element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.PatternElements.ExpandCollapseAutomationElement.ExpandCollapsePattern">
            <summary>
             Provides direct access to the expand/collapse pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.PatternElements.ExpandCollapseAutomationElement.ExpandCollapseState">
            <summary>
            Gets the current expand/collapse state.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.ExpandCollapseAutomationElement.Expand">
            <summary>
            Expands the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.ExpandCollapseAutomationElement.Collapse">
            <summary>
            Collapses the element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.PatternElements.InvokeAutomationElement">
            <summary>
            An element that supports the <see cref="T:FlaUI.Core.Patterns.IInvokePattern"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.InvokeAutomationElement.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates an element with a <see cref="T:FlaUI.Core.Patterns.IInvokePattern"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.PatternElements.InvokeAutomationElement.InvokePattern">
            <summary>
             Provides direct access to the invoke pattern.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.InvokeAutomationElement.Invoke">
            <summary>
            Invokes the element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.PatternElements.SelectionItemAutomationElement">
            <summary>
            An element which supports the <see cref="T:FlaUI.Core.Patterns.ISelectionItemPattern" />.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.SelectionItemAutomationElement.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.PatternElements.SelectionItemAutomationElement"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.PatternElements.SelectionItemAutomationElement.SelectionItemPattern">
            <summary>
            Pattern object for the <see cref="T:FlaUI.Core.Patterns.ISelectionItemPattern"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.PatternElements.SelectionItemAutomationElement.IsSelected">
            <summary>
            Value to get/set if this element is selected.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.SelectionItemAutomationElement.Select">
            <summary>
            Selects the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.SelectionItemAutomationElement.AddToSelection">
            <summary>
            Adds the element to the selection.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.SelectionItemAutomationElement.RemoveFromSelection">
            <summary>
            Removes the element from the selection.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.PatternElements.ToggleAutomationElement">
            <summary>
            Class for an element that supports the <see cref="T:FlaUI.Core.Patterns.ITogglePattern"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.ToggleAutomationElement.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates an element with a <see cref="T:FlaUI.Core.Patterns.ITogglePattern"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.PatternElements.ToggleAutomationElement.TogglePattern">
            <summary>
            The toggle pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.PatternElements.ToggleAutomationElement.ToggleState">
            <summary>
            Gets or sets the current toggle state.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.PatternElements.ToggleAutomationElement.IsToggled">
            <summary>
            Gets or sets if the element is toggled.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.PatternElements.ToggleAutomationElement.Toggle">
            <summary>
            Toggles the element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.ProgressBar">
            <summary>
            Class to interact with a progressbar element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ProgressBar.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.ProgressBar"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ProgressBar.RangeValuePattern">
            <summary>
            Pattern object for the <see cref="T:FlaUI.Core.Patterns.IRangeValuePattern"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ProgressBar.Minimum">
            <summary>
            Gets the minimum value.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ProgressBar.Maximum">
            <summary>
            Gets the maximum value.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.ProgressBar.Value">
            <summary>
            Gets the current value.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.RadioButton">
            <summary>
            Class to interact with a radiobutton element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.RadioButton.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.RadioButton"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.RadioButton.SelectionItemPattern">
            <summary>
            Pattern object for the <see cref="T:FlaUI.Core.Patterns.ISelectionItemPattern"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.RadioButton.IsChecked">
            <summary>
            Flag to get/set the selection of this element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar">
            <summary>
            A horizontal scrollbar element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a horizontal scroll bar element from the given element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar.SmallDecrementText">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar.SmallIncrementText">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar.LargeDecrementText">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar.LargeIncrementText">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar.ScrollLeft">
            <summary>
            Scrolls left by a small amount.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar.ScrollRight">
            <summary>
            Scrolls right by a small amount.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar.ScrollLeftLarge">
            <summary>
            Scrolls left by a large amount.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.HorizontalScrollBar.ScrollRightLarge">
            <summary>
            Scrolls right by a large amount.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase">
            <summary>
            Base class for a scroll bar element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a scroll bar element from the given element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.RangeValuePattern">
            <summary>
            The <see cref="T:FlaUI.Core.Patterns.IRangeValuePattern"/> of this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.SmallDecrementButton">
            <summary>
            The <see cref="T:FlaUI.Core.AutomationElements.Button"/> used to scroll by a small decrement.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.SmallIncrementButton">
            <summary>
            The <see cref="T:FlaUI.Core.AutomationElements.Button"/> used to scroll by a small increment.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.LargeDecrementButton">
            <summary>
            The <see cref="T:FlaUI.Core.AutomationElements.Button"/> used to scroll by a large decrement.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.LargeIncrementButton">
            <summary>
            The <see cref="T:FlaUI.Core.AutomationElements.Button"/> used to scroll by a large increment.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.Thumb">
            <summary>
            The <see cref="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.Thumb"/> used to scroll with dragging.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.Value">
            <summary>
            The current value of the scroll.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.MinimumValue">
            <summary>
            The minimum value of the scroll.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.MaximumValue">
            <summary>
            The maximum value of the scroll.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.SmallChange">
            <summary>
            The small change value of the scroll.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.LargeChange">
            <summary>
            The large change value of the scroll.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.IsReadOnly">
            <summary>
            Value which indicates if the scroll is read only.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.SmallDecrementText">
            <summary>
            The text used to find the small decrement button.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.SmallIncrementText">
            <summary>
            The text used to find the small increment button.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.LargeDecrementText">
            <summary>
            The text used to find the large decrement button.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.ScrollBarBase.LargeIncrementText">
            <summary>
            The text used to find the large increment button.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar">
            <summary>
            A vertical scrollbar element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a vertical scroll bar element from the given element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar.SmallDecrementText">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar.SmallIncrementText">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar.LargeDecrementText">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar.LargeIncrementText">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar.ScrollUp">
            <summary>
            Scrolls up by a small amount.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar.ScrollDown">
            <summary>
            Scrolls down by a small amount.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar.ScrollUpLarge">
            <summary>
            Scrolls up by a large amount.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Scrolling.VerticalScrollBar.ScrollDownLarge">
            <summary>
            Scrolls down by a large amount.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Slider">
            <summary>
            Class to interact with a slider element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Slider.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.Slider"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Slider.Minimum">
            <summary>
            The minimum value.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Slider.Maximum">
            <summary>
            The maximum value.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Slider.SmallChange">
            <summary>
            The value of a small change.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Slider.LargeChange">
            <summary>
            The value of a large change.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Slider.LargeIncreaseButton">
            <summary>
            The button element used to perform a large increment.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Slider.LargeDecreaseButton">
            <summary>
            The button element used to perform a large decrement.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Slider.Thumb">
            <summary>
            The element used to drag.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Slider.IsOnlyValue">
            <summary>
            Flag which indicates if the <see cref="T:FlaUI.Core.AutomationElements.Slider"/> supports range values (min->max) or only values (0-100).
            Only values are for example used when combining UIA3 and WinForms applications.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Slider.Value">
            <summary>
            Gets or sets the current value.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Slider.SmallIncrement">
            <summary>
            Performs a small increment.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Slider.SmallDecrement">
            <summary>
            Performs a small decrement.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Slider.LargeIncrement">
            <summary>
            Performs a large increment.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Slider.LargeDecrement">
            <summary>
            Performs a large decrement.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Spinner">
            <summary>
            Class to interact with a WinForms spinner element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Spinner.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.Spinner"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Spinner.Minimum">
            <summary>
            The minimum value.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Spinner.Maximum">
            <summary>
            The maximum value.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Spinner.SmallChange">
            <summary>
            The value of a small change.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Spinner.IncreaseButton">
            <summary>
            The button element used to perform a large increment.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Spinner.DecreaseButton">
            <summary>
            The button element used to perform a large decrement.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Spinner.IsOnlyValue">
            <summary>
            Flag which indicates if the <see cref="T:FlaUI.Core.AutomationElements.Spinner"/> supports range values (min->max) or only values (0-100).
            Only values are for example used when combining UIA3 and WinForms applications.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Spinner.Value">
            <summary>
            Gets or sets the current value.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Spinner.Increment">
            <summary>
            Performs increment.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Spinner.Decrement">
            <summary>
            Performs decrement.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Spinner.GetIncreaseButton">
            <summary>
            Method to get the increase button.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Spinner.GetDecreaseButton">
            <summary>
            Method to get the decrease button.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Tab">
            <summary>
            Class to interact with a tab element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Tab.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.Tab"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Tab.SelectedTabItem">
            <summary>
            The currently selected <see cref="T:FlaUI.Core.AutomationElements.TabItem" />
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Tab.SelectedTabItemIndex">
            <summary>
            The index of the currently selected <see cref="T:FlaUI.Core.AutomationElements.TabItem" />
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Tab.TabItems">
            <summary>
            All <see cref="T:FlaUI.Core.AutomationElements.TabItem" /> objects from this <see cref="T:FlaUI.Core.AutomationElements.Tab" />
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Tab.SelectTabItem(System.Int32)">
            <summary>
            Selects a <see cref="T:FlaUI.Core.AutomationElements.TabItem" /> by index
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Tab.SelectTabItem(System.String)">
            <summary>
            Selects a <see cref="T:FlaUI.Core.AutomationElements.TabItem" /> by a give text (name property)
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Tab.GetTabItems">
            <summary>
            Gets all the <see cref="T:FlaUI.Core.AutomationElements.TabItem" /> objects for this <see cref="T:FlaUI.Core.AutomationElements.Tab" />
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.TabItem">
            <summary>
            Class to interact with a tabitem element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TabItem.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.TabItem"/> element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.TextBox">
            <summary>
            Class to interact with a textbox element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TextBox.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.TextBox"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TextBox.Text">
            <summary>
            Gets or sets the text of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TextBox.IsReadOnly">
            <summary>
            Gets if the element is read only or not.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TextBox.Enter(System.String)">
            <summary>
            Simulate typing in text. This is slower than setting <see cref="P:FlaUI.Core.AutomationElements.TextBox.Text"/> but raises more events.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Thumb">
            <summary>
            Class to interact with a thumb element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Thumb.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.Thumb"/> element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Thumb.SlideHorizontally(System.Int32)">
            <summary>
            Moves the slider horizontally.
            </summary>
            <param name="distance">The distance to move the slider, + for right, - for left.</param>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Thumb.SlideVertically(System.Int32)">
            <summary>
            Moves the slider vertically.
            </summary>
            <param name="distance">The distance to move the slider, + for down, - for up.</param>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.TitleBar">
            <summary>
            Class to interact with a titlebar element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TitleBar.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.TitleBar"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TitleBar.MinimizeButton">
            <summary>
            Gets the minimize button element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TitleBar.MaximizeButton">
            <summary>
            Gets the maximize button element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TitleBar.RestoreButton">
            <summary>
            Gets the restore button element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TitleBar.CloseButton">
            <summary>
            Gets the close button element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.ToggleButton">
            <summary>
            Class to interact with a toggle button element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ToggleButton.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.ToggleButton"/> element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.ToggleButton.Toggle">
            <summary>
            Toggles the toggle button.
            Note: In some WPF scenarios, the bounded command might not be fired. Use <see cref="M:FlaUI.Core.AutomationElements.AutomationElement.Click(System.Boolean)"/> instead in that case.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Tree">
            <summary>
            Class to interact with a tree element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Tree.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.Tree"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Tree.SelectedTreeItem">
            <summary>
            The currently selected <see cref="T:FlaUI.Core.AutomationElements.TreeItem" />
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Tree.Items">
            <summary>
            All child <see cref="T:FlaUI.Core.AutomationElements.TreeItem" /> objects from this <see cref="T:FlaUI.Core.AutomationElements.Tree" />
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Tree.GetTreeItems">
            <summary>
            Gets all the <see cref="T:FlaUI.Core.AutomationElements.TreeItem" /> objects for this <see cref="T:FlaUI.Core.AutomationElements.Tree" />
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.TreeItem">
            <summary>
            Class to interact with a treeitem element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TreeItem.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.TreeItem"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TreeItem.Items">
            <summary>
            All child <see cref="T:FlaUI.Core.AutomationElements.TreeItem" /> objects from this <see cref="T:FlaUI.Core.AutomationElements.TreeItem" />.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TreeItem.Text">
            <summary>
            The text of the <see cref="T:FlaUI.Core.AutomationElements.TreeItem" />.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TreeItem.IsSelected">
            <summary>
            Value to get/set if this element is selected.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TreeItem.ExpandCollapseState">
            <summary>
            Gets the current expand / collapse state.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TreeItem.Expand">
            <summary>
            Expands the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TreeItem.Collapse">
            <summary>
            Collapses the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TreeItem.Select">
            <summary>
            Selects the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TreeItem.AddToSelection">
            <summary>
            Add the element to the selection.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TreeItem.RemoveFromSelection">
            <summary>
            Remove the element from the selection.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.TreeItem.GetTreeItems">
            <summary>
            Gets all the <see cref="T:FlaUI.Core.AutomationElements.TreeItem" /> objects for this <see cref="T:FlaUI.Core.AutomationElements.TreeItem" />
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.TreeItem.IsChecked">
            <summary>
            Gets or sets if the tree item is checked, if checking is supported.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElements.Window">
            <summary>
            Class to interact with a window element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Window.#ctor(FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.AutomationElements.Window"/> element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Window.Title">
            <summary>
            Gets the title of the window.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Window.IsModal">
            <summary>
            Gets if the window is modal.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Window.TitleBar">
            <summary>
            Gets the <see cref="P:FlaUI.Core.AutomationElements.Window.TitleBar"/> of the window.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Window.IsMainWindow">
            <summary>
            Flag to indicate, if the window is the application's main window.
            Is used so that it does not need to be looked up again in some cases (e.g. Context Menu).
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Window.ModalWindows">
            <summary>
            Gets a list of all modal child windows.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Window.Popup">
            <summary>
            Gets the current WPF popup window.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationElements.Window.ContextMenu">
            <summary>
            Gets the context menu for the window.
            Note: It uses the FrameworkType of the window as lookup logic. Use <see cref="M:FlaUI.Core.AutomationElements.Window.GetContextMenuByFrameworkType(FlaUI.Core.FrameworkType)" /> if you want to control this.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Window.GetContextMenuByFrameworkType(FlaUI.Core.FrameworkType)">
            <summary>
            Gets the context menu by a given <see cref="T:FlaUI.Core.FrameworkType"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Window.Close">
            <summary>
            Closes the window.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Window.Move(System.Int32,System.Int32)">
            <summary>
            Moves the window to the given coordinates.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Window.SetTransparency(System.Byte)">
            <summary>
            Brings the element to the foreground.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElements.Window.GetMainWindow">
            <summary>
            Gets the main window (first window on desktop with the same process as this window).
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationElementXPathNavigator">
            <summary>
            Custom implementation of a <see cref="T:System.Xml.XPath.XPathNavigator" /> which allows
            selecting items by xpath by using the <see cref="T:FlaUI.Core.ITreeWalker" />.
            </summary>
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.#ctor(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Creates a new XPath navigator which uses the given element as the root.
            </summary>
            <param name="rootElement">The element to use as root element.</param>
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.HasAttributes">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.Value">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.UnderlyingObject">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.NodeType">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.LocalName">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.Name">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.NameTable">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.NamespaceURI">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.Prefix">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.BaseURI">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationElementXPathNavigator.IsEmptyElement">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.Clone">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToFirstAttribute">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToNextAttribute">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.GetAttribute(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToAttribute(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToFirstNamespace(System.Xml.XPath.XPathNamespaceScope)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToNextNamespace(System.Xml.XPath.XPathNamespaceScope)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToRoot">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToNext">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToPrevious">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToFirstChild">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToParent">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveTo(System.Xml.XPath.XPathNavigator)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.MoveToId(System.String)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationElementXPathNavigator.IsSamePosition(System.Xml.XPath.XPathNavigator)">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.IAutomationPattern`1">
            <summary>
            Interface for an automation pattern object.
            </summary>
            <typeparam name="T">The type of the pattern.</typeparam>
        </member>
        <member name="P:FlaUI.Core.IAutomationPattern`1.Pattern">
            <summary>
            Gets the pattern. Throws if the pattern is not supported.
            </summary>
        </member>
        <member name="P:FlaUI.Core.IAutomationPattern`1.PatternOrDefault">
            <summary>
            Gets the pattern or null if it is not supported.
            </summary>
        </member>
        <member name="M:FlaUI.Core.IAutomationPattern`1.TryGetPattern(`0@)">
            <summary>
            Tries getting the pattern.
            </summary>
            <param name="pattern">The found pattern or null if it is not supported.</param>
            <returns>True if the pattern is supported, false otherwise.</returns>
        </member>
        <member name="P:FlaUI.Core.IAutomationPattern`1.IsSupported">
            <summary>
            Gets a boolean value which indicates, if the pattern is supported.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationPattern`2">
            <summary>
            Automation pattern object which is used to get automation patterns.
            </summary>
            <typeparam name="T">The type of the pattern.</typeparam>
            <typeparam name="TNative">The type of the native pattern.</typeparam>
        </member>
        <member name="M:FlaUI.Core.AutomationPattern`2.#ctor(FlaUI.Core.Identifiers.PatternId,FlaUI.Core.FrameworkAutomationElementBase,System.Func{FlaUI.Core.FrameworkAutomationElementBase,`1,`0})">
            <summary>
            Creates a new pattern object.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationPattern`2.FrameworkAutomationElement">
            <summary>
            The element which owns this pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationPattern`2.Pattern">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationPattern`2.PatternOrDefault">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationPattern`2.TryGetPattern(`0@)">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationPattern`2.IsSupported">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.IAutomationProperty`1">
            <summary>
            Inferface for property objects.
            </summary>
            <typeparam name="T">The type of the value of the property.</typeparam>
        </member>
        <member name="P:FlaUI.Core.IAutomationProperty`1.Value">
            <summary>
            Get the value of the property. Throws if the property is not supported or
            if it is accessed in a caching context and it is not cached.
            </summary>
        </member>
        <member name="P:FlaUI.Core.IAutomationProperty`1.ValueOrDefault">
            <summary>
            Gets the value of the property or the default for this property type if it is not supported.
            Throws if the property is accessed in a caching context and it is not cached.
            </summary>
        </member>
        <member name="M:FlaUI.Core.IAutomationProperty`1.TryGetValue(`0@)">
            <summary>
            Tries to get the value of the property.
            Throws if the property is accessed in a caching context and it is not cached.
            </summary>
            <param name="value">The value of the property. Contains the default if it is not supported.</param>
            <returns>True if the property is supported, false otherwise.</returns>
        </member>
        <member name="P:FlaUI.Core.IAutomationProperty`1.IsSupported">
            <summary>
            Gets a flag if the property is supported or not.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationProperty`1">
            <summary>
            Implementation of the property object.
            </summary>
            <typeparam name="TVal">The type of the value of the property.</typeparam>
        </member>
        <member name="M:FlaUI.Core.AutomationProperty`1.#ctor(FlaUI.Core.Identifiers.PropertyId,FlaUI.Core.FrameworkAutomationElementBase)">
            <summary>
            Create the property object.
            </summary>
            <param name="propertyId">The <see cref="P:FlaUI.Core.AutomationProperty`1.PropertyId"/> for this property object.</param>
            <param name="frameworkAutomationElement">The <see cref="P:FlaUI.Core.AutomationProperty`1.FrameworkAutomationElement"/> for this property object.</param>
        </member>
        <member name="P:FlaUI.Core.AutomationProperty`1.PropertyId">
            <summary>
            The <see cref="P:FlaUI.Core.AutomationProperty`1.PropertyId"/> of this property object.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationProperty`1.FrameworkAutomationElement">
            <summary>
            The <see cref="P:FlaUI.Core.AutomationProperty`1.FrameworkAutomationElement"/> where this property object belongs to.
            </summary>
        </member>
        <member name="P:FlaUI.Core.AutomationProperty`1.Value">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationProperty`1.ValueOrDefault">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationProperty`1.TryGetValue(`0@)">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.AutomationProperty`1.IsSupported">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.AutomationProperty`1.op_Implicit(FlaUI.Core.AutomationProperty{`0})~`0">
            <summary>
            Implicit operator to convert the property object directly to its value.
            </summary>
            <param name="automationProperty">The property object which should be converted.</param>
        </member>
        <member name="M:FlaUI.Core.AutomationProperty`1.Equals(`0)">
            <summary>
            Compares the value to another value.
            </summary>
            <param name="other">The other value.</param>
            <returns>True if they equal, false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationProperty`1.Equals(FlaUI.Core.AutomationProperty{`0})">
            <summary>
            Compares this property with another property.
            </summary>
            <param name="other">The other property.</param>
            <returns>True if they are value-equal, false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.AutomationProperty`1.ToString">
            <summary>
            Returns the value in a readable format.
            </summary>
        </member>
        <member name="T:FlaUI.Core.AutomationType">
            <summary>
            Defines which version of UIAutomation should be used.
            </summary>
        </member>
        <member name="F:FlaUI.Core.AutomationType.UIA2">
            <summary>
            Use UIA2.
            </summary>
        </member>
        <member name="F:FlaUI.Core.AutomationType.UIA3">
            <summary>
            Use UIA3.
            </summary>
        </member>
        <member name="T:FlaUI.Core.CacheRequest">
            <summary>
            A class which handles the cache requests.
            </summary>
        </member>
        <member name="P:FlaUI.Core.CacheRequest.AutomationElementMode">
            <summary>
            Defines the reference mode of automation elements in the cache.
            </summary>
        </member>
        <member name="P:FlaUI.Core.CacheRequest.TreeFilter">
            <summary>
            Defines the tree filter that is used to filter the items to cache.
            </summary>
        </member>
        <member name="P:FlaUI.Core.CacheRequest.TreeScope">
            <summary>
            The tree scope used for searching items for caching.
            </summary>
        </member>
        <member name="P:FlaUI.Core.CacheRequest.Patterns">
            <summary>
            The list of patterns to cache.
            </summary>
        </member>
        <member name="P:FlaUI.Core.CacheRequest.Properties">
            <summary>
            The list of properties to cache.
            </summary>
        </member>
        <member name="M:FlaUI.Core.CacheRequest.Add(FlaUI.Core.Identifiers.PatternId)">
            <summary>
            Adds a pattern to the list of patterns to cache.
            </summary>
        </member>
        <member name="M:FlaUI.Core.CacheRequest.Add(FlaUI.Core.Identifiers.PropertyId)">
            <summary>
            Adds a property to the list of properties to cache.
            </summary>
        </member>
        <member name="M:FlaUI.Core.CacheRequest.Activate">
            <summary>
            Activate the cache request.
            </summary>
        </member>
        <member name="P:FlaUI.Core.CacheRequest.IsCachingActive">
            <summary>
            Checks if a caching is currently active in the current context.
            </summary>
        </member>
        <member name="P:FlaUI.Core.CacheRequest.Current">
            <summary>
            Gets the current cache request object.
            </summary>
        </member>
        <member name="M:FlaUI.Core.CacheRequest.Push(FlaUI.Core.CacheRequest)">
            <summary>
            Pushes a stack request onto the stack.
            </summary>
        </member>
        <member name="M:FlaUI.Core.CacheRequest.Pop">
            <summary>
            Pops a cache request from the stack.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Capturing.Capture">
            <summary>
            Provides methods to capture the screen, <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>s or rectangles on them.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.Capture.MainScreen(FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Captures the main (primary) screen.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.Capture.Screen(System.Int32,FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Captures the whole screen (all monitors).
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.Capture.ScreensWithElement(FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Captures all screens an element is on.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.Capture.Element(FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Captures an element and returns the image.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.Capture.ElementRectangle(FlaUI.Core.AutomationElements.AutomationElement,System.Drawing.Rectangle,FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Captures a rectangle inside an element and returns the image.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.Capture.Rectangle(System.Drawing.Rectangle,FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Captures a specific area from the screen.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Capturing.CaptureImage">
            <summary>
            Object which is returned when the screen or parts of the screen are captured with <see cref="T:FlaUI.Core.Capturing.Capture"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.CaptureImage.#ctor(System.Drawing.Bitmap,System.Drawing.Rectangle,FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.Capturing.CaptureImage"/> object with the given <see cref="P:FlaUI.Core.Capturing.CaptureImage.Bitmap"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.CaptureImage.Bitmap">
            <summary>
            The original <see cref="P:FlaUI.Core.Capturing.CaptureImage.Bitmap"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.CaptureImage.OriginalBounds">
            <summary>
            The original bounding rectangle (relative to the whole desktop) that this image is based on.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.CaptureImage.Settings">
            <summary>
            The <see cref="T:FlaUI.Core.Capturing.CaptureSettings"/> used to capture the image.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.CaptureImage.ToFile(System.String)">
            <summary>
            Saves the image to the file with the given path.
            Uses the file extension as format, defaults to <see cref="P:System.Drawing.Imaging.ImageFormat.Png"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.CaptureImage.ApplyOverlays(FlaUI.Core.Capturing.ICaptureOverlay[])">
            <summary>
            Applies all the given overlays onto the image.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.CaptureImage.Dispose">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Capturing.CaptureSettings">
            <summary>
            Class with various settings for capturing images.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.CaptureSettings.OutputWidth">
            <summary>
            The width of the output. Set to -1 to scale it in aspect ratio to the <see cref="P:FlaUI.Core.Capturing.CaptureSettings.OutputHeight"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.CaptureSettings.OutputHeight">
            <summary>
            The height of the output. Set to -1 to scale it in aspect ratio to the <see cref="P:FlaUI.Core.Capturing.CaptureSettings.OutputWidth"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.CaptureSettings.OutputScale">
            <summary>
            The scale of the output (1 == 100%).
            </summary>
        </member>
        <member name="T:FlaUI.Core.Capturing.CaptureUtilities">
            <summary>
            Various utility methods used for capturing.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.CaptureUtilities.GetScale(System.Drawing.Rectangle,FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Calculates a scale factor according to the bounds and capture settings.
            </summary>
            <param name="originalBounds">The original bounds of the captured image.</param>
            <param name="captureSettings">The settings to use for the capture.</param>
            <returns>A scale factor, defaults to 1 which means original size.</returns>
        </member>
        <member name="M:FlaUI.Core.Capturing.CaptureUtilities.ScaleAccordingToSettings(System.Int32,System.Int32,System.Drawing.Rectangle,FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Scales a point according to the given settings.
            </summary>
            <param name="x">The x-position of the point to scale.</param>
            <param name="y">The y-position of the pint to scale.</param>
            <param name="originalBounds">The original bounds of the captured image.</param>
            <param name="captureSettings">The settings to use for the capture.</param>
            <returns>The transformed point.</returns>
        </member>
        <member name="M:FlaUI.Core.Capturing.CaptureUtilities.ScaleAccordingToSettings(System.Drawing.Point,System.Drawing.Rectangle,FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Scales a point according to the given settings.
            </summary>
            <param name="point">The point to scale.</param>
            <param name="originalBounds">The original bounds of the captured image.</param>
            <param name="captureSettings">The settings to use for the capture.</param>
            <returns>The transformed point.</returns>
        </member>
        <member name="M:FlaUI.Core.Capturing.CaptureUtilities.ScaleAccordingToSettings(System.Drawing.Rectangle,FlaUI.Core.Capturing.CaptureSettings)">
            <summary>
            Scales a rectangle according to the given settings.
            </summary>
            <param name="originalBounds">The original bounds of the captured image.</param>
            <param name="captureSettings">The settings to use for the capture.</param>
            <returns>The transformed rectangle.</returns>
        </member>
        <member name="M:FlaUI.Core.Capturing.CaptureUtilities.CaptureCursor(System.Drawing.Point@)">
            <summary>
            Captures the cursor as bitmap and returns the bitmap and the position on screen of the cursor.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Capturing.ICaptureOverlay">
            <summary>
            Interface for overlays that can be applied to captured images.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.ICaptureOverlay.Draw(System.Drawing.Graphics)">
            <summary>
            Draws the overlay onto the given graphics object.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Capturing.InfoOverlay">
            <summary>
            Overlay with various information about the system.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.InfoOverlay.#ctor(FlaUI.Core.Capturing.CaptureImage)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.Capturing.InfoOverlay"/> object for the current captured image.
            </summary>
            <param name="captureImage">The captured image.</param>
            <returns>The created object.</returns>
        </member>
        <member name="P:FlaUI.Core.Capturing.InfoOverlay.OverlayStringFormat">
            <summary>
            The string to use for the overlay. Has some variables which are automatically replaced.
            The variables are:
            - dt: The current systems datetime, can additionally be followed with a .net format string.
            - rt: The timespan since the recording started See also <see cref="P:FlaUI.Core.Capturing.InfoOverlay.RecordTimeSpan"/>.
            - name: The machine name of the current system.
            - cpu: The cpu usage.
            - mem.p.tot: The physical total memory.
            - mem.p.free: The physical free memory.
            - mem.p.used: The physical used memory.
            - mem.p.free.perc: The physical free memory in percent.
            - mem.p.used.perc: The physical used memory in percent.
            - mem.v.tot: The virtual total memory.
            - mem.v.free: The virtual free memory.
            - mem.v.used: The virtual used memory.
            - mem.v.free.perc: The virtual free memory in percent.
            - mem.v.used.perc: The virtual used memory in percent.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.InfoOverlay.OverlayPosition">
            <summary>
            The position of the overlay.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.InfoOverlay.OverlayBackgroundColor">
            <summary>
            The color of the overlay background (can also have an alpha for semi transparent).
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.InfoOverlay.OverlayTextColor">
            <summary>
            The color of the overlay text.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.InfoOverlay.OverlayTextFont">
            <summary>
            The font of the overlay text.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.InfoOverlay.RecordTimeSpan">
            <summary>
            The timespan for the recorded time.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.InfoOverlay.Draw(System.Drawing.Graphics)">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Capturing.InfoOverlayPosition">
            <summary>
            Defines the position of the info overlay.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Capturing.InfoOverlayPosition.TopLeft">
            <summary>
            The overlay is displayed on top-left.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Capturing.InfoOverlayPosition.TopRight">
            <summary>
            The overlay is displayed on top-right.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Capturing.InfoOverlayPosition.TopCenter">
            <summary>
            The overlay is displayed on top-center.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Capturing.InfoOverlayPosition.BottomLeft">
            <summary>
            The overlay is displayed on bottom-left.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Capturing.InfoOverlayPosition.BottomRight">
            <summary>
            The overlay is displayed on bottom-left.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Capturing.InfoOverlayPosition.BottomCenter">
            <summary>
            The overlay is displayed on bottom-left.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Capturing.MouseOverlay">
            <summary>
            An overlay to draw the current mouse cursor.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.MouseOverlay.#ctor(FlaUI.Core.Capturing.CaptureImage)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.Capturing.MouseOverlay"/> object for the current captured image.
            </summary>
            <param name="captureImage">The captured image.</param>
            <returns>The created object.</returns>
        </member>
        <member name="M:FlaUI.Core.Capturing.MouseOverlay.Draw(System.Drawing.Graphics)">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Capturing.OverlayBase">
            <summary>
            Base class for overlays.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.OverlayBase.#ctor(FlaUI.Core.Capturing.CaptureImage)">
            <summary>
            Base constructor for an object inheriting from <see cref="T:FlaUI.Core.Capturing.OverlayBase"/>.
            </summary>
            <param name="captureImage">The captured image.</param>
        </member>
        <member name="P:FlaUI.Core.Capturing.OverlayBase.CaptureImage">
            <summary>
            The captured image where this overlay should be painted.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.OverlayBase.Draw(System.Drawing.Graphics)">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Capturing.VideoFormat">
            <summary>
            Defines the video format that should be used for recording.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Capturing.VideoFormat.x264">
            <summary>
            Small file size, high cpu usage.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Capturing.VideoFormat.xvid">
            <summary>
            Medium file size, low cpu usage.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Capturing.VideoRecorder">
            <summary>
            A video recorder which records the captured images into a video file.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.VideoRecorder.#ctor(FlaUI.Core.Capturing.VideoRecorderSettings,System.Func{FlaUI.Core.Capturing.VideoRecorder,FlaUI.Core.Capturing.CaptureImage})">
            <summary>
            Creates the video recorder and starts recording.
            </summary>
            <param name="settings">The settings used for the recorder.</param>
            <param name="captureMethod">The method used for capturing the image which is recorder.</param>
        </member>
        <member name="P:FlaUI.Core.Capturing.VideoRecorder.TargetVideoPath">
            <summary>
            The path of the video file.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.VideoRecorder.RecordTimeSpan">
            <summary>
            The time since the recording started.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.VideoRecorder.Start">
            <summary>
            Starts recording.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Capturing.VideoRecorder.Stop">
            <summary>
            Stops recording and finishes the video file.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Capturing.VideoRecorderSettings">
            <summary>
            Settings class for the <see cref="T:FlaUI.Core.Capturing.VideoRecorder"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.VideoRecorderSettings.ffmpegPath">
            <summary>
            The path to ffmpeg.exe.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.VideoRecorderSettings.FrameRate">
            <summary>
            The framerate used for capturing and playback.
            Defaults to 5.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.VideoRecorderSettings.TargetVideoPath">
            <summary>
            The path to the target video file.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.VideoRecorderSettings.UseCompressedImages">
            <summary>
            Flag to indicate if compressed images should be captured.<para />
            Helps if the recording machine is limited on memory.<para />
            Defaults to true.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.VideoRecorderSettings.VideoFormat">
            <summary>
            Defines the video format that is used to record the video.<para />
            This has an influence on the file size and the cpu load during recording.<para />
            Defaults to <see cref="F:FlaUI.Core.Capturing.VideoFormat.x264"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.VideoRecorderSettings.VideoQuality">
            <summary>
            An integer defining the quality of the video. The value is dependent on the <see cref="P:FlaUI.Core.Capturing.VideoRecorderSettings.VideoFormat"/>.<para />
            <see cref="F:FlaUI.Core.Capturing.VideoFormat.x264"/>: From 0 (lossless) to 51 (worst). Sane values are from 18 to 28.<para />
            <see cref="F:FlaUI.Core.Capturing.VideoFormat.xvid"/>: From 1 (lossless) to 31 (worst). Sane values are around 5.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.VideoRecorderSettings.EncodeWithLowPriority">
            <summary>
            Run the encoding with low processor priority.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Capturing.VideoRecorderSettings.LogMissingFrames">
            <summary>
            If to log warning when recorder detects that there were missing frames. Default is <c>true</c>
            </summary>
        </member>
        <member name="T:FlaUI.Core.Conditions.AndCondition">
            <summary>
            A condition used to group two conditions together with an "and".
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.AndCondition.#ctor(FlaUI.Core.Conditions.ConditionBase,FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Creates a new <see cref="T:FlaUI.Core.Conditions.AndCondition" /> based on two conditions.
            </summary>
            <param name="condition1">The first condition.</param>
            <param name="condition2">The second condition.</param>
        </member>
        <member name="M:FlaUI.Core.Conditions.AndCondition.#ctor(System.Collections.Generic.IEnumerable{FlaUI.Core.Conditions.ConditionBase})">
            <summary>
            Creates a new <see cref="T:FlaUI.Core.Conditions.AndCondition" /> based on a list of conditions.
            </summary>
            <param name="conditions">The list of conditions.</param>
        </member>
        <member name="M:FlaUI.Core.Conditions.AndCondition.#ctor(FlaUI.Core.Conditions.ConditionBase[])">
            <summary>
            Creates a new <see cref="T:FlaUI.Core.Conditions.AndCondition" /> based on a list of conditions.
            </summary>
            <param name="conditions">The list of conditions.</param>
        </member>
        <member name="P:FlaUI.Core.Conditions.AndCondition.JunctionOperator">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Conditions.BoolCondition">
            <summary>
            Represents a boolean condition.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.BoolCondition.#ctor(System.Boolean)">
            <summary>
            Creates a new instance of a boolean condition with the given boolean value.
            </summary>
            <param name="booleanValue">The boolean value of this condition.</param>
        </member>
        <member name="P:FlaUI.Core.Conditions.BoolCondition.BooleanValue">
            <summary>
            Gets the boolean value for this condition.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.BoolCondition.ToString">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Conditions.TrueCondition">
            <summary>
            A boolean condition that has is "true".
            </summary>
        </member>
        <member name="P:FlaUI.Core.Conditions.TrueCondition.Default">
            <summary>
            The default instance for a <see cref="T:FlaUI.Core.Conditions.TrueCondition"/>.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Conditions.FalseCondition">
            <summary>
            A boolean condition that has is "false".
            </summary>
        </member>
        <member name="P:FlaUI.Core.Conditions.FalseCondition.Default">
            <summary>
            The default instance for a <see cref="T:FlaUI.Core.Conditions.FalseCondition"/>.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Conditions.ConditionBase">
            <summary>
            Base class for a condition.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionBase.And(FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Adds the given condition with an "and".
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionBase.Or(FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Adds the given condition with an "or".
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionBase.Not">
            <summary>
            Packs this condition into a not condition.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Conditions.ConditionFactory">
            <summary>
            Helper class with some commonly used conditions.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.#ctor(FlaUI.Core.IPropertyLibrary)">
            <summary>
            Creates a <see cref="T:FlaUI.Core.Conditions.ConditionFactory"/> with the given <see cref="T:FlaUI.Core.IPropertyLibrary"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByAutomationId(System.String,FlaUI.Core.Definitions.PropertyConditionFlags)">
            <summary>
            Creates a condition to search by an automation id.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByControlType(FlaUI.Core.Definitions.ControlType)">
            <summary>
            Creates a condition to search by a <see cref="T:FlaUI.Core.Definitions.ControlType"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByClassName(System.String,FlaUI.Core.Definitions.PropertyConditionFlags)">
            <summary>
            Creates a condition to search by a class name.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByName(System.String,FlaUI.Core.Definitions.PropertyConditionFlags)">
            <summary>
            Creates a condition to search by a name.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByText(System.String,FlaUI.Core.Definitions.PropertyConditionFlags)">
            <summary>
            Creates a condition to search by a text (same as <see cref="M:FlaUI.Core.Conditions.ConditionFactory.ByName(System.String,FlaUI.Core.Definitions.PropertyConditionFlags)"/>).
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByFrameworkId(System.String,FlaUI.Core.Definitions.PropertyConditionFlags)">
            <summary>
            Creates a condition to search by a Framework Id.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByFrameworkType(FlaUI.Core.FrameworkType)">
            <summary>
            Creates a condition to search by a Framework Type.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByProcessId(System.Int32)">
            <summary>
            Creates a condition to search by a process id.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByLocalizedControlType(System.String,FlaUI.Core.Definitions.PropertyConditionFlags)">
            <summary>
            Creates a condition to search by a localized control type.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByHelpText(System.String,FlaUI.Core.Definitions.PropertyConditionFlags)">
            <summary>
            Creates a condition to search by a help text.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.ByValue(System.String,FlaUI.Core.Definitions.PropertyConditionFlags)">
            <summary>
            Creates a condition to search by a value.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.Menu">
            <summary>
            Searches for a Menu/MenuBar.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.Grid">
            <summary>
            Searches for a DataGrid/List.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.HorizontalScrollBar">
            <summary>
            Searches for a horizontal scrollbar.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.ConditionFactory.VerticalScrollBar">
            <summary>
            Searches for a vertical scrollbar.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Conditions.JunctionConditionBase">
            <summary>
            Base class for a junction condition.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.JunctionConditionBase.#ctor">
            <summary>
            Creates a new instance of a junction condition.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.JunctionConditionBase.#ctor(System.Collections.Generic.IEnumerable{FlaUI.Core.Conditions.ConditionBase})">
            <summary>
            Creates a new instance of a junction condition and adds the given conditions.
            </summary>
            <param name="conditions">The conditions to add to the junction.</param>
        </member>
        <member name="P:FlaUI.Core.Conditions.JunctionConditionBase.Conditions">
            <summary>
            Returns the inner conditions.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Conditions.JunctionConditionBase.ChildCount">
            <summary>
            Gets the number of conditions in this junction condition.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Conditions.JunctionConditionBase.JunctionOperator">
            <summary>
            Gets the operator used for the junction.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.JunctionConditionBase.ToString">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Conditions.NotCondition">
            <summary>
            A condition that negates a given condition.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Conditions.NotCondition.Condition">
            <summary>
            Gets the inner condition that should be negated.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.NotCondition.#ctor(FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Creates a new instance of a <see cref="T:FlaUI.Core.Conditions.NotCondition"/> which negates the given condition.
            </summary>
            <param name="condition">The condition that should be negated.</param>
        </member>
        <member name="M:FlaUI.Core.Conditions.NotCondition.ToString">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Conditions.OrCondition">
            <summary>
            A condition used to group two conditions together with an "or".
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.OrCondition.#ctor(FlaUI.Core.Conditions.ConditionBase,FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Creates a new <see cref="T:FlaUI.Core.Conditions.OrCondition" /> based on two conditions.
            </summary>
            <param name="condition1">The first condition.</param>
            <param name="condition2">The second condition.</param>
        </member>
        <member name="M:FlaUI.Core.Conditions.OrCondition.#ctor(System.Collections.Generic.IEnumerable{FlaUI.Core.Conditions.ConditionBase})">
            <summary>
            Creates a new <see cref="T:FlaUI.Core.Conditions.OrCondition" /> based on a list of conditions.
            </summary>
            <param name="conditions">The list of conditions.</param>
        </member>
        <member name="M:FlaUI.Core.Conditions.OrCondition.#ctor(FlaUI.Core.Conditions.ConditionBase[])">
            <summary>
            Creates a new <see cref="T:FlaUI.Core.Conditions.OrCondition" /> based on a list of conditions.
            </summary>
            <param name="conditions">The list of conditions.</param>
        </member>
        <member name="P:FlaUI.Core.Conditions.OrCondition.JunctionOperator">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Conditions.PropertyCondition">
            <summary>
            A condition that is used to check for a property of an element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.PropertyCondition.#ctor(FlaUI.Core.Identifiers.PropertyId,System.Object)">
            <summary>
            Creates a new instance off a <see cref="T:FlaUI.Core.Conditions.PropertyCondition"/>.
            </summary>
            <param name="property">The property to check.</param>
            <param name="value">The value to check the property for.</param>
        </member>
        <member name="M:FlaUI.Core.Conditions.PropertyCondition.#ctor(FlaUI.Core.Identifiers.PropertyId,System.Object,FlaUI.Core.Definitions.PropertyConditionFlags)">
            <summary>
            Creates a new instance off a <see cref="T:FlaUI.Core.Conditions.PropertyCondition"/>.
            </summary>
            <param name="property">The property to check.</param>
            <param name="value">The value to check the property for.</param>
            <param name="propertyConditionFlags">The flags to use when checking the property.</param>
        </member>
        <member name="P:FlaUI.Core.Conditions.PropertyCondition.Property">
            <summary>
            The property that should be checked.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Conditions.PropertyCondition.PropertyConditionFlags">
            <summary>
            Optional flags that are used when checking the property.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Conditions.PropertyCondition.Value">
            <summary>
            The value that is used for checking.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Conditions.PropertyCondition.ToString">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Debug">
            <summary>
            Provides methods which can help in debugging.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Debug.GetXPathToElement(FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Gets the XPath to the element until the desktop or the given root element.
            Warning: This is quite a heavy operation
            </summary>
        </member>
        <member name="M:FlaUI.Core.Debug.Details(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Prints out various details about the given element (including children).
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.AnnotationType">
            <summary>
            Types of annotations in a document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.AdvancedProofingIssue">
            <summary>
            An advanced proofing issue.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.Author">
            <summary>
            The author of the document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.CircularReferenceError">
            <summary>
            A circular reference error that occurred.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.Comment">
            <summary>
            A comment. Comments can take different forms depending on the application.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.ConflictingChange">
            <summary>
            A conflicting change that was made to the document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.DataValidationError">
            <summary>
            A data validation error that occurred.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.DeletionChange">
            <summary>
            A deletion change that was made to the document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.EditingLockedChange">
            <summary>
            An editing locked change that was made to the document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.Endnote">
            <summary>
            The endnote for a document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.ExternalChange">
            <summary>
            An external change that was made to the document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.Footer">
            <summary>
            The footer for a page in a document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.Footnote">
            <summary>
            The footnote for a page in a document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.FormatChange">
            <summary>
            A format change that was made.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.FormulaError">
            <summary>
            An error in a formula. Formula errors typically include red text and exclamation marks.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.GrammarError">
            <summary>
            A grammatical error, often denoted by a green squiggly line.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.Header">
            <summary>
            The header for a page in a document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.Highlighted">
            <summary>
            Highlighted content, typically denoted by a contrasting background color.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.InsertionChange">
            <summary>
            An insertion change that was made to the document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.Mathematics">
            <summary>
            A text range containing mathematics.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.MoveChange">
            <summary>
            A move change that was made to the document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.Sensitive">
            <summary>
            (Sensitive content).
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.SpellingError">
            <summary>
            A spelling error, often denoted by a red squiggly line.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.TrackChanges">
            <summary>
            A change that was made to the document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.Unknown">
            <summary>
            The annotation type is unknown.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AnnotationType.UnsyncedChange">
            <summary>
            An unsynced change that was made to the document.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.AutomationElementMode">
            <summary>
            Contains values that specify the type of reference to use when returning UI Automation elements. 
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AutomationElementMode.None">
            <summary>
            Specifies that returned elements have no reference to the underlying UI and contain only cached information.
            This mode might be used, for example, to retrieve the names of items in a list box without obtaining references to the items themselves.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.AutomationElementMode.Full">
            <summary>
            Specifies that returned elements have a full reference to the underlying UI. 
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.CoalesceEventsOptions">
            <summary>
            Contains possible values for the CoalesceEvents property,
            which indicates whether an accessible technology client receives all events,
            or a subset where duplicate events are detected and filtered.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.CoalesceEventsOptions.Disabled">
            <summary>
            Event coalescing is disabled.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.CoalesceEventsOptions.Enabled">
            <summary>
            Event coalescing is enabled.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.ConnectionRecoveryBehaviorOptions">
            <summary>
            Contains possible values for the ConnectionRecoveryBehavior property,
            which indicates whether an accessible technology client adjusts provider
            request timeouts when the provider is non-responsive.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ConnectionRecoveryBehaviorOptions.Disabled">
            <summary>
            Connection recovery is disabled.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ConnectionRecoveryBehaviorOptions.Enabled">
            <summary>
            Connection recovery is enabled.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.ControlType">
            <summary>
            Types of controls in Microsoft UI Automation.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Unknown">
            <summary>
            Identifies an unknown control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.AppBar">
            <summary>
            Identifies the AppBar control type. Supported starting with Windows 8.1.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Button">
            <summary>
            Identifies the Button control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Calendar">
            <summary>
            Identifies the Calendar control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.CheckBox">
            <summary>
            Identifies the CheckBox control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.ComboBox">
            <summary>
            Identifies the ComboBox control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Custom">
            <summary>
            Identifies the Custom control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.DataGrid">
            <summary>
            Identifies the DataGrid control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.DataItem">
            <summary>
            Identifies the DataItem control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Document">
            <summary>
            Identifies the Document control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Edit">
            <summary>
            Identifies the Edit control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Group">
            <summary>
            Identifies the Group control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Header">
            <summary>
            Identifies the Header control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.HeaderItem">
            <summary>
            Identifies the HeaderItem control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Hyperlink">
            <summary>
            Identifies the Hyperlink control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Image">
            <summary>
            Identifies the Image control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.List">
            <summary>
            Identifies the List control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.ListItem">
            <summary>
            Identifies the ListItem control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.MenuBar">
            <summary>
            Identifies the MenuBar control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Menu">
            <summary>
            Identifies the Menu control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.MenuItem">
            <summary>
            Identifies the MenuItem control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Pane">
            <summary>
            Identifies the Pane control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.ProgressBar">
            <summary>
            Identifies the ProgressBar control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.RadioButton">
            <summary>
            Identifies the RadioButton control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.ScrollBar">
            <summary>
            Identifies the ScrollBar control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.SemanticZoom">
            <summary>
            Identifies the SemanticZoom control type. Supported starting with Windows 8.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Separator">
            <summary>
            Identifies the Separator control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Slider">
            <summary>
            Identifies the Slider control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Spinner">
            <summary>
            Identifies the Spinner control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.SplitButton">
            <summary>
            Identifies the SplitButton control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.StatusBar">
            <summary>
            Identifies the StatusBar control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Tab">
            <summary>
            Identifies the Tab control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.TabItem">
            <summary>
            Identifies the TabItem control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Table">
            <summary>
            Identifies the Table control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Text">
            <summary>
            Identifies the Text control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Thumb">
            <summary>
            Identifies the Thumb control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.TitleBar">
            <summary>
            Identifies the TitleBar control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.ToolBar">
            <summary>
            Identifies the ToolBar control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.ToolTip">
            <summary>
            Identifies the ToolTip control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Tree">
            <summary>
            Identifies the Tree control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.TreeItem">
            <summary>
            Identifies the TreeItem control type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ControlType.Window">
            <summary>
            Identifies the Window control type.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.DockPosition">
            <summary>
            Contains values that specify the dock position of an object, represented by a <see cref="T:FlaUI.Core.Patterns.IDockPattern"/>, within a docking container.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.DockPosition.Top">
            <summary>
            The window is docked at the top.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.DockPosition.Left">
            <summary>
            The window is docked at the left.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.DockPosition.Bottom">
            <summary>
            The window is docked at the bottom.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.DockPosition.Right">
            <summary>
            The window is docked at the right.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.DockPosition.Fill">
            <summary>
            The window is docked on all four sides.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.DockPosition.None">
            <summary>
            The window is not docked.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.ExpandCollapseState">
            <summary>
            Contains values that specify the state of a UI element that can be expanded and collapsed.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ExpandCollapseState.Collapsed">
            <summary>
            No children are visible.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ExpandCollapseState.Expanded">
            <summary>
            All children are visible.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ExpandCollapseState.PartiallyExpanded">
            <summary>
            Some, but not all, children are visible.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ExpandCollapseState.LeafNode">
            <summary>
            The element does not expand or collapse.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.FrameworkIds">
            <summary>
            Helper class to convert between <see cref="T:FlaUI.Core.FrameworkType"/> and string.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Definitions.FrameworkIds.Convert(System.String)">
            <summary>
            Converts a string to a <see cref="T:FlaUI.Core.FrameworkType"/>.
            </summary>
            <param name="frameworkId">The string to convert.</param>
            <returns>The matched <see cref="T:FlaUI.Core.FrameworkType"/>. Defaults to <see cref="F:FlaUI.Core.FrameworkType.Unknown"/>.</returns>
        </member>
        <member name="M:FlaUI.Core.Definitions.FrameworkIds.Convert(FlaUI.Core.FrameworkType)">
            <summary>
            Converts a <see cref="T:FlaUI.Core.FrameworkType"/> to a string.
            </summary>
            <param name="frameworkType">The <see cref="T:FlaUI.Core.FrameworkType"/> to convert.</param>
            <returns>The matched string.</returns>
        </member>
        <member name="T:FlaUI.Core.Definitions.HeadingLevel">
            <summary>
            Indicates the heading level of a Microsoft UI Automation element.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.HeadingLevel.None">
            <summary>
            No heading level specified.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.HeadingLevel.Level1">
            <summary>
            Heading level 1.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.HeadingLevel.Level2">
            <summary>
            Heading level 2.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.HeadingLevel.Level3">
            <summary>
            Heading level 3.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.HeadingLevel.Level4">
            <summary>
            Heading level 4.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.HeadingLevel.Level5">
            <summary>
            Heading level 5.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.HeadingLevel.Level6">
            <summary>
            Heading level 6.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.HeadingLevel.Level7">
            <summary>
            Heading level 7.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.HeadingLevel.Level8">
            <summary>
            Heading level 8.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.HeadingLevel.Level9">
            <summary>
            Heading level 9.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.LandmarkType">
            <summary>
            This topic describes the named constants used to identify Microsoft UI Automation landmark types.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.LandmarkType.CustomLandmark">
            <summary>
            Indicates a custom string should be used to describe the landmark using the value from UIA_LocalizedLandmarkTypePropertyId.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.LandmarkType.FormLandmark">
            <summary>
            Indicates that the landmark is related to form type elements typically used for data entry.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.LandmarkType.MainLandmark">
            <summary>
            Indicates that the landmark is for the primary content and not secondary or a lesser priority.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.LandmarkType.NavigationLandmark">
            <summary>
            Indicates that the landmark is related to navigation type elements.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.LandmarkType.SearchLandmark">
            <summary>
            Indicates that the landmark is related to search type elements.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.LiveSetting">
            <summary>
            Describes the notification characteristics of a particular live region.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.LiveSetting.Off">
            <summary>
            The element does not send notifications if the content of the live region has changed.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.LiveSetting.Polite">
            <summary>
            The element sends non-interruptive notifications if the content of the live region has
            changed. With this setting, UI Automation clients and assistive technologies are expected 
            to not interrupt the user to inform of changes to the live region.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.LiveSetting.Assertive">
            <summary>
            The element sends interruptive notifications if the content of the live region has changed. 
            With this setting, UI Automation clients and assistive technologies are expected to interrupt 
            the user to inform of changes to the live region.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.NotificationKind">
            <summary>
            Defines values that indicate the type of a notification event, and a hint to the listener about the processing of the event.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.NotificationKind.ItemAdded">
            <summary>
            The current element and/or the container has had something added to it that should be presented to the user.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.NotificationKind.ItemRemoved">
            <summary>
            The current element has had something removed from inside of it that should be presented to the user.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.NotificationKind.ActionCompleted">
            <summary>
            The current element has a notification that an action was completed.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.NotificationKind.ActionAborted">
            <summary>
            The current element has a notification that an action was aborted.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.NotificationKind.Other">
            <summary>
            The current element has a notification not an add, remove, completed, or aborted action.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.NotificationProcessing">
            <summary>
            Defines values that indicate how a notification should be processed.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.NotificationProcessing.ImportantAll">
            <summary>
            These notifications should be presented to the user as soon as possible
            and all of the notifications from this source should be delivered to the user.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.NotificationProcessing.ImportantMostRecent">
            <summary>
            These notifications should be presented to the user as soon as possible.
            The most recent notification from this source should be delivered to the user because it supersedes all of the other notifications.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.NotificationProcessing.All">
            <summary>
            These notifications should be presented to the user when possible.
            All of the notifications from this source should be delivered to the user.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.NotificationProcessing.MostRecent">
            <summary>
            These notifications should be presented to the user when possible.
            The most recent notification from this source should be delivered to the user because it supersedes all of the other notifications.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.NotificationProcessing.CurrentThenMostRecent">
            <summary>
            These notifications should be presented to the user when possible.
            Don’t interrupt the current notification for this one.
            If new notifications come in from the same source while the current notification is being presented,
            keep the most recent and ignore the rest until the current processing is completed.
            Then, use the most recent message as the current message.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.OrientationType">
            <summary>
            Contains values that specify the orientation of a control.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.OrientationType.None">
            <summary>
            The control has no orientation.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.OrientationType.Horizontal">
            <summary>
            The control has horizontal orientation.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.OrientationType.Vertical">
            <summary>
            The control has vertical orientation.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.PropertyConditionFlags">
            <summary>
            Contains values used in creating property conditions.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.PropertyConditionFlags.None">
            <summary>
            No flags.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.PropertyConditionFlags.IgnoreCase">
            <summary>
            Comparison of string properties is not case-sensitive.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.PropertyConditionFlags.MatchSubstring">
            <summary>
            Comparison of substring properties is enabled.
            Only available on Windows version 1809 and newer.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.RowOrColumnMajor">
            <summary>
            Contains values that specify whether data in a table should be read primarily by row or by column.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.RowOrColumnMajor.RowMajor">
            <summary>
            Data in the table should be read row by row.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.RowOrColumnMajor.ColumnMajor">
            <summary>
            Data in the table should be read column by column.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.RowOrColumnMajor.Indeterminate">
            <summary>
            The best way to present the data is indeterminate.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.ScrollAmount">
            <summary>
            Contains values that specify the direction and distance to scroll.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ScrollAmount.LargeDecrement">
            <summary>
            Scrolling is done in large decrements, equivalent to pressing the PAGE UP key or clicking on a blank part of a scroll bar.
            If one page up is not a relevant amount for the control and no scroll bar exists, the value represents an amount equal to the current visible window.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ScrollAmount.SmallDecrement">
            <summary>
            Scrolling is done in small decrements, equivalent to pressing an arrow key or clicking the arrow button on a scroll bar.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ScrollAmount.NoAmount">
            <summary>
            No scrolling is done.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ScrollAmount.LargeIncrement">
            <summary>
            Scrolling is done in large increments, equivalent to pressing the PAGE DOWN or PAGE UP key or clicking on a blank part of a scroll bar.
            If one page is not a relevant amount for the control and no scroll bar exists, the value represents an amount equal to the current visible window.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ScrollAmount.SmallIncrement">
            <summary>
            Scrolling is done in small increments, equivalent to pressing an arrow key or clicking the arrow button on a scroll bar.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.StructureChangeType">
            <summary>
            Contains values that specify the type of change in the Microsoft UI Automation tree structure.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StructureChangeType.ChildAdded">
            <summary>
            A child element was added to the UI Automation element tree.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StructureChangeType.ChildRemoved">
            <summary>
            A child element was removed from the UI Automation element tree.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StructureChangeType.ChildrenInvalidated">
            <summary>
            Child elements were invalidated in the UI Automation element tree.
            This might mean that one or more child elements were added or removed, or a combination of both
            This value can also indicate that one subtree in the UI was substituted for another.
            For example, the entire contents of a dialog box changed at once, or the view of a list changed because an Explorer-type application navigated to another location.
            The exact meaning depends on the UI Automation provider implementation.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StructureChangeType.ChildrenBulkAdded">
            <summary>
            Child elements were added in bulk to the UI Automation element tree.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StructureChangeType.ChildrenBulkRemoved">
            <summary>
            Child elements were removed in bulk from the UI Automation element tree.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StructureChangeType.ChildrenReordered">
            <summary>
            The order of child elements has changed in the UI Automation element tree. Child elements may or may not have been added or removed.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.StyleType">
            <summary>
            Represents the visual style of text in a document.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.BulletedList">
            <summary>
            A list with bulleted items. Supported starting with Windows 8.1.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Custom">
            <summary>
            A custom style.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Emphasis">
            <summary>
            Text that is emphasized.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Heading1">
            <summary>
            A first level heading.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Heading2">
            <summary>
            A second level heading.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Heading3">
            <summary>
            A third level heading.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Heading4">
            <summary>
            A fourth level heading.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Heading5">
            <summary>
            A fifth level heading.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Heading6">
            <summary>
            A sixth level heading.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Heading7">
            <summary>
            A seventh level heading.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Heading8">
            <summary>
            An eighth level heading.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Heading9">
            <summary>
            A ninth level heading.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Normal">
            <summary>
            Normal style.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.NumberedList">
            <summary>
            A list with numbered items. Supported starting with Windows 8.1.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Quote">
            <summary>
            A quotation.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Subtitle">
            <summary>
            A subtitle.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.StyleType.Title">
            <summary>
            A title.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.SupportedTextSelection">
            <summary>
            Contains values that specify the supported text selection attribute.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.SupportedTextSelection.None">
            <summary>
            Does not support text selections.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.SupportedTextSelection.Single">
            <summary>
            Supports a single, continuous text selection.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.SupportedTextSelection.Multiple">
            <summary>
            Supports multiple, disjoint text selections.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.SynchronizedInputType">
            <summary>
            Contains values that specify the type of synchronized input.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.SynchronizedInputType.KeyUp">
            <summary>
            A key has been released.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.SynchronizedInputType.KeyDown">
            <summary>
            A key has been pressed.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.SynchronizedInputType.LeftMouseUp">
            <summary>
            The left mouse button has been released.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.SynchronizedInputType.LeftMouseDown">
            <summary>
            The left mouse button has been pressed.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.SynchronizedInputType.RightMouseUp">
            <summary>
            The right mouse button has been released.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.SynchronizedInputType.RightMouseDown">
            <summary>
            The right mouse button has been pressed.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.TextEditChangeType">
            <summary>
            Describes the text editing change being performed by controls when text-edit events are raised or handled.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextEditChangeType.None">
            <summary>
            Not related to a specific change type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextEditChangeType.AutoCorrect">
            <summary>
            Change is from an auto-correct action performed by a control.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextEditChangeType.Composition">
            <summary>
            Change is from an IME active composition within a control.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextEditChangeType.CompositionFinalized">
            <summary>
            Change is from an IME composition going from active to finalized state within a control.
            Note: The finalized string may be empty if composition was canceled or deleted.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.TextPatternRangeEndpoint">
            <summary>
            Contains values that specify the endpoints of a text range.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextPatternRangeEndpoint.Start">
            <summary>
            The starting endpoint of the range.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextPatternRangeEndpoint.End">
            <summary>
            The ending endpoint of the range.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.TextUnit">
            <summary>
            Contains values that specify units of text for the purposes of navigation.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextUnit.Character">
            <summary>
            Specifies that the text unit is one character in length.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextUnit.Format">
            <summary>
            Specifies that the text unit is the length of a single, common format specification, such as bold, italic, or similar.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextUnit.Word">
            <summary>
            Specifies that the text unit is one word in length.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextUnit.Line">
            <summary>
            Specifies that the text unit is one line in length.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextUnit.Paragraph">
            <summary>
            Specifies that the text unit is one paragraph in length.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextUnit.Page">
            <summary>
            Specifies that the text unit is one document-specific page in length.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TextUnit.Document">
            <summary>
            Specifies that the text unit is an entire document in length.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.ToggleState">
            <summary>
            Contains values that specify the toggle state of a Microsoft UI Automation element that implements the <see cref="T:FlaUI.Core.Patterns.ITogglePattern"/>.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ToggleState.Off">
            <summary>
            The UI Automation element is not selected, checked, marked or otherwise activated.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ToggleState.On">
            <summary>
            The UI Automation element is selected, checked, marked or otherwise activated.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ToggleState.Indeterminate">
            <summary>
            The UI Automation element is in an indeterminate state.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.TreeScope">
            <summary>
            Contains values that specify the scope of various operations in the Microsoft UI Automation tree.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TreeScope.None">
            <summary>
            The scope excludes the subtree from the search.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TreeScope.Element">
            <summary>
            The scope includes the element itself.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TreeScope.Children">
            <summary>
            The scope includes children of the element.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TreeScope.Descendants">
            <summary>
            The scope includes children and more distant descendants of the element.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TreeScope.Subtree">
            <summary>
            The scope includes the element and all its descendants. This flag is a combination of the <see cref="F:FlaUI.Core.Definitions.TreeScope.Element"/> and <see cref="F:FlaUI.Core.Definitions.TreeScope.Descendants"/> values.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TreeScope.Parent">
            <summary>
            The scope includes the parent of the element.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TreeScope.Ancestors">
            <summary>
            The scope includes the parent and more distant ancestors of the element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.TreeTraversalOptions">
            <summary>
            Defines values that can be used to customize tree navigation order.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TreeTraversalOptions.Default">
            <summary>
            Pre-order, visit children from first to last.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TreeTraversalOptions.PostOrder">
            <summary>
            Post-order, see Remarks for more info.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.TreeTraversalOptions.LastToFirstOrder">
            <summary>
            Visit children from last to first.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.VisualEffects">
            <summary>
            Contains values for the VisualEffects attribute.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.VisualEffects.None">
            <summary>
            No visual effects.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.VisualEffects.Shadow">
            <summary>
            Shadow effect.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.VisualEffects.Reflection">
            <summary>
            Reflection effect.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.VisualEffects.Glow">
            <summary>
            Glow effect.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.VisualEffects.SoftEdges">
            <summary>
            Soft edges effect.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.VisualEffects.Bevel">
            <summary>
            Bevel effect.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.WindowInteractionState">
            <summary>
            Contains values that specify the current state of the window for purposes of user interaction.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.WindowInteractionState.Running">
            <summary>
            The window is running.
            This does not guarantee that the window is ready for user interaction or is responding.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.WindowInteractionState.Closing">
            <summary>
            The window is closing.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.WindowInteractionState.ReadyForUserInteraction">
            <summary>
            The window is ready for user interaction.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.WindowInteractionState.BlockedByModalWindow">
            <summary>
            The window is blocked by a modal window.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.WindowInteractionState.NotResponding">
            <summary>
            The window is not responding.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.WindowVisualState">
            <summary>
            Contains values that specify the visual state of a window.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.WindowVisualState.Normal">
            <summary>
            The window is normal (restored).
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.WindowVisualState.Maximized">
            <summary>
            The window is maximized.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.WindowVisualState.Minimized">
            <summary>
            The window is minimized.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Definitions.ZoomUnit">
            <summary>
            Contains possible values for the <see cref="M:FlaUI.Core.Patterns.ITransform2Pattern.ZoomByUnit(FlaUI.Core.Definitions.ZoomUnit)"/> method, which zooms the viewport of a control by the specified unit.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ZoomUnit.NoAmount">
            <summary>
            No increase or decrease in zoom.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ZoomUnit.LargeDecrement">
            <summary>
            Decrease zoom by a large decrement.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ZoomUnit.SmallDecrement">
            <summary>
            Decrease zoom by a small decrement.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ZoomUnit.LargeIncrement">
            <summary>
            Increase zoom by a large increment.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Definitions.ZoomUnit.SmallIncrement">
            <summary>
            Increase zoom by a small increment.
            </summary>
        </member>
        <member name="M:FlaUI.Core.EventHandlers.ActiveTextPositionChangedEventHandlerBase.UnregisterEventHandler">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.EventHandlers.AutomationEventHandlerBase.UnregisterEventHandler">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.EventHandlers.ElementEventHandlerBase">
            <summary>
            Base class for event handlers that are tied to elements.
            </summary>
        </member>
        <member name="P:FlaUI.Core.EventHandlers.ElementEventHandlerBase.FrameworkElement">
            <summary>
            The framework element element to which this event handler belongs to.
            </summary>
        </member>
        <member name="T:FlaUI.Core.EventHandlers.EventHandlerBase">
            <summary>
            Base class for all event handlers.
            </summary>
        </member>
        <member name="M:FlaUI.Core.EventHandlers.EventHandlerBase.#ctor(FlaUI.Core.AutomationBase)">
            <summary>
            Creates the event handler.
            </summary>
        </member>
        <member name="P:FlaUI.Core.EventHandlers.EventHandlerBase.Automation">
            <summary>
            The <see cref="T:FlaUI.Core.AutomationBase"/> object that belongs to the event handler.
            </summary>
        </member>
        <member name="M:FlaUI.Core.EventHandlers.EventHandlerBase.Dispose">
            <summary>
            Cleans up the event handler by unregistering it.
            </summary>
        </member>
        <member name="M:FlaUI.Core.EventHandlers.EventHandlerBase.UnregisterEventHandler">
            <summary>
            Unregisters the event handler from the automation.
            </summary>
        </member>
        <member name="T:FlaUI.Core.EventHandlers.FocusChangedEventHandlerBase">
            <summary>
            Base event handler for focus changed event handlers.
            </summary>
        </member>
        <member name="M:FlaUI.Core.EventHandlers.FocusChangedEventHandlerBase.UnregisterEventHandler">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.EventHandlers.NotificationEventHandlerBase.UnregisterEventHandler">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.EventHandlers.PropertyChangedEventHandlerBase">
            <summary>
            Base event handler for property changed event handlers.
            </summary>
        </member>
        <member name="M:FlaUI.Core.EventHandlers.PropertyChangedEventHandlerBase.UnregisterEventHandler">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.EventHandlers.StructureChangedEventHandlerBase">
            <summary>
            Base event handler for structure changed event handlers.
            </summary>
        </member>
        <member name="M:FlaUI.Core.EventHandlers.StructureChangedEventHandlerBase.UnregisterEventHandler">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.EventHandlers.TextEditTextChangedEventHandlerBase">
            <summary>
            ase event handler for text edit text changed event handlers.
            </summary>
        </member>
        <member name="M:FlaUI.Core.EventHandlers.TextEditTextChangedEventHandlerBase.UnregisterEventHandler">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.FrameworkAutomationElementBase">
            <summary>
            Base class for a framework specific automation element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.#ctor(FlaUI.Core.AutomationBase)">
            <summary>
            Create a framework automation element with the given <see cref="T:FlaUI.Core.AutomationBase"/>.
            </summary>
            <param name="automation">The <see cref="T:FlaUI.Core.AutomationBase"/>.</param>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.PropertyIdLibrary">
            <summary>
            Provides access to all <see cref="T:FlaUI.Core.Identifiers.PropertyId"/>s.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.Automation">
            <summary>
            Underlying <see cref="T:FlaUI.Core.AutomationBase" /> object where this element belongs to.
            </summary>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.GetPropertyValue(FlaUI.Core.Identifiers.PropertyId)">
            <summary>
            Gets the desired property value. Throws an exception if the property is not supported or is not cached during a cached request.
            </summary>
            <param name="property">The <see cref="T:FlaUI.Core.Identifiers.PropertyId"/> of the property to get the value from.</param>
            <returns>The value of the property.</returns>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.GetPropertyValue``1(FlaUI.Core.Identifiers.PropertyId)">
            <summary>
            Gets the desired property value as the desired type. Throws an exception if the property is not supported or is not cached during a cached request.
            </summary>
            <typeparam name="T">The type of the value to get.</typeparam>
            <param name="property">The <see cref="T:FlaUI.Core.Identifiers.PropertyId"/> of the property to get the value from.</param>
            <returns>The value of the property.</returns>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.TryGetPropertyValue(FlaUI.Core.Identifiers.PropertyId,System.Object@)">
            <summary>
            Tries to get the property value. Throws an exception if the property is not cached during a cached request.
            </summary>
            <param name="property">The <see cref="T:FlaUI.Core.Identifiers.PropertyId"/> of the property to get the value from.</param>
            <param name="value">The out object where the value should be put. Is the default if the property is not supported.</param>
            <returns>True if the property is supported and false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.TryGetPropertyValue``1(FlaUI.Core.Identifiers.PropertyId,``0@)">
            <summary>
            Tries to get the property value as the desired type. Throws an exception if the property is not cached during a cached request.
            </summary>
            <typeparam name="T">The type of the value to get.</typeparam>
            <param name="property">The <see cref="T:FlaUI.Core.Identifiers.PropertyId"/> of the property to get the value from.</param>
            <param name="value">The out object where the value should be put. Is the default if the property is not supported.</param>
            <returns>True if the property is supported and false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.GetNativePattern``1(FlaUI.Core.Identifiers.PatternId)">
            <summary>
            Gets the native pattern object. Throws an exception if the pattern is not supported or is not cached during a cached request.
            </summary>
            <typeparam name="T">The type of the pattern to get.</typeparam>
            <param name="pattern">The <see cref="T:FlaUI.Core.Identifiers.PatternId"/> of the pattern to get.</param>
            <returns>The native pattern.</returns>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.TryGetNativePattern``1(FlaUI.Core.Identifiers.PatternId,``0@)">
            <summary>
            Tries to get the native pattern.
            </summary>
            <typeparam name="T">The type of the pattern to get.</typeparam>
            <param name="pattern">The <see cref="T:FlaUI.Core.Identifiers.PatternId"/> of the pattern to get.</param>
            <param name="nativePattern">The out object where the pattern should be put. Is null if the pattern is not supported.</param>
            <returns>True if the pattern is supported and false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.GetClickablePoint">
            <summary>
            Gets the clickable point.
            </summary>
            <returns>The found clickable point.</returns>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.SetFocus">
            <summary>
            Sets the focus to the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.InternalGetPropertyValue(System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Gets the desired property value.
            </summary>
            <param name="propertyId">The id of the property to get.</param>
            <param name="cached">Flag to indicate if the cached or current value should be fetched.</param>
            <param name="useDefaultIfNotSupported"> Flag to indicate, if the default value should be used if the property is not supported.</param>
            <returns>The value / default value of the property or <see cref="P:FlaUI.Core.AutomationBase.NotSupportedValue" />.</returns>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.InternalGetPattern(System.Int32,System.Boolean)">
            <summary>
            Gets the desired pattern.
            </summary>
            <param name="patternId">The id of the pattern to get.</param>
            <param name="cached">Flag to indicate if the cached or current pattern should be fetched.</param>
            <returns>The pattern or null if it was not found / cached.</returns>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.FindAll(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.FindFirst(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.FindAllWithOptions(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase,FlaUI.Core.Definitions.TreeTraversalOptions,FlaUI.Core.AutomationElements.AutomationElement)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.FindFirstWithOptions(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Conditions.ConditionBase,FlaUI.Core.Definitions.TreeTraversalOptions,FlaUI.Core.AutomationElements.AutomationElement)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.FindAt(FlaUI.Core.Definitions.TreeScope,System.Int32,FlaUI.Core.Conditions.ConditionBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.TryGetClickablePoint(System.Drawing.Point@)">
            <summary>
            Tries to get a clickable point.
            </summary>
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.RegisterActiveTextPositionChangedEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.ITextRange})">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.RegisterAutomationEvent(FlaUI.Core.Identifiers.EventId,FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Identifiers.EventId})">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.RegisterPropertyChangedEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Identifiers.PropertyId,System.Object},FlaUI.Core.Identifiers.PropertyId[])">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.RegisterStructureChangedEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Definitions.StructureChangeType,System.Int32[]})">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.RegisterNotificationEvent(FlaUI.Core.Definitions.TreeScope,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Definitions.NotificationKind,FlaUI.Core.Definitions.NotificationProcessing,System.String,System.String})">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.RegisterTextEditTextChangedEventHandler(FlaUI.Core.Definitions.TreeScope,FlaUI.Core.Definitions.TextEditChangeType,System.Action{FlaUI.Core.AutomationElements.AutomationElement,FlaUI.Core.Definitions.TextEditChangeType,System.String[]})">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.UnregisterActiveTextPositionChangedEventHandler(FlaUI.Core.EventHandlers.ActiveTextPositionChangedEventHandlerBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.UnregisterAutomationEventHandler(FlaUI.Core.EventHandlers.AutomationEventHandlerBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.UnregisterPropertyChangedEventHandler(FlaUI.Core.EventHandlers.PropertyChangedEventHandlerBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.UnregisterStructureChangedEventHandler(FlaUI.Core.EventHandlers.StructureChangedEventHandlerBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.UnregisterNotificationEventHandler(FlaUI.Core.EventHandlers.NotificationEventHandlerBase)">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.FrameworkAutomationElementBase.UnregisterTextEditTextChangedEventHandler(FlaUI.Core.EventHandlers.TextEditTextChangedEventHandlerBase)">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.Patterns">
            <summary>
            Provides access to all patterns.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.Properties">
            <summary>
            Provides access to all properties.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.AcceleratorKey">
            <summary>
            Gets a string containing the accelerator key combinations for the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.AccessKey">
            <summary>
            Gets a string containing the access key character for the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.AriaProperties">
            <summary>
            Gets a formatted string containing the Accessible Rich Internet Application (ARIA) property information for the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.AriaRole">
            <summary>
            Gets a string containing the Accessible Rich Internet Application (ARIA) role information for the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.AutomationId">
            <summary>
            Gets a string containing the UI Automation identifier (ID) for the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.BoundingRectangle">
            <summary>
            Gets the coordinates of the rectangle that completely encloses the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.ClassName">
            <summary>
            Gets a string containing the class name of the element as assigned by the control developer.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.ClickablePoint">
            <summary>
            Gets a point on the element that can be clicked. An element cannot be clicked if it is completely or partially obscured by another window.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.ControllerFor">
            <summary>
            Gets an array of elements that are manipulated by the current element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.ControlType">
            <summary>
            Gets the <see cref="P:FlaUI.Core.FrameworkAutomationElementBase.ControlType"/> of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.Culture">
            <summary>
            Gets the locale identifier of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.DescribedBy">
            <summary>
            Gets an array of elements that provide more information about the current element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.FlowsFrom">
            <summary>
            Gets an array of elements that suggests the reading order before the current element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.FlowsTo">
            <summary>
            Gets an array of elements that suggests the reading order after the current element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.FrameworkId">
            <summary>
            Gets the name of the underlying UI framework.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.HasKeyboardFocus">
            <summary>
            Gets a value that indicates whether the element has keyboard focus.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.HeadingLevel">
            <summary>
            Gets the heading level of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.HelpText">
            <summary>
            Gets the help text associated with the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.IsContentElement">
            <summary>
            Gets a value that specifies whether the element is a content element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.IsControlElement">
            <summary>
            Gets a value that indicates whether the element is viewed as a control.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.IsDataValidForForm">
            <summary>
            Gets a value that indicates whether the entered or selected value is valid for the form rule associated with the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.IsDialog">
            <summary>
            Gets a value that indicates whether the element is a dialog.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.IsEnabled">
            <summary>
            Gets a value that indicates whether the user interface (UI) item referenced by the element is enabled.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.IsKeyboardFocusable">
            <summary>
            Gets a value that indicates whether the element can accept keyboard focus.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.IsOffscreen">
            <summary>
            Gets a value that indicates whether the element is visible on the screen.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.IsPassword">
            <summary>
            Gets a value that indicates whether the element contains protected content.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.IsPeripheral">
            <summary>
            Gets a value that indicates whether the element represents peripheral UI.
            Peripheral UI appears and supports user interaction, but does not take keyboard focus when it appears.
            Examples of peripheral UI includes popups, flyouts, context menus, or floating notifications.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.IsRequiredForForm">
            <summary>
            Gets a value that indicates whether the element is required to be filled out on a form.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.ItemStatus">
            <summary>
            Gets a description of the status of an item within an element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.ItemType">
            <summary>
            Gets a description of the type of an item.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.LabeledBy">
            <summary>
            Gets the element that contains the text label for this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.LandmarkType">
            <summary>
            Gets the landmark type of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.Level">
            <summary>
            Gets the 1-based integer for the level (hierarchy) for the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.LiveSetting">
            <summary>
            Gets a value which indicates the "politeness" level that a client should use to notify the user of changes to the live region.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.LocalizedControlType">
            <summary>
            Gets a description of the control type.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.Name">
            <summary>
            Gets the name of the element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.NativeWindowHandle">
            <summary>
            Gets the handle of the element's window.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.OptimizeForVisualContent">
            <summary>
            Gets a value that indicates whether the provider exposes only elements that are visible.
            A provider can use this property to optimize performance when working with very large pieces of content.
            For example, as the user pages through a large piece of content, the provider can destroy content elements that are no longer visible.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.Orientation">
            <summary>
            Gets the orientation of the control.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.ProcessId">
            <summary>
            Gets the process identifier (ID) of this element.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.ProviderDescription">
            <summary>
            Gets a formatted string containing the source information of the UI Automation provider for the element, including proxy information.
            </summary>
        </member>
        <member name="P:FlaUI.Core.FrameworkAutomationElementBase.RuntimeId">
            <summary>
            Gets the unique identifier assigned to the user interface (UI) item.
            </summary>
        </member>
        <member name="T:FlaUI.Core.FrameworkType">
            <summary>
            An enum for the known framework types.
            </summary>
        </member>
        <member name="F:FlaUI.Core.FrameworkType.None">
            <summary>
            No framework is used.
            </summary>
        </member>
        <member name="F:FlaUI.Core.FrameworkType.Unknown">
            <summary>
            The framework used is unknown.
            </summary>
        </member>
        <member name="F:FlaUI.Core.FrameworkType.Wpf">
            <summary>
            The framework used is WPF.
            </summary>
        </member>
        <member name="F:FlaUI.Core.FrameworkType.WinForms">
            <summary>
            The framework used is Windows Forms.
            </summary>
        </member>
        <member name="F:FlaUI.Core.FrameworkType.Win32">
            <summary>
            The framework used is Win32.
            </summary>
        </member>
        <member name="F:FlaUI.Core.FrameworkType.Xaml">
            <summary>
            The framework used is XAML (Universal Application).
            </summary>
        </member>
        <member name="F:FlaUI.Core.FrameworkType.Qt">
            <summary>
            The framework used is Qt.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Identifiers.ConvertibleIdentifierBase">
            <summary>
            Base class for identifiers which are convertible
            </summary>
        </member>
        <member name="M:FlaUI.Core.Identifiers.ConvertibleIdentifierBase.SetConverter``1(System.Func{FlaUI.Core.AutomationBase,System.Object,System.Object})">
            <summary>
            Sets a custom convert method to convert the values for this id
            </summary>
        </member>
        <member name="M:FlaUI.Core.Identifiers.ConvertibleIdentifierBase.Convert``1(FlaUI.Core.AutomationBase,System.Object)">
            <summary>
            Converts the given value with the converter or casts it, if no converter is given
            </summary>
        </member>
        <member name="T:FlaUI.Core.Identifiers.EventId">
            <summary>
            A wrapper around the event ids
            </summary>
        </member>
        <member name="F:FlaUI.Core.Identifiers.EventId.NotSupportedByFramework">
            <summary>
            Fixed EventId which is used for patterns that are not supported by the framework.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Identifiers.IdentifierBase">
            <summary>
            Base class for wrappers around various identifiers
            </summary>
        </member>
        <member name="T:FlaUI.Core.Identifiers.IdentifierBase.IdentifiersHolder">
            <summary>
            Class which capsules all identifiers which can be used for an automation library
            </summary>
        </member>
        <member name="F:FlaUI.Core.Identifiers.IdentifierBase.IdentifiersHolder.PropertyDict">
            <summary>
            Dictionary which holds all known properties
            </summary>
        </member>
        <member name="F:FlaUI.Core.Identifiers.IdentifierBase.IdentifiersHolder.EventDict">
            <summary>
            Dictionary which holds all known events
            </summary>
        </member>
        <member name="F:FlaUI.Core.Identifiers.IdentifierBase.IdentifiersHolder.PatternDict">
            <summary>
            Dictionary which holds all known patterns
            </summary>
        </member>
        <member name="F:FlaUI.Core.Identifiers.IdentifierBase.IdentifiersHolder.TextAttributeDict">
            <summary>
            Dictionary which holds all known text attributes
            </summary>
        </member>
        <member name="F:FlaUI.Core.Identifiers.IdentifierBase.IdentifiersDict">
            <summary>
            Dictionary which holds all identifiers for each automation library
            </summary>
        </member>
        <member name="P:FlaUI.Core.Identifiers.IdentifierBase.Id">
            <summary>
            The native id of the identifier
            </summary>
        </member>
        <member name="P:FlaUI.Core.Identifiers.IdentifierBase.Name">
            <summary>
            A readable name for the identifier
            </summary>
        </member>
        <member name="M:FlaUI.Core.Identifiers.IdentifierBase.Register``1(System.Int32,System.Collections.Generic.IDictionary{System.Int32,``0},System.Func{``0})">
            <summary>
            Adds the property to the dictionary if it does not exist yet
            </summary>
        </member>
        <member name="M:FlaUI.Core.Identifiers.IdentifierBase.GetIdHolder(FlaUI.Core.AutomationType)">
            <summary>
            Get the ids-holder or create a new one
            </summary>
        </member>
        <member name="T:FlaUI.Core.Identifiers.PatternId">
            <summary>
            A wrapper around the pattern ids
            </summary>
        </member>
        <member name="F:FlaUI.Core.Identifiers.PatternId.NotSupportedByFramework">
            <summary>
            Fixed PatternId which is used for patterns that are not supported by the framework.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Identifiers.PatternId.AvailabilityProperty">
            <summary>
            Property which can be used to check for the patterns availability on an element
            </summary>
        </member>
        <member name="T:FlaUI.Core.Identifiers.PropertyId">
            <summary>
            A wrapper around the property ids
            </summary>
        </member>
        <member name="F:FlaUI.Core.Identifiers.PropertyId.NotSupportedByFramework">
            <summary>
            Fixed PropertyId which is used for patterns that are not supported by the framework.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Identifiers.PropertyId.GetCondition(System.Object)">
            <summary>
            Returns a condition for this property with the given value.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Identifiers.TextAttributeId">
            <summary>
            A wrapper around text attribute ids
            </summary>
        </member>
        <member name="F:FlaUI.Core.Identifiers.TextAttributeId.NotSupportedByFramework">
            <summary>
            Fixed TextAttributeId which is used for text attributes that are not supported by the framework.
            </summary>
        </member>
        <member name="T:FlaUI.Core.IEventLibrary">
            <summary>
            Interface for an event library.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Input.Interpolation">
            <summary>
            Interpolation tool to transition one or more points to another location during a time frame.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Interpolation.Execute(System.Action{System.Drawing.Point[]},System.Tuple{System.Drawing.Point,System.Drawing.Point}[],System.TimeSpan,System.TimeSpan,System.Boolean)">
            <summary>
            Transitions the given points from start to end in the given duration. Calls action for each interval with all new points.
            </summary>
            <param name="action">The action to execute for each interval.</param>
            <param name="startEndPoints">A list of tuples with start/end points.</param>
            <param name="duration">The total duration for the transition.</param>
            <param name="interval">The interval of each step.</param>
            <param name="skipInitialPosition">A flag to indicate if the initial position should be skipped from firing the action.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Interpolation.Execute(System.Action{System.Drawing.Point},System.Drawing.Point,System.Drawing.Point,System.TimeSpan,System.TimeSpan,System.Boolean)">
            <summary>
            Transitions the given point from start to end in the given duration. Calls action for each interval with the new point.
            </summary>
            <param name="action">The action to execute for each interval.</param>
            <param name="startPoint">The starting point.</param>
            <param name="endPoint">The end point.</param>
            <param name="duration">The total duration for the transition.</param>
            <param name="interval">The interval of each step.</param>
            <param name="skipInitialPosition">A flag to indicate if the initial position should be skipped from firing the action.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Interpolation.ExecuteRotation(System.Action{System.Drawing.Point},System.Drawing.Point,System.Double,System.Double,System.Double,System.TimeSpan,System.TimeSpan,System.Boolean)">
            <summary>
            Performs a rotation transition around the given point with a given radius.
            </summary>
            <param name="action">The action to execute for each interval.</param>
            <param name="centerPoint">The center point of the rotation.</param>
            <param name="radius">The radius of the rotation.</param>
            <param name="startAngle">The starting angle (in rad).</param>
            <param name="endAngle">The ending angle (in rad).</param>
            <param name="duration">The total duration for the transition.</param>
            <param name="interval">The interval of each step.</param>
            <param name="skipInitialPosition">A flag to indicate if the initial position should be skipped from firing the action.</param>
        </member>
        <member name="T:FlaUI.Core.Input.Keyboard">
            <summary>
            Keyboard class to simulate key input.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.Type(System.String)">
            <summary>
            Types the given text, one char after another.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.Type(System.Char)">
            <summary>
            Types the given character.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.Type(FlaUI.Core.WindowsAPI.VirtualKeyShort[])">
            <summary>
            Types the given keys, one by one.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.TypeSimultaneously(FlaUI.Core.WindowsAPI.VirtualKeyShort[])">
            <summary>
            Types the given keys simultaneously (starting with the first).
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.TypeScanCode(System.UInt16,System.Boolean)">
            <summary>
            Types the given scan-code.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.TypeVirtualKeyCode(System.UInt16)">
            <summary>
            Types the given virtual key-code.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.Press(FlaUI.Core.WindowsAPI.VirtualKeyShort)">
            <summary>
            Presses the given key.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.PressScanCode(System.UInt16,System.Boolean)">
            <summary>
            Presses the given scan-code.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.PressVirtualKeyCode(System.UInt16)">
            <summary>
            Presses the given virtual key-code.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.Release(FlaUI.Core.WindowsAPI.VirtualKeyShort)">
            <summary>
            Releases the given key.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.ReleaseScanCode(System.UInt16,System.Boolean)">
            <summary>
            Releases the given scan-code.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.ReleaseVirtualKeyCode(System.UInt16)">
            <summary>
            Releases the given virtual key-code.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.Pressing(FlaUI.Core.WindowsAPI.VirtualKeyShort[])">
            <summary>
            Presses the given keys and releases them when the returned object is disposed.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.HasScanModifier(System.Byte,FlaUI.Core.WindowsAPI.VkKeyScanModifiers)">
            <summary>
            Checks if a given byte has a specific VkKeyScan-modifier set.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Keyboard.SendInput(System.UInt16,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Effectively sends the keyboard input command.
            </summary>
            <param name="keyCode">The key code to send. Can be the scan code or the virtual key code.</param>
            <param name="isKeyDown">Flag if the key should be pressed or released.</param>
            <param name="isScanCode">Flag if the code is the scan code or the virtual key code.</param>
            <param name="isExtended">Flag if the key is an extended key.</param>
            <param name="isUnicode">Flag if the key is unicode.</param>
        </member>
        <member name="T:FlaUI.Core.Input.Mouse">
            <summary>
            Mouse class to simulate mouse input.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.Mouse.ExtraMillisecondsBecauseOfBugInWindows">
            <summary>
            Time to add to the double click time to prevent false double clicks.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.Mouse.WheelDelta">
            <summary>
            Number which defines one wheel "click" of the mouse wheel.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.Mouse.CurrentDoubleClickTime">
            <summary>
            The current max timespan (in milliseconds) for double clicks.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.Mouse.LastClickTimes">
            <summary>
            Dictionary which holds the last click time for each button.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.Mouse.LastClickPositions">
            <summary>
            Dictionary which holds the last click position for each button.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.#cctor">
            <summary>
            Static constructor.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Input.Mouse.MovePixelsPerMillisecond">
            <summary>
            The number of pixels the mouse is moved per millisecond.
            Used to calculate the duration of a mouse move.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Input.Mouse.MovePixelsPerStep">
            <summary>
            The number of pixels the mouse is moved per step.
            Used to calculate the interval of a mouse move.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Input.Mouse.Position">
            <summary>
            The current position of the mouse cursor.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Input.Mouse.AreButtonsSwapped">
            <summary>
            Flag to indicate if the buttons are swapped (left-handed).
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.MoveBy(System.Int32,System.Int32)">
            <summary>
            Moves the mouse by a given delta from the current position.
            </summary>
            <param name="deltaX">The delta for the x-axis.</param>
            <param name="deltaY">The delta for the y-axis.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.MoveTo(System.Int32,System.Int32)">
            <summary>
            Moves the mouse to a new position.
            </summary>
            <param name="newX">The new position on the x-axis.</param>
            <param name="newY">The new position on the y-axis.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.MoveTo(System.Drawing.Point)">
            <summary>
            Moves the mouse to a new position.
            </summary>
            <param name="newPosition">The new position for the mouse.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.Click(FlaUI.Core.Input.MouseButton)">
            <summary>
            Clicks the specified mouse button at the current location.
            </summary>
            <param name="mouseButton">The mouse button to click. Defaults to the left button.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.Click(System.Drawing.Point,FlaUI.Core.Input.MouseButton)">
            <summary>
            Moves to a specific position and clicks the specified mouse button.
            </summary>
            <param name="point">The position to move to before clicking.</param>
            <param name="mouseButton">The mouse button to click. Defaults to the left button.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.DoubleClick(FlaUI.Core.Input.MouseButton)">
            <summary>
            Double-clicks the specified mouse button at the current location.
            </summary>
            <param name="mouseButton">The mouse button to double-click. Defaults to the left button.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.DoubleClick(System.Drawing.Point,FlaUI.Core.Input.MouseButton)">
            <summary>
            Moves to a specific position and double-clicks the specified mouse button.
            </summary>
            <param name="point">The position to move to before double-clicking.</param>
            <param name="mouseButton">The mouse button to double-click. Defaults to the left button.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.Down(FlaUI.Core.Input.MouseButton)">
            <summary>
            Sends a mouse down command for the specified mouse button.
            </summary>
            <param name="mouseButton">The mouse button to press. Defaults to the left button.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.Up(FlaUI.Core.Input.MouseButton)">
            <summary>
            Sends a mouse up command for the specified mouse button.
            </summary>
            <param name="mouseButton">The mouse button to release. Defaults to the left button.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.Scroll(System.Double)">
            <summary>
            Simulates scrolling of the mouse wheel up or down.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.HorizontalScroll(System.Double)">
            <summary>
            Simulates scrolling of the horizontal mouse wheel left or right.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.DragHorizontally(System.Drawing.Point,System.Int32,FlaUI.Core.Input.MouseButton)">
            <summary>
            Drags the mouse horizontally.
            </summary>
            <param name="startingPoint">Starting point of the drag.</param>
            <param name="distance">The distance to drag, + for right, - for left.</param>
            <param name="mouseButton">The mouse button to use for dragging. Defaults to the left button.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.DragVertically(System.Drawing.Point,System.Int32,FlaUI.Core.Input.MouseButton)">
            <summary>
            Drags the mouse vertically.
            </summary>
            <param name="startingPoint">Starting point of the drag</param>
            <param name="distance">The distance to drag, + for down, - for up</param>
            <param name="mouseButton">The mouse button to use for dragging. Defaults to the left button.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.Drag(System.Drawing.Point,System.Int32,System.Int32,FlaUI.Core.Input.MouseButton)">
            <summary>
            Drags the mouse from the starting point with the given distance.
            </summary>
            <param name="startingPoint">Starting point of the drag.</param>
            <param name="distanceX">The x distance to drag, + for down, - for up.</param>
            <param name="distanceY">The y distance to drag, + for right, - for left.</param>
            <param name="mouseButton">The mouse button to use for dragging. Defaults to the left button.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.Drag(System.Drawing.Point,System.Drawing.Point,FlaUI.Core.Input.MouseButton)">
            <summary>
            Drags the mouse from the starting point to another point.
            </summary>
            <param name="startingPoint">Starting point of the drag.</param>
            <param name="endingPoint">Ending point of the drag.</param>
            <param name="mouseButton">The mouse button to use for dragging. Defaults to the left button.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.GetFlagsAndDataForButton(FlaUI.Core.Input.MouseButton,System.Boolean,System.UInt32@)">
            <summary>
            Converts the button to the correct <see cref="T:FlaUI.Core.WindowsAPI.MouseEventFlags" /> object
            and fills the additional data if needed.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.SwapButtonIfNeeded(FlaUI.Core.Input.MouseButton)">
            <summary>
            Swaps the left/right button if <see cref="P:FlaUI.Core.Input.Mouse.AreButtonsSwapped" /> is set.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.SendInput(System.Int32,System.Int32,System.UInt32,FlaUI.Core.WindowsAPI.MouseEventFlags)">
            <summary>
            Effectively sends the mouse input command.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.NormalizeCoordinates(System.Int32@,System.Int32@)">
            <summary>
            Normalizes the coordinates to get the absolute values from 0 to 65536.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.LeftClick">
            <summary>
            Performs a left click.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.LeftClick(System.Drawing.Point)">
            <summary>
            Performs a left click on a given point.
            </summary>
            <param name="point">The position to move to before clicking.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.LeftDoubleClick">
            <summary>
            Performs a left double-click.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.LeftDoubleClick(System.Drawing.Point)">
            <summary>
            Performs a left double-click on a given point.
            </summary>
            <param name="point">The position to move to before clicking.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.RightClick">
            <summary>
            Performs a right click.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.RightClick(System.Drawing.Point)">
            <summary>
            Performs a right click on a given point.
            </summary>
            <param name="point">The position to move to before clicking.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.RightDoubleClick">
            <summary>
            Performs a right double-click.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Mouse.RightDoubleClick(System.Drawing.Point)">
            <summary>
            Performs a right double-click on a given point.
            </summary>
            <param name="point">The position to move to before clicking.</param>
        </member>
        <member name="T:FlaUI.Core.Input.MouseButton">
            <summary>
            An enum for the different mouse buttons.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.MouseButton.Left">
            <summary>
            The left mouse button
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.MouseButton.Middle">
            <summary>
            The middle mouse button
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.MouseButton.Right">
            <summary>
            The right mouse button
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.MouseButton.XButton1">
            <summary>
            The fourth mouse button
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.MouseButton.XButton2">
            <summary>
            The fifth mouse button
            </summary>
        </member>
        <member name="T:FlaUI.Core.Input.Touch">
            <summary>
            Touch class to simulate touch input.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Input.Touch.DefaultInterval">
            <summary>
            The interval that is used for interpolation/rotation.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Touch.Tap(System.Drawing.Point[])">
            <summary>
            Performs a tap on the given point or points.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Touch.Hold(System.TimeSpan,System.Drawing.Point[])">
            <summary>
            Holds the touch on the given points for the given duration.
            </summary>
            <param name="duration">The duration of the hold.</param>
            <param name="points">The points that should be hold down.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Touch.Pinch(System.Drawing.Point,System.Double,System.Double,System.TimeSpan,System.Double)">
            <summary>
            Performs a pinch with two fingers.
            </summary>
            <param name="center">The center point of the pinch.</param>
            <param name="startRadius">The starting radius.</param>
            <param name="endRadius">The end radius.</param>
            <param name="duration">The duration of the action.</param>
            <param name="angle">The angle of the two points, relative to the x-axis.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Touch.Transition(System.TimeSpan,System.Tuple{System.Drawing.Point,System.Drawing.Point}[])">
            <summary>
            Transitions all the points from the start point to the end points.
            </summary>
            <param name="duration">The duration for the action.</param>
            <param name="startEndPoints">The list of start/end point tuples.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Touch.Drag(System.TimeSpan,System.Drawing.Point,System.Drawing.Point)">
            <summary>
            Performs a touch-drag from the start point to the end point.
            </summary>
            <param name="duration">The duration of the action.</param>
            <param name="startPoint">The starting point of the drag.</param>
            <param name="endPoint">The end point of the drag.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Touch.Rotate(System.Drawing.Point,System.Double,System.Double,System.Double,System.TimeSpan)">
            <summary>
            Performs a 2-finger rotation around the given point where the first finger is at the center and
            the second is rotated around.
            </summary>
            <param name="center">The center point of the rotation.</param>
            <param name="radius">The radius of the rotation.</param>
            <param name="startAngle">The starting angle (in rad).</param>
            <param name="endAngle">The ending angle (in rad).</param>
            <param name="duration">The total duration for the transition.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Touch.CreatePointsAround(System.Drawing.Point,System.Double,System.Double)">
            <summary>
            Create two points around the given center points.
            </summary>
            <param name="center">The center point.</param>
            <param name="radius">The radius.</param>
            <param name="angle">The angle to the x axis.</param>
            <returns>An array of the two points.</returns>
        </member>
        <member name="M:FlaUI.Core.Input.Touch.CreatePointerTouch(System.Drawing.Point,FlaUI.Core.WindowsAPI.PointerFlags,System.UInt32)">
            <summary>
            Helper method to create the most used <see cref="T:FlaUI.Core.WindowsAPI.POINTER_TOUCH_INFO"/> structure.
            </summary>
            <param name="point">The point where the touch action occurs.</param>
            <param name="flags">The flags used for the touch action</param>
            <param name="id">The id of the point, only needed when more than one.</param>
            <returns>A <see cref="T:FlaUI.Core.WindowsAPI.POINTER_TOUCH_INFO"/> structure.</returns>
        </member>
        <member name="M:FlaUI.Core.Input.Touch.InjectTouchInput(FlaUI.Core.WindowsAPI.POINTER_TOUCH_INFO[])">
            <summary>
            Effectively executes the touch input action.
            </summary>
            <param name="contacts">The list of input contacts which should be executed.</param>
        </member>
        <member name="T:FlaUI.Core.Input.Wait">
            <summary>
            Class with various helper tools used in various places
            </summary>
        </member>
        <member name="M:FlaUI.Core.Input.Wait.UntilInputIsProcessed(System.Nullable{System.TimeSpan})">
            <summary>
            Waits a little to allow inputs (mouse, keyboard, ...) to be processed.
            </summary>
            <param name="waitTimeout">An optional timeout. If no value or null is passed, the timeout is 100ms.</param>
        </member>
        <member name="M:FlaUI.Core.Input.Wait.UntilResponsive(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Waits until the given element is responsive.
            </summary>
            <param name="automationElement">The element that should be waited for.</param>
            <returns>True if the element was responsive, false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.Input.Wait.UntilResponsive(FlaUI.Core.AutomationElements.AutomationElement,System.TimeSpan)">
            <summary>
            Waits until the given element is responsive.
            </summary>
            <param name="automationElement">The element that should be waited for.</param>
            <param name="timeout">The timeout of the waiting.</param>
            <returns>True if the element was responsive, false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.Input.Wait.UntilResponsive(System.IntPtr)">
            <summary>
            Waits until the given hwnd is responsive.
            See: https://blogs.msdn.microsoft.com/oldnewthing/20161118-00/?p=94745
            </summary>
            <param name="hWnd">The hwnd that should be waited for.</param>
            <returns>True if the hwnd was responsive, false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.Input.Wait.UntilResponsive(System.IntPtr,System.TimeSpan)">
            <summary>
            Waits until the given hwnd is responsive.
            See: https://blogs.msdn.microsoft.com/oldnewthing/20161118-00/?p=94745
            </summary>
            <param name="hWnd">The hwnd that should be waited for.</param>
            <param name="timeout">The timeout of the waiting.</param>
            <returns>True if the hwnd was responsive, false otherwise.</returns>
        </member>
        <member name="T:FlaUI.Core.IPatternLibrary">
            <summary>
            Interface for a pattern library.
            </summary>
        </member>
        <member name="P:FlaUI.Core.IPatternLibrary.AllForCurrentFramework">
            <summary>
            Returns all supported patterns for the current framework.
            </summary>
        </member>
        <member name="T:FlaUI.Core.IPropertyLibrary">
            <summary>
            Interface for a property library.
            </summary>
        </member>
        <member name="T:FlaUI.Core.ITextAttributeLibrary">
            <summary>
            Interface for a text attribute library.
            </summary>
        </member>
        <member name="T:FlaUI.Core.ITreeWalker">
            <summary>
            Interface for a class that implements a tree walker.
            </summary>
        </member>
        <member name="M:FlaUI.Core.ITreeWalker.GetParent(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Gets the parent of the given element.
            </summary>
            <param name="element">The element to get the parent for.</param>
            <returns>The parent or null if none is found.</returns>
        </member>
        <member name="M:FlaUI.Core.ITreeWalker.GetFirstChild(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Gets the first child of the given element.
            </summary>
            <param name="element">The element to get the first child for.</param>
            <returns>The first child or null if none is found.</returns>
        </member>
        <member name="M:FlaUI.Core.ITreeWalker.GetLastChild(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Gets the last child of the given element.
            </summary>
            <param name="element">The element to get the last child for.</param>
            <returns>The last child or null if none is found.</returns>
        </member>
        <member name="M:FlaUI.Core.ITreeWalker.GetNextSibling(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Gets the next sibling of the given element.
            </summary>
            <param name="element">The element to get the next sibling for.</param>
            <returns>The next sibling or null if none is found.</returns>
        </member>
        <member name="M:FlaUI.Core.ITreeWalker.GetPreviousSibling(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Gets the previous sibling of the given element.
            </summary>
            <param name="element">The element to get the previous sibling for.</param>
            <returns>The previous sibling or null if none is found.</returns>
        </member>
        <member name="T:FlaUI.Core.ITreeWalkerFactory">
            <summary>
            Interface for a class to create <see cref="T:FlaUI.Core.ITreeWalker"/> instances.
            </summary>
        </member>
        <member name="M:FlaUI.Core.ITreeWalkerFactory.GetControlViewWalker">
            <summary>
            Creates a control view walker.
            </summary>
        </member>
        <member name="M:FlaUI.Core.ITreeWalkerFactory.GetContentViewWalker">
            <summary>
            Creates a content view walker.
            </summary>
        </member>
        <member name="M:FlaUI.Core.ITreeWalkerFactory.GetRawViewWalker">
            <summary>
            Creates a raw view walker.
            </summary>
            <returns></returns>
        </member>
        <member name="M:FlaUI.Core.ITreeWalkerFactory.GetCustomTreeWalker(FlaUI.Core.Conditions.ConditionBase)">
            <summary>
            Creates a custom walker with a given condition.
            </summary>
            <param name="condition">The condition used for the walker.</param>
        </member>
        <member name="M:FlaUI.Core.Logging.ILogger.SetLevel(FlaUI.Core.Logging.LogLevel)">
            <summary>
            Sets up to which level the logger should log messages.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Logging.LoggerBase.SetLevel(FlaUI.Core.Logging.LogLevel)">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.Overlay.IOverlayManager.Size">
            <summary>
            Border size of the overlay
            </summary>
        </member>
        <member name="P:FlaUI.Core.Overlay.IOverlayManager.Margin">
            <summary>
            Margin of the overlay (use negative to move it inside)
            </summary>
        </member>
        <member name="M:FlaUI.Core.Overlay.IOverlayManager.Show(System.Drawing.Rectangle,System.Drawing.Color,System.Int32)">
            <summary>
            Shows the overlay for a given duration asynchronously.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Overlay.IOverlayManager.ShowBlocking(System.Drawing.Rectangle,System.Drawing.Color,System.Int32)">
            <summary>
            Shows the overlay for a given duration and blocks further execution until it is hidden again.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Overlay.NullOverlayManager">
            <summary>
            An overlay manager that does nothing.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Patterns.Infrastructure.IPattern">
            <summary>
            Interface for an automation pattern.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Patterns.Infrastructure.PatternBase`1">
            <summary>
            Base class for a pattern implementation.
            </summary>
            <typeparam name="TNativePattern">The type of the native pattern.</typeparam>
        </member>
        <member name="T:FlaUI.Core.Patterns.IInvokePattern">
            <summary>
            Interface for an invoke pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Patterns.IInvokePattern.EventIds">
            <summary>
            Gets the supported events by this pattern.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Patterns.IInvokePattern.Invoke">
            <summary>
            Invokes the element.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Patterns.IInvokePatternEventIds">
            <summary>
            Interface for invoke pattern events.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Patterns.IInvokePatternEventIds.InvokedEvent">
            <summary>
            Gets the invoked event.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Patterns.InvokePatternBase`1">
            <summary>
            Base cass for an <see cref="T:FlaUI.Core.Patterns.IInvokePattern"/>.
            </summary>
            <typeparam name="TNativePattern">The type of the native invoke pattern.</typeparam>
        </member>
        <member name="M:FlaUI.Core.Patterns.InvokePatternBase`1.#ctor(FlaUI.Core.FrameworkAutomationElementBase,`0)">
            <summary>
            Creates the <see cref="T:FlaUI.Core.Patterns.IInvokePattern"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Patterns.InvokePatternBase`1.EventIds">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.Patterns.InvokePatternBase`1.Invoke">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.Patterns.IValuePattern.PropertyIds">
            <summary>
            Gets the object which provides access to all properties of this pattern.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Patterns.IValuePattern.IsReadOnly">
            <summary>
            Gets a value that specifies whether the value of the element is read-only.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Patterns.IValuePattern.Value">
            <summary>
            Gets the value of the element.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Patterns.IValuePattern.SetValue(System.String)">
            <summary>
            Sets the value of the control.
            </summary>
            <param name="value">The value to set.</param>
        </member>
        <member name="P:FlaUI.Core.Patterns.ValuePatternBase`1.PropertyIds">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.Patterns.ValuePatternBase`1.IsReadOnly">
            <inheritdoc />
        </member>
        <member name="P:FlaUI.Core.Patterns.ValuePatternBase`1.Value">
            <inheritdoc />
        </member>
        <member name="M:FlaUI.Core.Patterns.ValuePatternBase`1.SetValue(System.String)">
            <inheritdoc />
        </member>
        <member name="T:FlaUI.Core.Tools.Com">
            <summary>
            Wrapper for com interaction.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Tools.Com.UIA_E_ELEMENTNOTENABLED">
            <summary>
            Indicates that a method that requires an enabled element, such as ISelectionItemProvider::Select or IExpandCollapseProvider::Expand, was called on an element that was disabled.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Tools.Com.UIA_E_ELEMENTNOTAVAILABLE">
            <summary>
            Indicates that a method was called on a virtualized element, or on an element that no longer exists, usually because it has been destroyed.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Tools.Com.UIA_E_NOCLICKABLEPOINT">
            <summary>
            Indicates that the IUIAutomationElement::GetClickablePoint method was called on an element that has no clickable point.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Tools.Com.UIA_E_PROXYASSEMBLYNOTLOADED">
            <summary>
            Indicates that a problem occurred when loading an assembly that contains a client - side provider.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Tools.Com.UIA_E_NOTSUPPORTED">
            <summary>
            Indicates that the provider explicitly does not support the specified property or control pattern. UI Automation will return this error code to the caller without attempting to provide a default value or falling back to another provider.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Tools.Com.UIA_E_TIMEOUT">
            <summary>
            Indicates that the time allotted for a process or operation has expired.
            </summary>
        </member>
        <member name="F:FlaUI.Core.Tools.Com.UIA_E_INVALIDOPERATION">
            <summary>
            Indicates that the method attempted an operation that was not valid.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.Com.Call(System.Action)">
            <summary>
            Wraps an action with a com call and throws the correct win32 exception in case of an error.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.Com.CallWithHResult(System.Func{System.Int32})">
            <summary>
            Wraps an action which returns a HRESULT with a com call and throws the correct win32 exception in case of an error.
            or when the HRESULT is not 0.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.Com.Call``1(System.Func{``0})">
            <summary>
            Wraps an function with a com call and throws the correct win32 exception in case of an error.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.Com.ConvertException(System.Runtime.InteropServices.COMException,System.Exception@)">
            <summary>
            Tries to convert a com exception to a more usable exception.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Tools.ExtensionMethods">
            <summary>
            Provides various extension methods.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.Clamp``1(``0,``0,``0)">
            <summary>
            Makes sure a comparable object is between a given range.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.ToInt(System.Boolean)">
            <summary>
            Converts a boolean to an int
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.ToBool(System.Int32)">
            <summary>
            Converts an int to a boolean.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.ToInt(System.Double)">
            <summary>
            Converts a double to the nearest int32.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.ToInt(System.Enum)">
            <summary>
            Converts an enum value to an int.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.ToUInt(System.Enum)">
            <summary>
            Converts an enum value to an uint.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.Even(System.Int32)">
            <summary>
            Rounds the number down the the next even number.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.HasValue(System.Double)">
            <summary>
            Checks if a double is not NaN and not Infinity
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.GetFlags(System.Enum)">
            <summary>
            Gets a list of flags which are set in an <see cref="T:System.Enum"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.Distance(System.Drawing.Point,System.Drawing.Point)">
            <summary>
            Calculates the distance between two points.
            </summary>
            <param name="self">The first point.</param>
            <param name="other">The second point.</param>
            <returns>The distance of the points.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.Distance(System.Drawing.Point,System.Double,System.Double)">
            <summary>
            Calculates the distance between a point and an x/y coordinate pair.
            </summary>
            <param name="self">The first point.</param>
            <param name="otherX">The x-coordinate of the second point.</param>
            <param name="otherY">The x-coordinate of the second point.</param>
            <returns>The distance of the points.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.ToPOINT(System.Drawing.Point)">
            <summary>
            Converts the Point to a Win32-POINT structure.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ExtensionMethods.Even(System.Drawing.Rectangle)">
            <summary>
            Makes the rectangles dimensions a multiple of 2.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Tools.ItemRealizer">
            <summary>
            Helper class which tries to load all items for an item container.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.ItemRealizer.RealizeItems(FlaUI.Core.AutomationElements.AutomationElement)">
            <summary>
            Tries to realize all items in the given item container.
            </summary>
            <param name="itemContainerElement">The item container whose items should be realized.</param>
        </member>
        <member name="T:FlaUI.Core.Tools.LocalizedStrings">
            <summary>
            Static class which provides localized texts of some control texts.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.LocalizedStrings.HorizontalScrollBar">
            <summary>
            Name of a horizontal scrollbar.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.LocalizedStrings.VerticalScrollBar">
            <summary>
            Name of a vertical scrollbar.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.LocalizedStrings.TableHorizontalScrollBar">
            <summary>
            Name of a horizontal scrollbar in a table.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.LocalizedStrings.TableVerticalScrollBar">
            <summary>
            Name of a vertical scrollbar in a table.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.LocalizedStrings.WinFormsUIA2VerticalScrollBarName">
            <summary>
            Name of a WinForms vertical scrollbar in UIA2.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.LocalizedStrings.WinFormsUIA3VerticalScrollBarName">
            <summary>
            Name of a WinForms vertical scrollbar in UIA3.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.LocalizedStrings.WinFormsUIA2HorizontalScrollBarName">
            <summary>
            Name of a WinForms horizontal scrollbar in UIA2.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.LocalizedStrings.WinFormsUIA3HorizontalScrollBarName">
            <summary>
            Name of a WinForms horizontal scrollbar in UIA3.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.LocalizedStrings.DataGridViewHeader">
            <summary>
            Name of the header row in a DataGridView.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.LocalizedStrings.DataGridViewHeaderItemTopLeft">
            <summary>
            Name of the top-left header item.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Tools.OperatingSystem">
            <summary>
            Static class that can be used to get information about the current operating system.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.OperatingSystem.Version">
            <summary>
            The full version number of the current system.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.OperatingSystem.CurrentCulture">
            <summary>
            Gets the current <see cref="T:System.Globalization.CultureInfo"/>.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.OperatingSystem.Is64Bit">
            <summary>
            Determine if the OS is 32 or 64 bit.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.CurrentProductContains(System.String)">
            <summary>
            Checks if the current operating system name contains the given string.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.BrandingStringContains(System.String)">
            <summary>
            Checks if the current operating system name contains the given string.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.IsWindows8_1">
            <summary>
            Checks if the current operating system is Windows 8.1.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.IsWindows10">
            <summary>
            Checks if the current operating system is Windows 10.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.IsWindows11">
            <summary>
            Checks if the current operating system is Windows 11.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.IsWindowsServer2016">
            <summary>
            Checks if the current operating system is Windows Server 2016.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.IsWindowsServer2019">
            <summary>
            Checks if the current operating system is Windows Server 2019.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.GetProductName">
            <summary>
            Gets the product name in plain text.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.GetBuildNumber">
            <summary>
            Gets the internal build number.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.GetRelease">
            <summary>
            Gets the release (Windows 10).
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.GetVersion">
            <summary>
            Gets the version number.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.OperatingSystem.GetUpdateBuildRevision">
            <summary>
            Gets the current update build revision.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Tools.Retry">
            <summary>
            Static class with methods for retrying actions.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.Retry.DefaultTimeout">
            <summary>
            The default timeout to use for retries without a given timeout. The default is 1000ms.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.Retry.DefaultInterval">
            <summary>
            The default interval to use for retries without a given interval. The default is 100ms.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.While``1(System.Func{``0},System.Func{``0,System.Boolean},System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Boolean,System.Boolean,System.String,System.Boolean,``0)">
            <summary>
            Retries while the given method evaluates to true and returns the value from the method.
            If it fails, it returns the default of <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">The type of the return value.</typeparam>
            <param name="retryMethod">The method which is retried.</param>
            <param name="checkMethod">The method which is used to decide if a retry is needed or if the value is correct.</param>
            <param name="timeout">The timeout when the retry aborts.</param>
            <param name="interval">The interval of retries.</param>
            <param name="throwOnTimeout">A flag indicating if it should throw on timeout.</param>
            <param name="ignoreException">A flag indicating if it should retry on an exception.</param>
            <param name="timeoutMessage">The message that should be added to the timeout exception in case a timeout occurs.</param>
            <param name="lastValueOnTimeout">A flag indicating if the last value should be returned on timeout. Returns the default if the value could never be fetched.</param>
            <param name="defaultOnTimeout">Allows to define a default value in case of a timeout.</param>
            <returns>The value from <paramref name="retryMethod"/> or the default of <typeparamref name="T"/>.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.While``1(System.Func{``0},System.Func{``0,System.Boolean},FlaUI.Core.Tools.RetrySettings,System.Boolean,``0)">
            <summary>
            Retries while the given method evaluates to true and returns the value from the method.
            If it fails, it returns the default of <typeparamref name="T"/>.
            </summary>
            <param name="retryMethod">The method which is retried.</param>
            <param name="checkMethod">The method which is used to decide if a retry is needed or if the value is correct.</param>
            <param name="retrySettings">The settings to use for retrying.</param>
            <param name="lastValueOnTimeout">A flag indicating if the last value should be returned on timeout. Returns the default if the value could never be fetched.</param>
            <param name="defaultOnTimeout">Allows to define a default value in case of a timeout.</param>
            <typeparam name="T">The type of the return value.</typeparam>
            <returns>The value from <paramref name="retryMethod"/> or the default of <typeparamref name="T"/>.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.WhileNot``1(System.Func{``0},System.Func{``0,System.Boolean},System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Boolean,System.Boolean,System.String,System.Boolean,``0)">
            <summary>
            Retries while the given method evaluates to false and returns the value from the method.
            If it fails, it returns the default of <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">The type of the return value.</typeparam>
            <returns>The value from <paramref name="retryMethod"/> or the default of <typeparamref name="T"/>.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.WhileNot``1(System.Func{``0},System.Func{``0,System.Boolean},FlaUI.Core.Tools.RetrySettings,System.Boolean,``0)">
            <summary>
            Retries while the given method evaluates to false and returns the value from the method.
            If it fails, it returns the default of <typeparamref name="T"/>.
            </summary>
            <param name="retryMethod">The method which is retried.</param>
            <param name="checkMethod">The method which is used to decide if a retry is needed or if the value is correct.</param>
            <param name="retrySettings">The settings to use for retrying.</param>
            <param name="lastValueOnTimeout">A flag indicating if the last value should be returned on timeout. Returns the default if the value could never be fetched.</param>
            <param name="defaultOnTimeout">Allows to define a default value in case of a timeout.</param>
            <typeparam name="T">The type of the return value.</typeparam>
            <returns>The value from <paramref name="retryMethod"/> or the default of <typeparamref name="T"/>.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.WhileTrue(System.Func{System.Boolean},System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Boolean,System.Boolean,System.String)">
            <summary>
            Retries while the given method evaluates to true.
            </summary>
            <returns>True if the retry completed successfully within the time and false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.WhileFalse(System.Func{System.Boolean},System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Boolean,System.Boolean,System.String)">
            <summary>
            Retries while the given method evaluates to false.
            </summary>
            <returns>True if the retry completed successfully within the time and false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.WhileNull``1(System.Func{``0},System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Boolean,System.Boolean,System.String)">
            <summary>
            Retries while the given method evaluates to null.
            </summary>
            <returns>The value from <paramref name="checkMethod"/> or the default of <typeparamref name="T"/> in case of a timeout.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.WhileNotNull``1(System.Func{``0},System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Boolean,System.Boolean,System.String)">
            <summary>
            Retries while the given method evaluates to not null.
            </summary>
            <returns>True if it evaluated to null within the time or false in case of a timeout.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.WhileEmpty``1(System.Func{``0},System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Boolean,System.Boolean,System.String)">
            <summary>
            Retries while return value from the given method evaluates to null or has no elements.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.WhileEmpty(System.Func{System.String},System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Boolean,System.Boolean,System.String)">
            <summary>
            Retries while return value from the given method is null or an empty string.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.WhileException(System.Action,System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Boolean,System.String)">
            <summary>
            Retries while the given method has an exception.
            </summary>
            <returns>True if the method completed without exception, false otherwise.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.WhileException``1(System.Func{``0},System.Nullable{System.TimeSpan},System.Nullable{System.TimeSpan},System.Boolean,System.String)">
            <summary>
            Retries while the given method has an exception and returns the value from the method.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.IsTimeoutReached(System.DateTime,System.TimeSpan)">
            <summary>
            Method which checks if the timeout is reached.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.Find(System.Func{FlaUI.Core.AutomationElements.AutomationElement[]},FlaUI.Core.Tools.RetrySettings)">
            <summary>
            Allows searching with retrying for a list of <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>s.
            </summary>
            <param name="searchMethod">The method used to search for the element list.</param>
            <param name="retrySettings">The settings to use for retrying.</param>
            <returns>The list of found elements.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.Retry.Find(System.Func{FlaUI.Core.AutomationElements.AutomationElement},FlaUI.Core.Tools.RetrySettings)">
            <summary>
            Allows searching with retrying for an <see cref="T:FlaUI.Core.AutomationElements.AutomationElement"/>.
            </summary>
            <param name="searchMethod">The method used to search for the element.</param>
            <param name="retrySettings">The settings to use for retrying.</param>
            <returns>The found element.</returns>
        </member>
        <member name="T:FlaUI.Core.Tools.RetryResult`1">
            <summary>
            Class which represents a result when a retry method was used.
            </summary>
            <typeparam name="T">The type of the returned value from the retry.</typeparam>
        </member>
        <member name="M:FlaUI.Core.Tools.RetryResult`1.#ctor">
            <summary>
            Constructor for a new <see cref="T:FlaUI.Core.Tools.RetryResult`1"/>.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.RetryResult`1.#ctor(System.DateTime)">
            <summary>
            Constructor for a new <see cref="T:FlaUI.Core.Tools.RetryResult`1"/> with a given start time.
            </summary>
            <param name="manualStartTime">The start time to set.</param>
        </member>
        <member name="P:FlaUI.Core.Tools.RetryResult`1.StartTime">
            <summary>
            Date and time when the retry was started.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetryResult`1.EndTime">
            <summary>
            Date and time when the retry finished or aborted.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetryResult`1.TimedOut">
            <summary>
            Flag which indicates if the retry finished because it reached the timeout.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetryResult`1.Success">
            <summary>
            Flag which indicates if the retry finished successfully.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetryResult`1.LastException">
            <summary>
            Contains the last occured exception in the retry (if any). Only usefull if "ignoreException" is set to true on the retry.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetryResult`1.HadException">
            <summary>
            Flag which indicates if the retry had an exception or not.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetryResult`1.Result">
            <summary>
            Contains the final value returned by the retry.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetryResult`1.Duration">
            <summary>
            Time span how long the retry did run.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetryResult`1.Iterations">
            <summary>
            Contains the counter on how many iterations the retry did before returning.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.RetryResult`1.Finish(`0,System.Boolean)">
            <summary>
            Finishes the retry and sets the according values.
            </summary>
            <param name="result">The value to set as result.</param>
            <param name="timedOut">The flag which indicates if the retry timed out or not.</param>
            <returns>The object itself for fluent usage.</returns>
        </member>
        <member name="M:FlaUI.Core.Tools.RetryResult`1.SetException(System.Exception)">
            <summary>
            Sets the last exception.
            </summary>
            <param name="ex">The exception to set.</param>
            <returns>The object itself for fluent usage.</returns>
        </member>
        <member name="T:FlaUI.Core.Tools.RetrySettings">
            <summary>
            This object contains various settings that influence the behavior of retries.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetrySettings.Timeout">
            <summary>
            The timeout when the retry will abort if it did not succeed.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetrySettings.Interval">
            <summary>
            The interval of the retries.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetrySettings.ThrowOnTimeout">
            <summary>
            A flag indicating if it should throw an <see cref="T:System.TimeoutException"/> if the timeout is reached.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetrySettings.IgnoreException">
            <summary>
            A flag indicating if it should continue retrying when an exception occurs.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.RetrySettings.TimeoutMessage">
            <summary>
            The message that should be added to the timeout exception in case a timeout occurs.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Tools.StringFormatter">
            <summary>
            Class with methods that help formatting strings.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.StringFormatter.SizeSuffix(System.UInt64,System.Int32)">
            <summary>
            Adds size suffixes like KB, MB, GB, ...
            </summary>
        </member>
        <member name="T:FlaUI.Core.Tools.SystemInfo">
            <summary>
            Utility class to get various system information.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.MinimumRefreshInterval">
            <summary>
            As the operations to get the memory/cpu values are quite slow, this interval is used to not refresh too often.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.SystemInfo.RefreshAll">
            <summary>
            Refreshes all system information.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.SystemInfo.RefreshMemory">
            <summary>
            Refreshes the memory information.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.SystemInfo.RefreshCpu">
            <summary>
            Refreshes the CPU information.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.CpuUsage">
            <summary>
            The current CPU usage in percent.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.PhysicalMemoryTotal">
            <summary>
            The total physical memory in bytes.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.PhysicalMemoryFree">
            <summary>
            The physical memory that is free in bytes.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.PhysicalMemoryUsed">
            <summary>
            The physical memory that is used in bytes.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.PhysicalMemoryFreePercent">
            <summary>
            The physical memory that is free in percent.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.PhysicalMemoryUsedPercent">
            <summary>
            The physical memory that is used in percent.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.VirtualMemoryTotal">
            <summary>
            The total virtual memory in bytes.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.VirtualMemoryFree">
            <summary>
            The virtual memory that is free in bytes.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.VirtualMemoryUsed">
            <summary>
            The virtual memory that is used in bytes.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.VirtualMemoryFreePercent">
            <summary>
            The virtual memory that is free in percent.
            </summary>
        </member>
        <member name="P:FlaUI.Core.Tools.SystemInfo.VirtualMemoryUsedPercent">
            <summary>
            The virtual memory that is used in percent.
            </summary>
        </member>
        <member name="T:FlaUI.Core.Tools.WindowsStoreAppLauncher">
            <summary>
            Class with methods to launch windows store apps.
            </summary>
        </member>
        <member name="M:FlaUI.Core.Tools.WindowsStoreAppLauncher.Launch(System.String,System.String)">
            <summary>
            Launch the store app with the given id and arguments.
            </summary>
            <param name="appUserModelId">The app id of the application to launch.</param>
            <param name="arguments">The arguments to pass to the application.</param>
            <returns>The process of the launched application.</returns>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_ARRANGE">
            <summary>
            The flags that specify how the system arranged minimized windows. For more information, see the Remarks section in this topic.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CLEANBOOT">
            <summary>
            The value that specifies how the system is started:
            0 Normal boot
            1 Fail-safe boot
            2 Fail-safe with network boot
            A fail-safe boot (also called SafeBoot, Safe Mode, or Clean Boot) bypasses the user startup files.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CMONITORS">
            <summary>
            The number of display monitors on a desktop. For more information, see the Remarks section in this topic.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CMOUSEBUTTONS">
            <summary>
            The number of buttons on a mouse, or zero if no mouse is installed.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CONVERTIBLESLATEMODE">
            <summary>
            Reflects the state of the laptop or slate mode, 0 for Slate Mode and non-zero otherwise. When this system metric changes, the system sends a broadcast message via WM_SETTINGCHANGE with "ConvertibleSlateMode" in the LPARAM. Note that this system metric doesn't apply to desktop PCs. In that case, use GetAutoRotationState.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXBORDER">
            <summary>
            The width of a window border, in pixels. This is equivalent to the SM_CXEDGE value for windows with the 3-D look.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXCURSOR">
            <summary>
            The width of a cursor, in pixels. The system cannot create cursors of other sizes.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXDLGFRAME">
            <summary>
            This value is the same as SM_CXFIXEDFRAME.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXDOUBLECLK" -->
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXDRAG">
            <summary>
            The number of pixels on either side of a mouse-down point that the mouse pointer can move before a drag operation begins. This allows the user to click and release the mouse button easily without unintentionally starting a drag operation. If this value is negative, it is subtracted from the left of the mouse-down point and added to the right of it.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXEDGE">
            <summary>
            The width of a 3-D border, in pixels. This metric is the 3-D counterpart of SM_CXBORDER.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXFIXEDFRAME" -->
        <!-- Badly formed XML comment ignored for member "F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXFOCUSBORDER" -->
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXFRAME">
            <summary>
            This value is the same as SM_CXSIZEFRAME.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXFULLSCREEN">
            <summary>
            The width of the client area for a full-screen window on the primary display monitor, in pixels. To get the coordinates of the portion of the screen that is not obscured by the system taskbar or by application desktop toolbars, call the SystemParametersInfo function with the SPI_GETWORKAREA value.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXHSCROLL">
            <summary>
            The width of the arrow bitmap on a horizontal scroll bar, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXHTHUMB">
            <summary>
            The width of the thumb box in a horizontal scroll bar, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXICON">
            <summary>
            The default width of an icon, in pixels. The LoadIcon function can load only icons with the dimensions that SM_CXICON and SM_CYICON specifies.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXICONSPACING">
            <summary>
            The width of a grid cell for items in large icon view, in pixels. Each item fits into a rectangle of size SM_CXICONSPACING by SM_CYICONSPACING when arranged. This value is always greater than or equal to SM_CXICON.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXMAXIMIZED">
            <summary>
            The default width, in pixels, of a maximized top-level window on the primary display monitor.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXMAXTRACK">
            <summary>
            The default maximum width of a window that has a caption and sizing borders, in pixels. This metric refers to the entire desktop. The user cannot drag the window frame to a size larger than these dimensions. A window can override this value by processing the WM_GETMINMAXINFO message.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXMENUCHECK">
            <summary>
            The width of the default menu check-mark bitmap, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXMENUSIZE">
            <summary>
            The width of menu bar buttons, such as the child window close button that is used in the multiple document interface, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXMIN">
            <summary>
            The minimum width of a window, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXMINIMIZED">
            <summary>
            The width of a minimized window, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXMINSPACING">
            <summary>
            The width of a grid cell for a minimized window, in pixels. Each minimized window fits into a rectangle this size when arranged. This value is always greater than or equal to SM_CXMINIMIZED.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXMINTRACK">
            <summary>
            The minimum tracking width of a window, in pixels. The user cannot drag the window frame to a size smaller than these dimensions. A window can override this value by processing the WM_GETMINMAXINFO message.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXPADDEDBORDER" -->
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXSCREEN">
            <summary>
            The width of the screen of the primary display monitor, in pixels. This is the same value obtained by calling GetDeviceCaps as follows: GetDeviceCaps( hdcPrimaryMonitor, HORZRES).
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXSIZE">
            <summary>
            The width of a button in a window caption or title bar, in pixels.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXSIZEFRAME" -->
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXSMICON">
            <summary>
            The recommended width of a small icon, in pixels. Small icons typically appear in window captions and in small icon view.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXSMSIZE">
            <summary>
            The width of small caption buttons, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXVIRTUALSCREEN">
            <summary>
            The width of the virtual screen, in pixels. The virtual screen is the bounding rectangle of all display monitors. The SM_XVIRTUALSCREEN metric is the coordinates for the left side of the virtual screen.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CXVSCROLL">
            <summary>
            The width of a vertical scroll bar, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYBORDER">
            <summary>
            The height of a window border, in pixels. This is equivalent to the SM_CYEDGE value for windows with the 3-D look.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYCAPTION">
            <summary>
            The height of a caption area, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYCURSOR">
            <summary>
            The height of a cursor, in pixels. The system cannot create cursors of other sizes.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYDLGFRAME">
            <summary>
            This value is the same as SM_CYFIXEDFRAME.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYDOUBLECLK" -->
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYDRAG">
            <summary>
            The number of pixels above and below a mouse-down point that the mouse pointer can move before a drag operation begins. This allows the user to click and release the mouse button easily without unintentionally starting a drag operation. If this value is negative, it is subtracted from above the mouse-down point and added below it.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYEDGE">
            <summary>
            The height of a 3-D border, in pixels. This is the 3-D counterpart of SM_CYBORDER.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYFIXEDFRAME" -->
        <!-- Badly formed XML comment ignored for member "F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYFOCUSBORDER" -->
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYFRAME">
            <summary>
            This value is the same as SM_CYSIZEFRAME.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYFULLSCREEN">
            <summary>
            The height of the client area for a full-screen window on the primary display monitor, in pixels. To get the coordinates of the portion of the screen not obscured by the system taskbar or by application desktop toolbars, call the SystemParametersInfo function with the SPI_GETWORKAREA value.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYHSCROLL">
            <summary>
            The height of a horizontal scroll bar, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYICON">
            <summary>
            The default height of an icon, in pixels. The LoadIcon function can load only icons with the dimensions SM_CXICON and SM_CYICON.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYICONSPACING">
            <summary>
            The height of a grid cell for items in large icon view, in pixels. Each item fits into a rectangle of size SM_CXICONSPACING by SM_CYICONSPACING when arranged. This value is always greater than or equal to SM_CYICON.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYKANJIWINDOW">
            <summary>
            For double byte character set versions of the system, this is the height of the Kanji window at the bottom of the screen, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYMAXIMIZED">
            <summary>
            The default height, in pixels, of a maximized top-level window on the primary display monitor.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYMAXTRACK">
            <summary>
            The default maximum height of a window that has a caption and sizing borders, in pixels. This metric refers to the entire desktop. The user cannot drag the window frame to a size larger than these dimensions. A window can override this value by processing the WM_GETMINMAXINFO message.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYMENU">
            <summary>
            The height of a single-line menu bar, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYMENUCHECK">
            <summary>
            The height of the default menu check-mark bitmap, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYMENUSIZE">
            <summary>
            The height of menu bar buttons, such as the child window close button that is used in the multiple document interface, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYMIN">
            <summary>
            The minimum height of a window, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYMINIMIZED">
            <summary>
            The height of a minimized window, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYMINSPACING">
            <summary>
            The height of a grid cell for a minimized window, in pixels. Each minimized window fits into a rectangle this size when arranged. This value is always greater than or equal to SM_CYMINIMIZED.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYMINTRACK">
            <summary>
            The minimum tracking height of a window, in pixels. The user cannot drag the window frame to a size smaller than these dimensions. A window can override this value by processing the WM_GETMINMAXINFO message.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYSCREEN">
            <summary>
            The height of the screen of the primary display monitor, in pixels. This is the same value obtained by calling GetDeviceCaps as follows: GetDeviceCaps( hdcPrimaryMonitor, VERTRES).
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYSIZE">
            <summary>
            The height of a button in a window caption or title bar, in pixels.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYSIZEFRAME" -->
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYSMCAPTION">
            <summary>
            The height of a small caption, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYSMICON">
            <summary>
            The recommended height of a small icon, in pixels. Small icons typically appear in window captions and in small icon view.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYSMSIZE">
            <summary>
            The height of small caption buttons, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYVIRTUALSCREEN">
            <summary>
            The height of the virtual screen, in pixels. The virtual screen is the bounding rectangle of all display monitors. The SM_YVIRTUALSCREEN metric is the coordinates for the top of the virtual screen.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYVSCROLL">
            <summary>
            The height of the arrow bitmap on a vertical scroll bar, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_CYVTHUMB">
            <summary>
            The height of the thumb box in a vertical scroll bar, in pixels.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_DBCSENABLED">
            <summary>
            Nonzero if User32.dll supports DBCS; otherwise, 0.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_DEBUG">
            <summary>
            Nonzero if the debug version of User.exe is installed; otherwise, 0.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_DIGITIZER">
            <summary>
            Nonzero if the current operating system is Windows 7 or Windows Server 2008 R2 and the Tablet PC Input service is started; otherwise, 0. The return value is a bitmask that specifies the type of digitizer input supported by the device. For more information, see Remarks.
            Windows Server 2008, Windows Vista and Windows XP/2000:  This value is not supported.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_IMMENABLED">
            <summary>
            Nonzero if Input Method Manager/Input Method Editor features are enabled; otherwise, 0.
            SM_IMMENABLED indicates whether the system is ready to use a Unicode-based IME on a Unicode application. To ensure that a language-dependent IME works, check SM_DBCSENABLED and the system ANSI code page. Otherwise the ANSI-to-Unicode conversion may not be performed correctly, or some components like fonts or registry settings may not be present.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_MAXIMUMTOUCHES">
            <summary>
            Nonzero if there are digitizers in the system; otherwise, 0.
            SM_MAXIMUMTOUCHES returns the aggregate maximum of the maximum number of contacts supported by every digitizer in the system. If the system has only single-touch digitizers, the return value is 1. If the system has multi-touch digitizers, the return value is the number of simultaneous contacts the hardware can provide.
            Windows Server 2008, Windows Vista and Windows XP/2000:  This value is not supported.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_MEDIACENTER">
            <summary>
            Nonzero if the current operating system is the Windows XP, Media Center Edition, 0 if not.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_MENUDROPALIGNMENT">
            <summary>
            Nonzero if drop-down menus are right-aligned with the corresponding menu-bar item; 0 if the menus are left-aligned.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_MIDEASTENABLED">
            <summary>
            Nonzero if the system is enabled for Hebrew and Arabic languages, 0 if not.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_MOUSEPRESENT">
            <summary>
            Nonzero if a mouse is installed; otherwise, 0. This value is rarely zero, because of support for virtual mice and because some systems detect the presence of the port instead of the presence of a mouse.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_MOUSEHORIZONTALWHEELPRESENT">
            <summary>
            Nonzero if a mouse with a horizontal scroll wheel is installed; otherwise 0.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_MOUSEWHEELPRESENT">
            <summary>
            Nonzero if a mouse with a vertical scroll wheel is installed; otherwise 0.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_NETWORK">
            <summary>
            The least significant bit is set if a network is present; otherwise, it is cleared. The other bits are reserved for future use.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_PENWINDOWS">
            <summary>
            Nonzero if the Microsoft Windows for Pen computing extensions are installed; zero otherwise.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_REMOTECONTROL">
            <summary>
            This system metric is used in a Terminal Services environment to determine if the current Terminal Server session is being remotely controlled. Its value is nonzero if the current session is remotely controlled; otherwise, 0.
            You can use terminal services management tools such as Terminal Services Manager (tsadmin.msc) and shadow.exe to control a remote session. When a session is being remotely controlled, another user can view the contents of that session and potentially interact with it.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_REMOTESESSION">
            <summary>
            This system metric is used in a Terminal Services environment. If the calling process is associated with a Terminal Services client session, the return value is nonzero. If the calling process is associated with the Terminal Services console session, the return value is 0. Windows Server 2003 and Windows XP:  The console session is not necessarily the physical console. For more information, see WTSGetActiveConsoleSessionId.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_SAMEDISPLAYFORMAT">
            <summary>
            Nonzero if all the display monitors have the same color format, otherwise, 0. Two displays can have the same bit depth, but different color formats. For example, the red, green, and blue pixels can be encoded with different numbers of bits, or those bits can be located in different places in a pixel color value.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_SECURE">
            <summary>
            This system metric should be ignored; it always returns 0.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_SERVERR2">
            <summary>
            The build number if the system is Windows Server 2003 R2; otherwise, 0.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_SHOWSOUNDS">
            <summary>
            Nonzero if the user requires an application to present information visually in situations where it would otherwise present the information only in audible form; otherwise, 0.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_SHUTTINGDOWN">
            <summary>
            Nonzero if the current session is shutting down; otherwise, 0.
            Windows 2000:  This value is not supported.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_SLOWMACHINE">
            <summary>
            Nonzero if the computer has a low-end (slow) processor; otherwise, 0.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_STARTER">
            <summary>
            Nonzero if the current operating system is Windows 7 Starter Edition, Windows Vista Starter, or Windows XP Starter Edition; otherwise, 0.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_SWAPBUTTON">
            <summary>
            Nonzero if the meanings of the left and right mouse buttons are swapped; otherwise, 0.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_SYSTEMDOCKED">
            <summary>
            Reflects the state of the docking mode, 0 for Undocked Mode and non-zero otherwise. When this system metric changes, the system sends a broadcast message via WM_SETTINGCHANGE with "SystemDockMode" in the LPARAM.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_TABLETPC">
            <summary>
            Nonzero if the current operating system is the Windows XP Tablet PC edition or if the current operating system is Windows Vista or Windows 7 and the Tablet PC Input service is started; otherwise, 0. The SM_DIGITIZER setting indicates the type of digitizer input supported by a device running Windows 7 or Windows Server 2008 R2. For more information, see Remarks.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_XVIRTUALSCREEN">
            <summary>
            The coordinates for the left side of the virtual screen. The virtual screen is the bounding rectangle of all display monitors. The SM_CXVIRTUALSCREEN metric is the width of the virtual screen.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.SystemMetric.SM_YVIRTUALSCREEN">
            <summary>
            The coordinates for the top of the virtual screen. The virtual screen is the bounding rectangle of all display monitors. The SM_CYVIRTUALSCREEN metric is the height of the virtual screen.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.LBUTTON">
            <summary>
            Left mouse button
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.RBUTTON">
            <summary>
            Right mouse button
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.CANCEL">
            <summary>
            Control-break processing
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.MBUTTON">
            <summary>
            Middle mouse button (three-button mouse)
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.XBUTTON1">
            <summary>
            Windows 2000/XP: X1 mouse button
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.XBUTTON2">
            <summary>
            Windows 2000/XP: X2 mouse button
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.BACK">
            <summary>
            BACKSPACE key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.TAB">
            <summary>
            TAB key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.CLEAR">
            <summary>
            CLEAR key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.RETURN">
            <summary>
            ENTER key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.SHIFT">
            <summary>
            SHIFT key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.CONTROL">
            <summary>
            CTRL key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.ALT">
            <summary>
            ALT key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.PAUSE">
            <summary>
            PAUSE key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.CAPITAL">
            <summary>
            CAPS LOCK key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KANA">
            <summary>
            Input Method Editor (IME) Kana mode
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.HANGUL">
            <summary>
            IME Hangul mode
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.JUNJA">
            <summary>
            IME Junja mode
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.FINAL">
            <summary>
            IME final mode
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.HANJA">
            <summary>
            IME Hanja mode
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KANJI">
            <summary>
            IME Kanji mode
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.ESCAPE">
            <summary>
            ESC key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.CONVERT">
            <summary>
            IME convert
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NONCONVERT">
            <summary>
            IME nonconvert
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.ACCEPT">
            <summary>
            IME accept
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.MODECHANGE">
            <summary>
            IME mode change request
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.SPACE">
            <summary>
            SPACEBAR
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.PRIOR">
            <summary>
            PAGE UP key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NEXT">
            <summary>
            PAGE DOWN key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.END">
            <summary>
            END key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.HOME">
            <summary>
            HOME key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.LEFT">
            <summary>
            LEFT ARROW key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.UP">
            <summary>
            UP ARROW key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.RIGHT">
            <summary>
            RIGHT ARROW key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.DOWN">
            <summary>
            DOWN ARROW key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.SELECT">
            <summary>
            SELECT key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.PRINT">
            <summary>
            PRINT key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.EXECUTE">
            <summary>
            EXECUTE key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.SNAPSHOT">
            <summary>
            PRINT SCREEN key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.INSERT">
            <summary>
            INS key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.DELETE">
            <summary>
            DEL key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.HELP">
            <summary>
            HELP key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_0">
            <summary>
            0 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_1">
            <summary>
            1 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_2">
            <summary>
            2 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_3">
            <summary>
            3 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_4">
            <summary>
            4 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_5">
            <summary>
            5 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_6">
            <summary>
            6 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_7">
            <summary>
            7 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_8">
            <summary>
            8 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_9">
            <summary>
            9 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_A">
            <summary>
            A key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_B">
            <summary>
            B key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_C">
            <summary>
            C key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_D">
            <summary>
            D key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_E">
            <summary>
            E key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_F">
            <summary>
            F key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_G">
            <summary>
            G key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_H">
            <summary>
            H key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_I">
            <summary>
            I key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_J">
            <summary>
            J key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_K">
            <summary>
            K key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_L">
            <summary>
            L key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_M">
            <summary>
            M key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_N">
            <summary>
            N key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_O">
            <summary>
            O key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_P">
            <summary>
            P key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_Q">
            <summary>
            Q key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_R">
            <summary>
            R key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_S">
            <summary>
            S key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_T">
            <summary>
            T key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_U">
            <summary>
            U key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_V">
            <summary>
            V key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_W">
            <summary>
            W key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_X">
            <summary>
            X key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_Y">
            <summary>
            Y key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.KEY_Z">
            <summary>
            Z key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.LWIN">
            <summary>
            Left Windows key (Microsoft Natural keyboard)
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.RWIN">
            <summary>
            Right Windows key (Natural keyboard)
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.APPS">
            <summary>
            Applications key (Natural keyboard)
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.SLEEP">
            <summary>
            Computer Sleep key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMPAD0">
            <summary>
            Numeric keypad 0 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMPAD1">
            <summary>
            Numeric keypad 1 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMPAD2">
            <summary>
            Numeric keypad 2 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMPAD3">
            <summary>
            Numeric keypad 3 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMPAD4">
            <summary>
            Numeric keypad 4 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMPAD5">
            <summary>
            Numeric keypad 5 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMPAD6">
            <summary>
            Numeric keypad 6 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMPAD7">
            <summary>
            Numeric keypad 7 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMPAD8">
            <summary>
            Numeric keypad 8 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMPAD9">
            <summary>
            Numeric keypad 9 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.MULTIPLY">
            <summary>
            Multiply key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.ADD">
            <summary>
            Add key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.SEPARATOR">
            <summary>
            Separator key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.SUBTRACT">
            <summary>
            Subtract key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.DECIMAL">
            <summary>
            Decimal key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.DIVIDE">
            <summary>
            Divide key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F1">
            <summary>
            F1 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F2">
            <summary>
            F2 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F3">
            <summary>
            F3 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F4">
            <summary>
            F4 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F5">
            <summary>
            F5 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F6">
            <summary>
            F6 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F7">
            <summary>
            F7 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F8">
            <summary>
            F8 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F9">
            <summary>
            F9 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F10">
            <summary>
            F10 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F11">
            <summary>
            F11 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F12">
            <summary>
            F12 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F13">
            <summary>
            F13 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F14">
            <summary>
            F14 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F15">
            <summary>
            F15 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F16">
            <summary>
            F16 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F17">
            <summary>
            F17 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F18">
            <summary>
            F18 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F19">
            <summary>
            F19 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F20">
            <summary>
            F20 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F21">
            <summary>
            F21 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F22">
            <summary>
            F22 key, (PPC only) Key used to lock device.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F23">
            <summary>
            F23 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.F24">
            <summary>
            F24 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NUMLOCK">
            <summary>
            NUM LOCK key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.SCROLL">
            <summary>
            SCROLL LOCK key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.LSHIFT">
            <summary>
            Left SHIFT key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.RSHIFT">
            <summary>
            Right SHIFT key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.LCONTROL">
            <summary>
            Left CONTROL key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.RCONTROL">
            <summary>
            Right CONTROL key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.LMENU">
            <summary>
            Left MENU key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.RMENU">
            <summary>
            Right MENU key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.BROWSER_BACK">
            <summary>
            Windows 2000/XP: Browser Back key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.BROWSER_FORWARD">
            <summary>
            Windows 2000/XP: Browser Forward key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.BROWSER_REFRESH">
            <summary>
            Windows 2000/XP: Browser Refresh key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.BROWSER_STOP">
            <summary>
            Windows 2000/XP: Browser Stop key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.BROWSER_SEARCH">
            <summary>
            Windows 2000/XP: Browser Search key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.BROWSER_FAVORITES">
            <summary>
            Windows 2000/XP: Browser Favorites key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.BROWSER_HOME">
            <summary>
            Windows 2000/XP: Browser Start and Home key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.VOLUME_MUTE">
            <summary>
            Windows 2000/XP: Volume Mute key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.VOLUME_DOWN">
            <summary>
            Windows 2000/XP: Volume Down key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.VOLUME_UP">
            <summary>
            Windows 2000/XP: Volume Up key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.MEDIA_NEXT_TRACK">
            <summary>
            Windows 2000/XP: Next Track key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.MEDIA_PREV_TRACK">
            <summary>
            Windows 2000/XP: Previous Track key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.MEDIA_STOP">
            <summary>
            Windows 2000/XP: Stop Media key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.MEDIA_PLAY_PAUSE">
            <summary>
            Windows 2000/XP: Play/Pause Media key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.LAUNCH_MAIL">
            <summary>
            Windows 2000/XP: Start Mail key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.LAUNCH_MEDIA_SELECT">
            <summary>
            Windows 2000/XP: Select Media key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.LAUNCH_APP1">
            <summary>
            Windows 2000/XP: Start Application 1 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.LAUNCH_APP2">
            <summary>
            Windows 2000/XP: Start Application 2 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_1">
            <summary>
            Used for miscellaneous characters; it can vary by keyboard.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_PLUS">
            <summary>
            Windows 2000/XP: For any country/region, the '+' key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_COMMA">
            <summary>
            Windows 2000/XP: For any country/region, the ',' key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_MINUS">
            <summary>
            Windows 2000/XP: For any country/region, the '-' key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_PERIOD">
            <summary>
            Windows 2000/XP: For any country/region, the '.' key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_2">
            <summary>
            Used for miscellaneous characters; it can vary by keyboard.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_3">
            <summary>
            Used for miscellaneous characters; it can vary by keyboard.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_4">
            <summary>
            Used for miscellaneous characters; it can vary by keyboard.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_5">
            <summary>
            Used for miscellaneous characters; it can vary by keyboard.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_6">
            <summary>
            Used for miscellaneous characters; it can vary by keyboard.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_7">
            <summary>
            Used for miscellaneous characters; it can vary by keyboard.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_8">
            <summary>
            Used for miscellaneous characters; it can vary by keyboard.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_102">
            <summary>
            Windows 2000/XP: Either the angle bracket key or the backslash key on the RT 102-key keyboard
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.PROCESSKEY">
            <summary>
            Windows 95/98/Me, Windows NT 4.0, Windows 2000/XP: IME PROCESS key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.PACKET">
            <summary>
            Windows 2000/XP: Used to pass Unicode characters as if they were keystrokes.
            The VK_PACKET key is the low word of a 32-bit Virtual Key value used for non-keyboard input methods. For more
            information,
            see Remark in KEYBDINPUT, SendInput, WM_KEYDOWN, and WM_KEYUP
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.ATTN">
            <summary>
            Attn key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.CRSEL">
            <summary>
            CrSel key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.EXSEL">
            <summary>
            ExSel key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.EREOF">
            <summary>
            Erase EOF key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.PLAY">
            <summary>
            Play key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.ZOOM">
            <summary>
            Zoom key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.NONAME">
            <summary>
            Reserved
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.PA1">
            <summary>
            PA1 key
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.VirtualKeyShort.OEM_CLEAR">
            <summary>
            Clear key
            </summary>
        </member>
        <member name="T:FlaUI.Core.WindowsAPI.ScanCodeShort">
            <summary>
            These are the hardware keyboard codes
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.CursorState.CURSOR_HIDING">
            <summary>
            The cursor is hidden.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.CursorState.CURSOR_SHOWING">
            <summary>
            The cursor is showing.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.CursorState.CURSOR_SUPPRESSED">
            <summary>
            Windows 8: The cursor is suppressed. This flag indicates that the system is not drawing
            the cursor because the user is providing input through touch or pen instead of the mouse.
            </summary>
        </member>
        <member name="T:FlaUI.Core.WindowsAPI.TernaryRasterOperations">
            <summary>
            Specifies a raster-operation code. These codes define how the color data for the
            source rectangle is to be combined with the color data for the destination
            rectangle to achieve the final color.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.SRCCOPY">
            <summary>dest = source</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.SRCPAINT">
            <summary>dest = source OR dest</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.SRCAND">
            <summary>dest = source AND dest</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.SRCINVERT">
            <summary>dest = source XOR dest</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.SRCERASE">
            <summary>dest = source AND (NOT dest)</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.NOTSRCCOPY">
            <summary>dest = (NOT source)</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.NOTSRCERASE">
            <summary>dest = (NOT src) AND (NOT dest)</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.MERGECOPY">
            <summary>dest = (source AND pattern)</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.MERGEPAINT">
            <summary>dest = (NOT source) OR dest</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.PATCOPY">
            <summary>dest = pattern</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.PATPAINT">
            <summary>dest = DPSnoo</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.PATINVERT">
            <summary>dest = pattern XOR dest</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.DSTINVERT">
            <summary>dest = (NOT dest)</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.BLACKNESS">
            <summary>dest = BLACK</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.WHITENESS">
            <summary>dest = WHITE</summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TernaryRasterOperations.CAPTUREBLT">
            <summary>
            Capture window as seen on screen. This includes layered windows
            such as WPF windows with AllowsTransparency="true"
            </summary>
        </member>
        <member name="T:FlaUI.Core.WindowsAPI.InjectedInputVisualizationMode">
            <summary>
            Specifies the type of visual feedback displayed for the injected input type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.InjectedInputVisualizationMode.DEFAULT">
            <summary>
            Specifies default touch visualizations.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.InjectedInputVisualizationMode.INDIREC">
            <summary>
            Specifies indirect touch visualizations.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.InjectedInputVisualizationMode.NONE">
            <summary>
            Specifies no touch visualizations.
            </summary>
        </member>
        <member name="T:FlaUI.Core.WindowsAPI.PointerInputType">
            <summary>
            Identifies the pointer input types.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerInputType.PT_POINTER">
            <summary>
            Generic pointer type. This type never appears in pointer messages or pointer data. Some data query functions allow the caller to restrict the query to specific pointer type.
            The PT_POINTER type can be used in these functions to specify that the query is to include pointers of all types
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerInputType.PT_TOUCH">
            <summary>
            Touch pointer type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerInputType.PT_PEN">
            <summary>
            Pen pointer type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerInputType.PT_MOUSE">
            <summary>
            Mouse pointer type.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerInputType.PT_TOUCHPAD">
            <summary>
            Touchpad pointer type (Windows 8.1 and later).
            </summary>
        </member>
        <member name="T:FlaUI.Core.WindowsAPI.PointerFlags">
            <summary>
            Values that can appear in the pointerFlags field of the POINTER_INFO structure.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.NONE">
            <summary>
            Default.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.NEW">
            <summary>
            Indicates the arrival of a new pointer.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.INRANGE">
            <summary>
            Indicates that this pointer continues to exist. When this flag is not set, it indicates the pointer has left detection range.
            This flag is typically not set only when a hovering pointer leaves detection range (POINTER_FLAG_UPDATE is set) or when a pointer in contact with a window surface leaves detection range(POINTER_FLAG_UP is set).
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.INCONTACT">
            <summary>
            Indicates that this pointer is in contact with the digitizer surface. When this flag is not set, it indicates a hovering pointer.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.FIRSTBUTTON">
            <summary>
            Indicates a primary action, analogous to a left mouse button down.
            A touch pointer has this flag set when it is in contact with the digitizer surface.
            A pen pointer has this flag set when it is in contact with the digitizer surface with no buttons pressed.
            A mouse pointer has this flag set when the left mouse button is down.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.SECONDBUTTON">
            <summary>
            Indicates a secondary action, analogous to a right mouse button down.
            A touch pointer does not use this flag.
            A pen pointer has this flag set when it is in contact with the digitizer surface with the pen barrel button pressed.
            A mouse pointer has this flag set when the right mouse button is down.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.THIRDBUTTON">
            <summary>
            Analogous to a mouse wheel button down.
            A touch pointer does not use this flag.
            A pen pointer does not use this flag.
            A mouse pointer has this flag set when the mouse wheel button is down.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.FOURTHBUTTON">
            <summary>
            Analogous to a first extended mouse (XButton1) button down.
            A touch pointer does not use this flag.
            A pen pointer does not use this flag.
            A mouse pointer has this flag set when the first extended mouse(XBUTTON1) button is down.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.FIFTHBUTTON">
            <summary>
            Analogous to a first extended mouse (XButton2) button down.
            A touch pointer does not use this flag.
            A pen pointer does not use this flag.
            A mouse pointer has this flag set when the first extended mouse(XBUTTON2) button is down.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.PRIMARY">
            <summary>
            Indicates that this pointer has been designated as the primary pointer. A primary pointer is a single pointer that can perform actions beyond those available to non-primary pointers.
            For example, when a primary pointer makes contact with a window s surface, it may provide the window an opportunity to activate by sending it a WM_POINTERACTIVATE message.
            The primary pointer is identified from all current user interactions on the system (mouse, touch, pen, and so on).
            As such, the primary pointer might not be associated with your app.The first contact in a multi-touch interaction is set as the primary pointer.Once a primary pointer is identified, all contacts must be lifted before a new contact can be identified as a primary pointer.For apps that don't process pointer input, only the primary pointer's events are promoted to mouse events.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.CONFIDENCE">
            <summary>
            Confidence is a suggestion from the source device about whether the pointer represents an intended or accidental interaction, which is especially relevant for PT_TOUCH pointers where an accidental interaction (such as with the palm of the hand) can trigger input. The presence of this flag indicates that the source device has high confidence that this input is part of an intended interaction.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.CANCELLED">
            <summary>
            Indicates that the pointer is departing in an abnormal manner, such as when the system receives invalid input for the pointer or when a device with active pointers departs abruptly.
            If the application receiving the input is in a position to do so, it should treat the interaction as not completed and reverse any effects of the concerned pointer.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.DOWN">
            <summary>
            Indicates that this pointer transitioned to a down state; that is, it made contact with the digitizer surface.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.UPDATE">
            <summary>
            Indicates that this is a simple update that does not include pointer state changes.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.UP">
            <summary>
            Indicates that this pointer transitioned to an up state; that is, contact with the digitizer surface ended.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.WHEEL">
            <summary>
            Indicates input associated with a pointer wheel. For mouse pointers, this is equivalent to the action of the mouse scroll wheel (WM_MOUSEHWHEEL).
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.HWHEEL">
            <summary>
            Indicates input associated with a pointer h-wheel. For mouse pointers, this is equivalent to the action of the mouse horizontal scroll wheel (WM_MOUSEHWHEEL).
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.CAPTURECHANGED">
            <summary>
            Indicates that this pointer was captured by (associated with) another element and the original element has lost capture (see WM_POINTERCAPTURECHANGED).
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerFlags.HASTRANSFORM">
            <summary>
            Indicates that this pointer has an associated transform.
            </summary>
        </member>
        <member name="T:FlaUI.Core.WindowsAPI.PointerButtonChangeType">
            <summary>
            Identifies a change in the state of a button associated with a pointer.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.NONE">
            <summary>
            No change in button state.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.FIRSTBUTTON_DOWN">
            <summary>
            The first button (see POINTER_FLAG_FIRSTBUTTON) transitioned to a pressed state.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.FIRSTBUTTON_UP">
            <summary>
            The first button (see POINTER_FLAG_FIRSTBUTTON) transitioned to a released state.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.SECONDBUTTON_DOWN">
            <summary>
            The second button (see POINTER_FLAG_SECONDBUTTON) transitioned to a pressed state.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.SECONDBUTTON_UP">
            <summary>
            The second button (see POINTER_FLAG_SECONDBUTTON) transitioned to a released state.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.THIRDBUTTON_DOWN">
            <summary>
            The third button (see POINTER_FLAG_THIRDBUTTON) transitioned to a pressed state.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.THIRDBUTTON_UP">
            <summary>
            The third button (see POINTER_FLAG_THIRDBUTTON) transitioned to a released state.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.FOURTHBUTTON_DOWN">
            <summary>
            The fourth button (see POINTER_FLAG_FOURTHBUTTON) transitioned to a pressed state.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.FOURTHBUTTON_UP">
            <summary>
            The fourth button (see POINTER_FLAG_FOURTHBUTTON) transitioned to a released state.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.FIFTHBUTTON_DOWN">
            <summary>
            The fifth button (see POINTER_FLAG_FIFTHBUTTON) transitioned to a pressed state.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.PointerButtonChangeType.FIFTHBUTTON_UP">
            <summary>
            The fifth button (see POINTER_FLAG_FIFTHBUTTON) transitioned to a released state.
            </summary>
        </member>
        <member name="T:FlaUI.Core.WindowsAPI.TouchFlags">
            <summary>
            Values that can appear in the touchFlags field of the POINTER_TOUCH_INFO structure.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TouchFlags.NONE">
            <summary>
            The default value.
            </summary>
        </member>
        <member name="T:FlaUI.Core.WindowsAPI.TouchMask">
            <summary>
            Values that can appear in the touchMask field of the POINTER_TOUCH_INFO structure.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TouchMask.NONE">
            <summary>
            Default. None of the optional fields are valid.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TouchMask.CONTACTAREA">
            <summary>
            <see cref="F:FlaUI.Core.WindowsAPI.POINTER_TOUCH_INFO.rcContact"/> of the <see cref="T:FlaUI.Core.WindowsAPI.POINTER_TOUCH_INFO"/> structure is valid.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TouchMask.ORIENTATION">
            <summary>
            <see cref="F:FlaUI.Core.WindowsAPI.POINTER_TOUCH_INFO.orientation"/> of the <see cref="T:FlaUI.Core.WindowsAPI.POINTER_TOUCH_INFO"/> structure is valid.
            </summary>
        </member>
        <member name="F:FlaUI.Core.WindowsAPI.TouchMask.PRESSURE">
            <summary>
            <see cref="F:FlaUI.Core.WindowsAPI.POINTER_TOUCH_INFO.pressure"/> of the <see cref="T:FlaUI.Core.WindowsAPI.POINTER_TOUCH_INFO"/> structure is valid.
            </summary>
        </member>
        <member name="T:FlaUI.Core.WindowsAPI.Win32Fallback">
            <summary>
            This class wraps the various win32 fallback methods which are used in case UIA fails.
            </summary>
        </member>
        <member name="M:FlaUI.Core.WindowsAPI.WindowsApiTools.GetMainModuleFilepath(System.Diagnostics.Process)">
            <summary>
            Tries to get the executable path for a given process.
            </summary>
        </member>
    </members>
</doc>
