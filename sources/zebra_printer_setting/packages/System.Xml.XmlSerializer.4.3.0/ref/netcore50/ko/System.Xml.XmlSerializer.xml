<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.XmlSerializer</name>
  </assembly>
  <members>
    <member name="T:System.Xml.Serialization.XmlAnyAttributeAttribute">
      <summary>멤버(<see cref="T:System.Xml.XmlAttribute" /> 개체의 배열을 반환하는 필드)가 XML 특성을 포함할 수 있도록 지정합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyAttributeAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" /> 클래스의 새 인스턴스를 만듭니다.</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttribute">
      <summary>멤버(<see cref="T:System.Xml.XmlElement" /> 또는 <see cref="T:System.Xml.XmlNode" /> 개체의 배열을 반환하는 필드)가 serialize 또는 deserialize되고 있는 개체에 해당 멤버가 없는 XML 요소를 나타내는 개체를 포함하도록 지정합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 클래스의 새 인스턴스를 초기화하며 XML 문서에 생성된 XML 요소의 이름을 지정합니다.</summary>
      <param name="name">
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 생성하는 XML 요소의 이름입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor(System.String,System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 클래스의 새 인스턴스를 초기화하며 XML 문서와 이 문서의 XML 네임스페이스에 생성된 XML 요소의 이름을 지정합니다.</summary>
      <param name="name">
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 생성하는 XML 요소의 이름입니다. </param>
      <param name="ns">XML 요소의 XML 네임스페이스입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Name">
      <summary>XML 요소 이름을 가져오거나 설정합니다.</summary>
      <returns>XML 요소의 이름입니다.</returns>
      <exception cref="T:System.InvalidOperationException">배열 멤버의 요소 이름이 <see cref="P:System.Xml.Serialization.XmlAnyElementAttribute.Name" /> 속성으로 지정한 요소 이름과 일치하지 않는 경우 </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Namespace">
      <summary>XML 문서에 생성된 XML 네임스페이스를 가져오거나 설정합니다.</summary>
      <returns>XML 네임스페이스입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Order">
      <summary>요소가 serialize 또는 deserialize되는 명시적 순서를 가져오거나 설정합니다.</summary>
      <returns>코드가 생성되는 순서입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttributes">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 개체의 컬렉션을 나타냅니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Add(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />를 컬렉션에 추가합니다.</summary>
      <returns>새로 추가한 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />의 인덱스입니다.</returns>
      <param name="attribute">추가할 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Clear">
      <summary>
        <see cref="T:System.Collections.CollectionBaseinstance" />에서 개체를 모두 제거합니다.이 메서드는 재정의할 수 없습니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Contains(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>지정된 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />가 컬렉션에 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />가 컬렉션에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="attribute">원하는 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.CopyTo(System.Xml.Serialization.XmlAnyElementAttribute[],System.Int32)">
      <summary>대상 배열의 지정된 인덱스에서 시작하여 전체 컬렉션을 호환 가능한 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 개체의 1차원 배열에 복사합니다. </summary>
      <param name="array">컬렉션에서 복사된 요소의 대상인 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 개체의 일차원 배열입니다.배열에서 0부터 시작하는 인덱스를 사용해야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Count">
      <summary>
        <see cref="T:System.Collections.CollectionBase" /> 인스턴스에 포함된 요소의 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBase" /> 인스턴스에 포함된 요소의 수입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.CollectionBaseinstance" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBaseinstance" />를 반복하는 열거자입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.IndexOf(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>지정된 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />의 인덱스를 가져옵니다.</summary>
      <returns>지정된 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />의 인덱스입니다.</returns>
      <param name="attribute">원하는 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />의 인덱스입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>지정된 인덱스의 컬렉션에 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />를 삽입합니다.</summary>
      <param name="index">
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />가 삽입되는 위치의 인덱스입니다. </param>
      <param name="attribute">삽입할 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Item(System.Int32)">
      <summary>지정된 인덱스에 있는 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />를 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스의 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />입니다.</returns>
      <param name="index">
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />의 인덱스입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Remove(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>컬렉션에서 지정된 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />을 제거합니다.</summary>
      <param name="attribute">제거할 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.RemoveAt(System.Int32)">
      <summary>
        <see cref="T:System.Collections.CollectionBaseinstance" />의 지정한 인덱스에서 요소를 제거합니다.이 메서드는 재정의할 수 없습니다.</summary>
      <param name="index">제거할 요소의 인덱스입니다.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>대상 배열의 지정된 인덱스에서 시작하여 전체 컬렉션을 호환 가능한 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 개체의 1차원 배열에 복사합니다.</summary>
      <param name="array">1차원 배열입니다.</param>
      <param name="index">지정한 인덱스입니다.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.CollectionBase" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBase" />에 대한 액세스가 동기화되면 True이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.CollectionBase" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBase" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Add(System.Object)">
      <summary>개체를 <see cref="T:System.Collections.CollectionBase" />의 끝 부분에 추가합니다.</summary>
      <returns>컬렉션에 추가된 개체입니다.</returns>
      <param name="value">컬렉션에 추가할 개체의 값입니다.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>
        <see cref="T:System.Collections.CollectionBase" />에 특정 요소가 들어 있는지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBase" />에 특정 요소가 있으면 True이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">요소의 값입니다.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>지정한 개체를 검색하고, 전체 <see cref="T:System.Collections.CollectionBase" />에서 이 개체가 처음 나타나는 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>개체의 0부터 시작하는 인덱스입니다.</returns>
      <param name="value">개체의 값입니다.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>
        <see cref="T:System.Collections.CollectionBase" />의 지정된 인덱스에 요소를 삽입합니다.</summary>
      <param name="index">요소가 삽입되는 인덱스입니다.</param>
      <param name="value">요소의 값입니다.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.CollectionBasehas" />의 크기가 고정되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBasehas" />가 고정 크기이면 True이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.CollectionBase" />이 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.CollectionBase" />이 읽기 전용이면 True이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>지정된 인덱스에 있는 요소를 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스의 요소입니다.</returns>
      <param name="index">요소의 인덱스입니다.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>
        <see cref="T:System.Collections.CollectionBase" />에서 맨 처음 발견되는 특정 개체를 제거합니다.</summary>
      <param name="value">제거된 개체의 값입니다.</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 특정 클래스 멤버를 XML 요소의 배열로 serialize하도록 지정합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> 클래스의 새 인스턴스를 초기화하며 XML 문서 인스턴스에서 생성된 XML 요소의 이름을 지정합니다.</summary>
      <param name="elementName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 생성하는 XML 요소의 이름입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.ElementName">
      <summary>serialize된 배열에 지정되어 있는 XML 요소의 이름을 가져오거나 설정합니다.</summary>
      <returns>serialize된 배열의 XML 요소 이름으로,기본값은 <see cref="T:System.Xml.Serialization.XmlArrayAttribute" />가 할당된 멤버의 이름입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Form">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />에서 생성한 XML 요소 이름이 정규화되었는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 값 중 하나입니다.기본값은 XmlSchemaForm.None입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.IsNullable">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 멤버를 xsi:nil 특성이 true로 설정된 빈 XML 태그로 serialize해야 하는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 xsi:nil 특성을 생성하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Namespace">
      <summary>XML 요소의 네임스페이스를 가져오거나 설정합니다.</summary>
      <returns>XML 요소의 네임스페이스입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Order">
      <summary>요소가 serialize 또는 deserialize되는 명시적 순서를 가져오거나 설정합니다.</summary>
      <returns>코드가 생성되는 순서입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 serialize된 배열에 배치할 수 있는 파생 형식을 지정하는 특성을 나타냅니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 클래스의 새 인스턴스를 초기화하고 XML 문서에 생성된 XML 요소의 이름을 지정합니다.</summary>
      <param name="elementName">XML 요소의 이름입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String,System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 클래스의 새 인스턴스를 초기화하고 XML 문서에 생성된 XML 요소의 이름 및 생성된 XML 문서에 삽입할 수 있는 <see cref="T:System.Type" />을 지정합니다.</summary>
      <param name="elementName">XML 요소의 이름입니다. </param>
      <param name="type">serialize할 개체의 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 클래스의 새 인스턴스를 초기화하고 serialize된 배열에 삽입할 수 있는 <see cref="T:System.Type" />을 지정합니다.</summary>
      <param name="type">serialize할 개체의 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.DataType">
      <summary>생성된 XML 요소의 XML 데이터 형식을 가져오거나 설정합니다.</summary>
      <returns>World Wide Web 컨소시엄(www.w3.org) 문서의 "XML Schema Part 2: Datatypes"에 정의된 XSD(XML 스키마 정의) 데이터 형식입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.ElementName">
      <summary>생성된 XML 요소의 이름을 가져오거나 설정합니다.</summary>
      <returns>생성된 XML 요소의 이름입니다.기본값은 멤버 식별자입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Form">
      <summary>생성된 XML 요소의 이름이 정규화된 이름인지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 값 중 하나입니다.기본값은 XmlSchemaForm.None입니다.</returns>
      <exception cref="T:System.Exception">
        <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Form" /> 속성이 XmlSchemaForm.Unqualified로 설정되어 있으며 <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace" /> 값이 지정되어 있는 경우 </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.IsNullable">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 멤버를 xsi:nil 특성이 true로 설정된 빈 XML 태그로 serialize해야 하는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 xsi:nil 특성을 생성하면 true이고, 그렇지 않으면 false이고 인스턴스가 생성되지 않습니다.기본값은 true입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace">
      <summary>생성된 XML 요소의 네임스페이스를 가져오거나 설정합니다.</summary>
      <returns>생성된 XML 요소의 네임스페이스입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.NestingLevel">
      <summary>XML 요소 계층 구조에서 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />가 영향을 주는 수준을 가져오거나 설정합니다.</summary>
      <returns>배열의 배열에 있는 인덱스 집합의 인덱스(0부터 시작)입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Type">
      <summary>배열에 사용할 수 있는 형식을 가져오거나 설정합니다.</summary>
      <returns>배열에 사용할 수 있는 <see cref="T:System.Type" />입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttributes">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 개체의 컬렉션을 나타냅니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Add(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />를 컬렉션에 추가합니다.</summary>
      <returns>추가된 항목의 인덱스입니다.</returns>
      <param name="attribute">컬렉션에 추가할 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Clear">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />에서 요소를 모두 제거합니다.</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />가 읽기 전용인 경우또는 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 크기가 고정되어 있는 경우 </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Contains(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>컬렉션에 지정한 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />가 들어 있는지 여부를 확인합니다. </summary>
      <returns>컬렉션에 지정한 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />가 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="attribute">확인할 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />입니다.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.CopyTo(System.Xml.Serialization.XmlArrayItemAttribute[],System.Int32)">
      <summary>지정한 대상 인덱스에서 시작하여 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 배열을 컬렉션에 복사합니다. </summary>
      <param name="array">컬렉션에 복사할 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 개체의 배열입니다.</param>
      <param name="index">복사된 특성이 시작되는 인덱스입니다.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Count">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.GetEnumerator">
      <summary>전체 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />에 대한 열거자를 반환합니다.</summary>
      <returns>전체 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.IndexOf(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>컬렉션에서 지정한 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />가 처음 나타나는 인덱스(0부터 시작)를 반환하고, 컬렉션에 특성이 없으면 -1을 반환합니다. </summary>
      <returns>컬렉션에서 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />의 첫 번째 인덱스이고, 컬렉션에 특성이 없으면 -1입니다.</returns>
      <param name="attribute">컬렉션에서 찾을 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />입니다.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Insert(System.Int32,System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>지정된 인덱스의 컬렉션에 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />를 삽입합니다. </summary>
      <param name="index">특성이 삽입되는 인덱스입니다.</param>
      <param name="attribute">삽입할 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />입니다.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Item(System.Int32)">
      <summary>지정한 인덱스에 있는 항목을 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스에 있는 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />입니다.</returns>
      <param name="index">가져오거나 설정할 컬렉션 멤버의 인덱스(0부터 시작)입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Remove(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />가 컬렉션에 있는 경우 컬렉션에서 이를 제거합니다. </summary>
      <param name="attribute">제거할 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />입니다.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.RemoveAt(System.Int32)">
      <summary>지정한 인덱스에서 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 항목을 제거합니다.</summary>
      <param name="index">제거할 항목의 인덱스(0부터 시작)입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />가 읽기 전용인 경우또는 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 크기가 고정되어 있는 경우 </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>대상 배열의 지정된 인덱스에서 시작하여 전체 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />을 호환되는 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Add(System.Object)">
      <summary>개체를 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 끝 부분에 추가합니다.</summary>
      <returns>
        <paramref name="value" />가 추가된 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 인덱스입니다.</returns>
      <param name="value">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 끝에 추가할 <see cref="T:System.Object" />입니다.값은 null이 될 수 있습니다.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />가 읽기 전용인 경우또는 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 크기가 고정되어 있는 경우 </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>컬렉션에 지정한 <see cref="T:System.Object" />이 들어 있는지 여부를 확인합니다. </summary>
      <returns>컬렉션에 지정한 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />가 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>컬렉션에서 지정한 <see cref="T:System.Object" />가 처음 나타나는 인덱스(0부터 시작)를 반환하고, 컬렉션에 특성이 없으면 1을 반환합니다. </summary>
      <returns>컬렉션에서 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />의 첫 번째 인덱스이고, 컬렉션에 특성이 없으면 -1입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 지정된 인덱스에 요소를 삽입합니다.</summary>
      <param name="index">
        <paramref name="value" />를 삽입해야 하는 인덱스(0부터 시작)입니다. </param>
      <param name="value">삽입할 <see cref="T:System.Object" />입니다.값은 null이 될 수 있습니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우또는 <paramref name="index" />가 <see cref="P:System.Xml.Serialization.XmlArrayItemAttributes.Count" />보다 큰 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />가 읽기 전용인 경우또는 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 크기가 고정되어 있는 경우 </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IsFixedSize">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 크기가 고정되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 크기가 고정되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IsReadOnly">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />이 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>지정한 인덱스에 있는 항목을 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스에 있는 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />입니다.</returns>
      <param name="index">가져오거나 설정할 컬렉션 멤버의 인덱스(0부터 시작)입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />에서 맨 처음 발견되는 특정 개체를 제거합니다.</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />가 읽기 전용인 경우또는 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" />의 크기가 고정되어 있는 경우 </exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 해당 클래스 멤버를 XML 특성으로 serialize하도록 지정합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 클래스의 새 인스턴스를 초기화하고 생성된 XML 특성의 이름을 지정합니다.</summary>
      <param name="attributeName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 생성하는 XML 특성의 이름입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.String,System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="attributeName">생성되는 XML 특성의 이름입니다. </param>
      <param name="type">특성을 저장하는 데 사용되는 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="type">특성을 저장하는 데 사용되는 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.AttributeName">
      <summary>XML 특성의 이름을 가져오거나 설정합니다.</summary>
      <returns>XML 특성의 이름입니다.기본값은 멤버 이름입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.DataType">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />에 의해 생성된 XML 특성의 XSD 데이터 형식을 가져오거나 설정합니다.</summary>
      <returns>World Wide Web 컨소시엄(www.w3.org) 문서의 "XML Schema Part 2: Datatypes"에 정의된 XSD(XML 스키마 문서) 데이터 형식입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Form">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />를 통해 생성된 XML 특성의 이름이 정규화된 이름인지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 값 중 하나입니다.기본값은 XmlForm.None입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Namespace">
      <summary>XML 특성의 XML 네임스페이스를 가져오거나 설정합니다.</summary>
      <returns>XML 특성의 XML 네임스페이스입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Type">
      <summary>XML 특성의 복합 형식을 가져오거나 설정합니다.</summary>
      <returns>XML 특성의 형식입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeOverrides">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />를 사용하여 개체를 serialize하거나 deserialize하면 속성, 필드 및 클래스 특성을 재정의할 수 있습니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.String,System.Xml.Serialization.XmlAttributes)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributes" /> 개체를 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 개체 컬렉션에 추가합니다.<paramref name="type" /> 매개 변수는 재정의할 개체를 지정합니다.<paramref name="member" /> 매개 변수는 재정의되는 멤버의 이름을 지정합니다.</summary>
      <param name="type">재정의할 개체의 <see cref="T:System.Type" />입니다. </param>
      <param name="member">재정의할 멤버의 이름입니다. </param>
      <param name="attributes">재정의 특성을 나타내는 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 개체입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.Xml.Serialization.XmlAttributes)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributes" /> 개체를 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 개체 컬렉션에 추가합니다.<paramref name="type" /> 매개 변수는 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 개체로 재정의할 개체를 지정합니다.</summary>
      <param name="type">재정의되는 개체의 <see cref="T:System.Type" />입니다. </param>
      <param name="attributes">재정의 특성을 나타내는 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 개체입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type)">
      <summary>지정한 기본 클래스 형식과 관련된 개체를 가져옵니다.</summary>
      <returns>재정의 특성의 컬렉션을 나타내는 <see cref="T:System.Xml.Serialization.XmlAttributes" />입니다.</returns>
      <param name="type">검색할 특성의 컬렉션과 관련된 기본 클래스 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type,System.String)">
      <summary>지정한 (기본 클래스) 형식과 관련된 개체를 가져옵니다.해당 멤버 매개 변수는 재정의되는 기본 클래스 멤버를 지정합니다.</summary>
      <returns>재정의 특성의 컬렉션을 나타내는 <see cref="T:System.Xml.Serialization.XmlAttributes" />입니다.</returns>
      <param name="type">원하는 특성의 컬렉션과 관련된 기본 클래스 <see cref="T:System.Type" />입니다. </param>
      <param name="member">반환할 <see cref="T:System.Xml.Serialization.XmlAttributes" />를 지정하는 재정의된 멤버의 이름입니다. </param>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributes">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 개체를 serialize 및 deserialize하는 방식을 제어하는 특성 개체의 컬렉션을 나타냅니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributes.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlAttributes" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyAttribute">
      <summary>재정의할 <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" />를 가져오거나 설정합니다.</summary>
      <returns>재정의할 <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" />입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyElements">
      <summary>재정의할 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 개체의 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 개체의 컬렉션을 나타내는 <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArray">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 배열을 반환하는 공용 필드 또는 읽기/쓰기 속성을 serialize하는 방식을 지정하는 개체를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 배열을 반환하는 공용 필드 또는 읽기/쓰기 속성을 serialize하는 방식을 지정하는 <see cref="T:System.Xml.Serialization.XmlArrayAttribute" />입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArrayItems">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 공용 필드 또는 읽기/쓰기 속성에 의해 반환된 배열 내에 삽입된 항목을 serialize하는 방식을 지정하는 개체 컬렉션을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 개체의 컬렉션을 포함하는 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 공용 필드 또는 공용 읽기/쓰기 속성을 XML 특성으로 serialize하는 방식을 지정하는 개체를 가져오거나 설정합니다.</summary>
      <returns>공용 필드 또는 읽기/쓰기 속성을 XML 특성으로 serialize하는 것을 제어하는 <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" />입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlChoiceIdentifier">
      <summary>일련의 선택을 명확하게 구별하는 개체를 가져오거나 설정합니다.</summary>
      <returns>xsi:choice 요소로 serialize되는 클래스 멤버에 적용할 수 있는 <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" />입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlDefaultValue">
      <summary>XML 요소 또는 특성의 기본값을 가져오거나 설정합니다.</summary>
      <returns>XML 요소 또는 특성의 기본값을 나타내는 <see cref="T:System.Object" />입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlElements">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 공용 필드 또는 읽기/쓰기 속성을 XML 요소로 serialize하는 방식을 지정하는 개체의 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 개체의 컬렉션을 포함하는 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlEnum">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 열거형 멤버를 serialize하는 방식을 지정하는 개체를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 열거형 멤버를 serialize하는 방식을 지정하는 <see cref="T:System.Xml.Serialization.XmlEnumAttribute" />입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlIgnore">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 공용 필드 또는 읽기/쓰기 속성을 serialize하는지 여부를 지정하는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 필드 또는 속성을 serialize하지 않으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.Xmlns">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 개체를 반환하는 멤버가 들어 있는 개체가 재정의될 때 모든 네임스페이스 선언을 유지할지 여부를 지정하는 값을 가져오거나 설정합니다.</summary>
      <returns>네임스페이스 선언을 유지해야 한다면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlRoot">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 클래스를 XML 루트 요소로 serialize하는 방식을 지정하는 개체를 가져오거나 설정합니다.</summary>
      <returns>XML 루트 요소로 지정된 클래스를 재정의하는 <see cref="T:System.Xml.Serialization.XmlRootAttribute" />입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlText">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 공용 필드 또는 공용 읽기/쓰기 속성을 XML 텍스트로 serialize하도록 하는 개체를 가져오거나 설정합니다.</summary>
      <returns>공용 속성 또는 필드의 기본 serialization을 재정의하는 <see cref="T:System.Xml.Serialization.XmlTextAttribute" />입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlType">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlTypeAttribute" />가 적용된 클래스를 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 serialize하는 방식을 지정하는 개체를 가져오거나 설정합니다.</summary>
      <returns>클래스 선언에 적용된 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" />를 재정의하는 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" />입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute">
      <summary>열거형을 사용하여 멤버를 추가로 검색할 수 있음을 지정합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">선택을 검색하는 데 사용하는 열거형을 반환하는 멤버 이름입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlChoiceIdentifierAttribute.MemberName">
      <summary>형식을 검색하는 경우 사용할 열거형을 반환하는 필드의 이름을 가져오거나 설정합니다.</summary>
      <returns>열거형을 반환하는 필드의 이름입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttribute">
      <summary>공용 필드 또는 속성을 포함하는 개체를 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 serialize하거나 deserialize할 때 해당 필드나 속성이 XML 요소를 나타냄을 의미합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 클래스의 새 인스턴스를 초기화하고 XML 요소의 이름을 지정합니다.</summary>
      <param name="elementName">serialize된 멤버의 XML 요소 이름입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.String,System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" />의 새 인스턴스를 초기화하고 XML 요소의 이름을 지정하며 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />가 적용되는 멤버의 파생 형식도 지정합니다.이 멤버 형식은 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 이를 포함하는 개체를 serialize할 때 사용됩니다.</summary>
      <param name="elementName">serialize된 멤버의 XML 요소 이름입니다. </param>
      <param name="type">멤버의 형식에서 파생된 개체의 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 클래스의 새 인스턴스를 초기화하고 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />가 적용되는 멤버에 대한 형식을 지정합니다.이 형식은 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 이를 포함하는 개체를 serialize하거나 deserialize할 때 사용됩니다.</summary>
      <param name="type">멤버의 형식에서 파생된 개체의 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.DataType">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />에 의해 생성된 XML 요소의 XSD(XML 스키마 정의) 데이터 형식을 가져오거나 설정합니다.</summary>
      <returns>XML 스키마 데이터 형식에 대한 자세한 내용은 World Wide Web 컨소시엄(www.w3.org) 문서 "XML Schema Part 2: Datatypes"를 참조하십시오.</returns>
      <exception cref="T:System.Exception">지정한 XML 스키마 데이터 형식을 .NET 데이터 형식에 매핑할 수 없는 경우 </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.ElementName">
      <summary>생성된 XML 요소의 이름을 가져오거나 설정합니다.</summary>
      <returns>생성된 XML 요소의 이름입니다.기본값은 멤버 식별자입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Form">
      <summary>요소가 한정되었는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 값 중 하나입니다.기본값은 <see cref="F:System.Xml.Schema.XmlSchemaForm.None" />입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.IsNullable">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 null로 설정된 멤버를 xsi:nil 특성이 true로 설정된 빈 태그로 serialize해야 하는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 xsi:nil 특성을 생성하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Namespace">
      <summary>클래스가 serialize될 때 결과로 만들어지는 XML 요소에 할당된 네임스페이스를 가져오거나 설정합니다.</summary>
      <returns>XML 요소의 네임스페이스입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Order">
      <summary>요소가 serialize 또는 deserialize되는 명시적 순서를 가져오거나 설정합니다.</summary>
      <returns>코드가 생성되는 순서입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Type">
      <summary>XML 요소를 나타내는 데 사용되는 개체 형식을 가져오거나 설정합니다.</summary>
      <returns>멤버의 <see cref="T:System.Type" />입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttributes">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 클래스를 serialize하는 기본 방식을 재정의하는 데 사용하는 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 개체의 컬렉션을 나타냅니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Add(System.Xml.Serialization.XmlElementAttribute)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" />를 컬렉션에 추가합니다.</summary>
      <returns>새로 추가한 항목의 인덱스(0부터 시작)입니다.</returns>
      <param name="attribute">추가할 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Clear">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에서 요소를 모두 제거합니다.</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />가 읽기 전용인 경우또는 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 크기가 고정되어 있는 경우 </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Contains(System.Xml.Serialization.XmlElementAttribute)">
      <summary>컬렉션에 지정된 개체가 들어 있는지 여부를 확인합니다.</summary>
      <returns>개체가 컬렉션에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="attribute">찾아볼 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.CopyTo(System.Xml.Serialization.XmlElementAttribute[],System.Int32)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 또는 그 일부를 1차원 배열에 복사합니다.</summary>
      <param name="array">복사된 요소를 보유하는 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 배열입니다. </param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Count">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.GetEnumerator">
      <summary>전체 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에 대한 열거자를 반환합니다.</summary>
      <returns>전체 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.IndexOf(System.Xml.Serialization.XmlElementAttribute)">
      <summary>지정된 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />의 인덱스를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" />의 인덱스이며 0에서 시작합니다.</returns>
      <param name="attribute">인덱스가 검색되는 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />입니다.</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlElementAttribute)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" />를 컬렉션에 삽입합니다.</summary>
      <param name="index">멤버가 삽입된 0부터 시작하는 인덱스입니다. </param>
      <param name="attribute">삽입할 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Item(System.Int32)">
      <summary>지정된 인덱스에 있는 요소를 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스의 요소입니다.</returns>
      <param name="index">가져오거나 설정할 요소의 인덱스(0부터 시작)입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.NotSupportedException">속성이 설정되어 있으며 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />가 읽기 전용인 경우 </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Remove(System.Xml.Serialization.XmlElementAttribute)">
      <summary>컬렉션에서 지정된 개체를 제거합니다.</summary>
      <param name="attribute">컬렉션에서 제거할 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.RemoveAt(System.Int32)">
      <summary>지정한 인덱스에서 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 항목을 제거합니다.</summary>
      <param name="index">제거할 항목의 인덱스(0부터 시작)입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />가 읽기 전용인 경우또는 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 크기가 고정되어 있는 경우 </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>대상 배열의 지정된 인덱스에서 시작하여 전체 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />을 호환되는 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Add(System.Object)">
      <summary>개체를 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 끝 부분에 추가합니다.</summary>
      <returns>
        <paramref name="value" />가 추가된 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 인덱스입니다.</returns>
      <param name="value">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 끝에 추가할 <see cref="T:System.Object" />입니다.값은 null이 될 수 있습니다.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />가 읽기 전용인 경우또는 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 크기가 고정되어 있는 경우 </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에 특정 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Object" />가 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에서 찾을 개체입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에서 특정 항목의 인덱스를 확인합니다.</summary>
      <returns>목록에 있으면 <paramref name="value" />의 인덱스이고, 그렇지 않으면 -1입니다.</returns>
      <param name="value">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에서 찾을 개체입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 지정된 인덱스에 요소를 삽입합니다.</summary>
      <param name="index">
        <paramref name="value" />를 삽입해야 하는 인덱스(0부터 시작)입니다. </param>
      <param name="value">삽입할 <see cref="T:System.Object" />입니다.값은 null이 될 수 있습니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우또는 <paramref name="index" />가 <see cref="P:System.Xml.Serialization.XmlElementAttributes.Count" />보다 큰 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />가 읽기 전용인 경우또는 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 크기가 고정되어 있는 경우 </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IsFixedSize">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 크기가 고정되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 크기가 고정되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IsReadOnly">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />이 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>지정된 인덱스에 있는 요소를 가져오거나 설정합니다.</summary>
      <returns>지정된 인덱스의 요소입니다.</returns>
      <param name="index">가져오거나 설정할 요소의 인덱스(0부터 시작)입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.NotSupportedException">속성이 설정되어 있으며 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />가 읽기 전용인 경우 </exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />에서 맨 처음 발견되는 특정 개체를 제거합니다.</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" />가 읽기 전용인 경우또는 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />의 크기가 고정되어 있는 경우 </exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlEnumAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 열거형 멤버를 serialize하는 방식을 제어합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlEnumAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlEnumAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlEnumAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlEnumAttribute" /> 클래스의 새 인스턴스를 초기화하고 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 열거형을 serialize하거나 deserialize할 때 생성하거나 인식하는 XML 값을 지정합니다.</summary>
      <param name="name">열거형 멤버의 재정의 이름입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlEnumAttribute.Name">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 열거형을 serialize할 때 XML 문서 인스턴스에서 생성된 값 또는 열거형 멤버를 deserialize할 때 인식된 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 열거형을 serialize할 때 XML 문서 인스턴스에서 생성된 값, 또는 열거형 멤버를 deserialize할 때 인식된 값입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlIgnoreAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />의 <see cref="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)" /> 메서드를 호출하여 공용 필드 또는 공용 읽기/쓰기 속성 값을 serialize하지 않도록 합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIgnoreAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlIgnoreAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlIncludeAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 개체를 serialize하거나 deserialize할 때 형식을 인식할 수 있게 합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIncludeAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlIncludeAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="type">포함할 개체의 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlIncludeAttribute.Type">
      <summary>포함할 개체의 형식을 가져오거나 설정합니다.</summary>
      <returns>포함할 개체의 <see cref="T:System.Type" />입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute">
      <summary>대상 속성, 매개 변수, 반환 값 또는 클래스 멤버가 XML 문서 내에서 사용되는 네임스페이스와 연관된 접두사를 포함하도록 지정합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlRootAttribute">
      <summary>특성 대상의 XML serialization을 XML 루트 요소로 제어합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlRootAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlRootAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> 클래스의 새 인스턴스를 초기화하고 XML 루트 요소의 이름을 지정합니다.</summary>
      <param name="elementName">XML 루트 요소의 이름입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.DataType">
      <summary>XML 루트 요소의 XSD 데이터 형식을 가져오거나 설정합니다.</summary>
      <returns>World Wide Web 컨소시엄(www.w3.org) 문서의 "XML Schema Part 2: Datatypes"에 정의된 XSD(XML 스키마 문서) 데이터 형식입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.ElementName">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 클래스의 <see cref="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)" /> 및 <see cref="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)" /> 메서드에 의해 각각 생성되고 인식되는 XML 요소의 이름을 가져오거나 설정합니다.</summary>
      <returns>XML 문서 인스턴스에서 생성되고 인식되는 XML 루트 요소의 이름입니다.기본값은 serialize된 클래스의 이름입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.IsNullable">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 null로 설정된 멤버를 true로 설정된 xsi:nil 특성으로 serialize해야 하는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 xsi:nil 특성을 생성하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.Namespace">
      <summary>XML 루트 요소의 네임스페이스를 가져오거나 설정합니다.</summary>
      <returns>XML 요소의 네임스페이스입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializer">
      <summary>XML 문서로 개체를 serialize하고 XML 문서에서 개체를 deserialize합니다.<see cref="T:System.Xml.Serialization.XmlSerializer" />를 사용하면 개체가 XML로 인코딩되는 방식을 제어할 수 있습니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type)">
      <summary>지정된 형식의 개체를 XML 문서로 serialize하고 XML 문서를 지정된 형식의 개체로 deserialize할 수 있는 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="type">이 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 serialize할 수 있는 개체의 형식입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.String)">
      <summary>지정된 형식의 개체를 XML 문서로 serialize하고 XML 문서를 지정된 형식의 개체로 deserialize할 수 있는 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 클래스의 새 인스턴스를 초기화합니다.모든 XML 요소의 기본 네임스페이스를 지정합니다.</summary>
      <param name="type">이 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 serialize할 수 있는 개체의 형식입니다. </param>
      <param name="defaultNamespace">모든 XML 요소에 사용할 기본 네임스페이스입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Type[])">
      <summary>지정된 형식의 개체를 XML 문서로 serialize하고 XML 문서를 지정된 형식의 개체로 deserialize할 수 있는 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 클래스의 새 인스턴스를 초기화합니다.필드 또는 속성이 배열을 반환하는 경우 <paramref name="extraTypes" /> 매개 변수는 배열에 삽입될 수 있는 개체를 지정합니다.</summary>
      <param name="type">이 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 serialize할 수 있는 개체의 형식입니다. </param>
      <param name="extraTypes">serialize할 추가 개체 형식으로 이루어진 <see cref="T:System.Type" /> 배열입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides)">
      <summary>지정된 형식의 개체를 XML 문서로 serialize하고 XML 문서를 지정된 형식의 개체로 deserialize할 수 있는 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 클래스의 새 인스턴스를 초기화합니다.serialize되는 각 개체는 클래스의 인스턴스를 포함할 수 있으며, 이 오버로드는 다른 클래스로 재정의할 수 있습니다.</summary>
      <param name="type">serialize할 개체의 형식입니다. </param>
      <param name="overrides">
        <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides,System.Type[],System.Xml.Serialization.XmlRootAttribute,System.String)">
      <summary>
        <see cref="T:System.Object" /> 형식의 개체를 XML 문서 인스턴스로 serialize하고 XML 문서 인스턴스를 <see cref="T:System.Object" /> 형식의 개체로 deserialize할 수 있는 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 클래스의 새 인스턴스를 초기화합니다.serialize되는 각 개체는 클래스의 인스턴스를 포함할 수 있으며, 이 오버로드에서 그 클래스를 다른 클래스로 재정의합니다.또한 이 오버로드는 모든 XML 요소의 기본 네임스페이스 및 XML 루트 요소로 사용할 클래스를 지정합니다.</summary>
      <param name="type">이 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 serialize할 수 있는 개체의 형식입니다. </param>
      <param name="overrides">
        <paramref name="type" /> 매개 변수에 지정된 클래스의 동작을 확장하거나 재정의하는 <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />입니다. </param>
      <param name="extraTypes">serialize할 추가 개체 형식으로 이루어진 <see cref="T:System.Type" /> 배열입니다. </param>
      <param name="root">XML 요소 속성을 정의하는 <see cref="T:System.Xml.Serialization.XmlRootAttribute" />입니다. </param>
      <param name="defaultNamespace">XML 문서에 있는 모든 XML 요소의 기본 네임스페이스입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlRootAttribute)">
      <summary>지정된 형식의 개체를 XML 문서로 serialize하고 XML 문서를 지정된 형식의 개체로 deserialize할 수 있는 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 클래스의 새 인스턴스를 초기화합니다.또한 XML 루트 요소로 사용할 클래스를 지정합니다.</summary>
      <param name="type">이 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 serialize할 수 있는 개체의 형식입니다. </param>
      <param name="root">XML 루트 요소를 나타내는 <see cref="T:System.Xml.Serialization.XmlRootAttribute" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.CanDeserialize(System.Xml.XmlReader)">
      <summary>이 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 지정된 XML 문서를 deserialize할 수 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.XmlReader" />가 가리키는 개체를 이 <see cref="T:System.Xml.Serialization.XmlSerializer" />가 deserialize할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="xmlReader">deserialize할 문서를 가리키는 <see cref="T:System.Xml.XmlReader" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)">
      <summary>지정된 <see cref="T:System.IO.Stream" />에 포함된 XML 문서를 deserialize합니다.</summary>
      <returns>deserialize되는 <see cref="T:System.Object" />입니다.</returns>
      <param name="stream">deserialize할 XML 문서를 포함하는 <see cref="T:System.IO.Stream" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.TextReader)">
      <summary>지정된 <see cref="T:System.IO.TextReader" />에 포함된 XML 문서를 deserialize합니다.</summary>
      <returns>deserialize되는 <see cref="T:System.Object" />입니다.</returns>
      <param name="textReader">deserialize할 XML 문서를 포함하는 <see cref="T:System.IO.TextReader" />입니다. </param>
      <exception cref="T:System.InvalidOperationException">deserialization 중 오류가 발생했습니다.<see cref="P:System.Exception.InnerException" /> 속성을 사용하여 원래 예외를 사용할 수 있습니다.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.Xml.XmlReader)">
      <summary>지정된 <see cref="T:System.xml.XmlReader" />에 포함된 XML 문서를 deserialize합니다.</summary>
      <returns>deserialize되는 <see cref="T:System.Object" />입니다.</returns>
      <param name="xmlReader">deserialize할 XML 문서를 포함하는 <see cref="T:System.xml.XmlReader" />입니다. </param>
      <exception cref="T:System.InvalidOperationException">deserialization 중 오류가 발생했습니다.<see cref="P:System.Exception.InnerException" /> 속성을 사용하여 원래 예외를 사용할 수 있습니다.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.FromTypes(System.Type[])">
      <summary>형식 배열에서 만든 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 개체의 배열을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 개체의 배열입니다.</returns>
      <param name="types">
        <see cref="T:System.Type" /> 개체로 이루어진 배열입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object)">
      <summary>지정된 <see cref="T:System.Object" />를 serialize하고 지정된 <see cref="T:System.IO.Stream" />을 사용하여 XML 문서를 파일에 씁니다.</summary>
      <param name="stream">XML 문서를 쓰는 데 사용되는 <see cref="T:System.IO.Stream" />입니다. </param>
      <param name="o">serialize할 <see cref="T:System.Object" />입니다. </param>
      <exception cref="T:System.InvalidOperationException">serialization 중 오류가 발생했습니다.<see cref="P:System.Exception.InnerException" /> 속성을 사용하여 원래 예외를 사용할 수 있습니다.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>지정된 <see cref="T:System.Object" />를 serialize하고 지정된 네임스페이스를 참조하는 지정된 <see cref="T:System.IO.Stream" />을 사용하여 XML 문서를 파일에 씁니다.</summary>
      <param name="stream">XML 문서를 쓰는 데 사용되는 <see cref="T:System.IO.Stream" />입니다. </param>
      <param name="o">serialize할 <see cref="T:System.Object" />입니다. </param>
      <param name="namespaces">개체에서 참조하는 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />입니다. </param>
      <exception cref="T:System.InvalidOperationException">serialization 중 오류가 발생했습니다.<see cref="P:System.Exception.InnerException" /> 속성을 사용하여 원래 예외를 사용할 수 있습니다.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)">
      <summary>지정된 <see cref="T:System.Object" />를 serialize하고 지정된 <see cref="T:System.IO.TextWriter" />를 사용하여 XML 문서를 파일에 씁니다.</summary>
      <param name="textWriter">XML 문서를 쓰는 데 사용되는 <see cref="T:System.IO.TextWriter" />입니다. </param>
      <param name="o">serialize할 <see cref="T:System.Object" />입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>지정된 <see cref="T:System.Object" />를 serialize하고 지정된 <see cref="T:System.IO.TextWriter" />를 사용하여 XML 문서를 파일에 쓰며 지정된 네임스페이스를 참조합니다.</summary>
      <param name="textWriter">XML 문서를 쓰는 데 사용되는 <see cref="T:System.IO.TextWriter" />입니다. </param>
      <param name="o">serialize할 <see cref="T:System.Object" />입니다. </param>
      <param name="namespaces">생성된 XML 문서의 네임스페이스를 포함하는 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />입니다. </param>
      <exception cref="T:System.InvalidOperationException">serialization 중 오류가 발생했습니다.<see cref="P:System.Exception.InnerException" /> 속성을 사용하여 원래 예외를 사용할 수 있습니다.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object)">
      <summary>지정된 <see cref="T:System.Object" />를 serialize하고 지정된 <see cref="T:System.Xml.XmlWriter" />를 사용하여 XML 문서를 파일에 씁니다.</summary>
      <param name="xmlWriter">XML 문서를 쓰는 데 사용되는 <see cref="T:System.xml.XmlWriter" />입니다. </param>
      <param name="o">serialize할 <see cref="T:System.Object" />입니다. </param>
      <exception cref="T:System.InvalidOperationException">serialization 중 오류가 발생했습니다.<see cref="P:System.Exception.InnerException" /> 속성을 사용하여 원래 예외를 사용할 수 있습니다.</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>지정된 <see cref="T:System.Object" />를 serialize하고 지정된 <see cref="T:System.Xml.XmlWriter" />를 사용하여 XML 문서를 파일에 쓰며 지정된 네임스페이스를 참조합니다.</summary>
      <param name="xmlWriter">XML 문서를 쓰는 데 사용되는 <see cref="T:System.xml.XmlWriter" />입니다. </param>
      <param name="o">serialize할 <see cref="T:System.Object" />입니다. </param>
      <param name="namespaces">개체에서 참조하는 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />입니다. </param>
      <exception cref="T:System.InvalidOperationException">serialization 중 오류가 발생했습니다.<see cref="P:System.Exception.InnerException" /> 속성을 사용하여 원래 예외를 사용할 수 있습니다.</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializerNamespaces">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 XML 문서 인스턴스에서 정규화된 이름을 생성하는 데 사용하는 XML 네임스페이스 및 접두사를 포함합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor(System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>접두사와 네임스페이스 쌍의 컬렉션을 포함하는 XmlSerializerNamespaces의 지정된 인스턴스를 사용하여 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="namespaces">네임스페이스와 접두사 쌍을 포함하는 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />의 인스턴스입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor(System.Xml.XmlQualifiedName[])">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="namespaces">
        <see cref="T:System.Xml.XmlQualifiedName" /> 개체로 이루어진 배열입니다. </param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.Add(System.String,System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 개체에 접두사와 네임스페이스 쌍을 추가합니다.</summary>
      <param name="prefix">XML 네임스페이스와 관련된 접두사입니다. </param>
      <param name="ns">XML 네임스페이스입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerNamespaces.Count">
      <summary>컬렉션에 있는 접두사와 네임스페이스 쌍의 개수를 가져옵니다.</summary>
      <returns>컬렉션에 있는 접두사와 네임스페이스 쌍의 개수입니다.</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.ToArray">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 개체에 있는 접두사와 네임스페이스 쌍으로 이루어진 배열을 가져옵니다.</summary>
      <returns>XML 문서에서 정규화된 이름으로 사용되는 <see cref="T:System.Xml.XmlQualifiedName" /> 개체로 이루어진 배열입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTextAttribute">
      <summary>멤버가 포함된 클래스가 serialize되거나 deserialize될 때 멤버를 XML 텍스트로 처리하도록 <see cref="T:System.Xml.Serialization.XmlSerializer" />에 지정합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTextAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlTextAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTextAttribute.#ctor(System.Type)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlTextAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="type">serialize할 개체의 <see cref="T:System.Type" />입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTextAttribute.DataType">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />에 의해 생성된 텍스트의 XSD(XML 스키마 정의) 데이터 형식을 가져오거나 설정합니다.</summary>
      <returns>World Wide Web 컨소시엄(www.w3.org) 문서의 "XML Schema Part 2: Datatypes"에 정의된 XSD(XML 스키마 정의) 데이터 형식입니다.</returns>
      <exception cref="T:System.Exception">지정한 XML 스키마 데이터 형식을 .NET 데이터 형식에 매핑할 수 없는 경우 </exception>
      <exception cref="T:System.InvalidOperationException">지정한 XML 스키마 데이터 형식은 속성에 맞지 않으므로 멤버 형식으로 변환할 수 없는 경우 </exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlTextAttribute.Type">
      <summary>멤버의 형식을 가져오거나 설정합니다.</summary>
      <returns>멤버의 <see cref="T:System.Type" />입니다.</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTypeAttribute">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 특성 대상을 serialize할 때 생성되는 XML 스키마를 제어합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor(System.String)">
      <summary>
        <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> 클래스의 새 인스턴스를 초기화하고 XML 형식의 이름을 지정합니다.</summary>
      <param name="typeName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" />가 클래스 인스턴스를 serialize할 때 생성하고 클래스 인스턴스를 deserialize할 때 인식하는 XML 형식의 이름입니다. </param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.AnonymousType">
      <summary>결과 스키마 형식이 XSD 익명 형식인지 여부를 결정하는 값을 가져오거나 설정합니다.</summary>
      <returns>결과 스키마 형식이 XSD 익명 형식이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.IncludeInSchema">
      <summary>XML 스키마 문서에 형식을 포함할지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>XML 스키마 문서에 형식을 포함하려면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.Namespace">
      <summary>XML 형식의 네임스페이스를 가져오거나 설정합니다.</summary>
      <returns>XML 형식의 네임스페이스입니다.</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.TypeName">
      <summary>XML 형식의 이름을 가져오거나 설정합니다.</summary>
      <returns>XML 형식의 이름입니다.</returns>
    </member>
  </members>
</doc>