<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.XmlSerializer</name>
  </assembly>
  <members>
    <member name="T:System.Xml.Serialization.XmlAnyAttributeAttribute">
      <summary>指定成員 (傳回 <see cref="T:System.Xml.XmlAttribute" /> 物件陣列的欄位) 可以包含任何 XML 屬性。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyAttributeAttribute.#ctor">
      <summary>建構 <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttribute">
      <summary>指定成員 (傳回 <see cref="T:System.Xml.XmlElement" /> 或 <see cref="T:System.Xml.XmlNode" /> 物件陣列的欄位) 包含物件，該物件表示在序列化或還原序列化物件中沒有對應成員的任何 XML 項目。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 類別的新執行個體，指定 XML 文件中產生的 XML 項目名稱。</summary>
      <param name="name">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生的 XML 項目名稱。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttribute.#ctor(System.String,System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 類別的新執行個體，指定 XML 文件中產生的 XML 項目名稱及其 XML 命名空間。</summary>
      <param name="name">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生的 XML 項目名稱。</param>
      <param name="ns">XML 項目的 XML 命名空間。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Name">
      <summary>取得或設定 XML 項目名稱。</summary>
      <returns>XML 項目的名稱。</returns>
      <exception cref="T:System.InvalidOperationException">陣列成員的項目名稱與 <see cref="P:System.Xml.Serialization.XmlAnyElementAttribute.Name" /> 屬性指定的項目名稱不符。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Namespace">
      <summary>取得或設定在 XML 文件中產生的 XML 命名空間。</summary>
      <returns>XML 命名空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttribute.Order">
      <summary>取得或設定項目序列化或還原序列化的明確順序。</summary>
      <returns>程式碼產生的順序。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAnyElementAttributes">
      <summary>表示 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 物件的集合。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Add(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>將 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 加入集合中。</summary>
      <returns>新加入之 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 的索引。</returns>
      <param name="attribute">要相加的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Clear">
      <summary>從 <see cref="T:System.Collections.CollectionBaseinstance" /> 移除所有物件。無法覆寫這個方法。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Contains(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>取得值，指出指定 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 是否存在於集合中。</summary>
      <returns>如果集合中有 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />，則為 true，否則為 false。</returns>
      <param name="attribute">您所要的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.CopyTo(System.Xml.Serialization.XmlAnyElementAttribute[],System.Int32)">
      <summary>從目標陣列的指定索引開始，複製整個集合至 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 物件的相容一維陣列。</summary>
      <param name="array">
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 物件的一維陣列，從集合複製之項目的目的地。陣列必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Count">
      <summary>取得包含在 <see cref="T:System.Collections.CollectionBase" /> 執行個體中的項目數目。</summary>
      <returns>包含在 <see cref="T:System.Collections.CollectionBase" /> 執行個體中的項目數目。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.GetEnumerator">
      <summary>傳回在 <see cref="T:System.Collections.CollectionBaseinstance" /> 中逐一查看的列舉值。</summary>
      <returns>逐一查看 <see cref="T:System.Collections.CollectionBaseinstance" /> 的列舉程式。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.IndexOf(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>取得指定 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 的索引。</summary>
      <returns>指定之 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 的索引。</returns>
      <param name="attribute">您想要其索引的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>將 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 插入集合中指定之索引處。</summary>
      <param name="index">插入 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 的索引。</param>
      <param name="attribute">要插入的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.Item(System.Int32)">
      <summary>取得或設定在指定索引處的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</summary>
      <returns>在指定索引處的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</returns>
      <param name="index">
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 的索引。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.Remove(System.Xml.Serialization.XmlAnyElementAttribute)">
      <summary>從集合中移除指定的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</summary>
      <param name="attribute">要移除的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.RemoveAt(System.Int32)">
      <summary>移除 <see cref="T:System.Collections.CollectionBaseinstance" /> 中指定之索引處的項目。無法覆寫這個方法。</summary>
      <param name="index">要移除的元素索引。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從目標陣列的指定索引開始，複製整個集合至 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 物件的相容一維陣列。</summary>
      <param name="array">一維陣列。</param>
      <param name="index">指定的索引。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.CollectionBase" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果 <see cref="T:System.Collections.CollectionBase" /> 的存取已同步處理，則為 True，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步存取 <see cref="T:System.Collections.CollectionBase" /> 的物件。</summary>
      <returns>可用來同步存取 <see cref="T:System.Collections.CollectionBase" /> 的物件。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Add(System.Object)">
      <summary>將物件加入至 <see cref="T:System.Collections.CollectionBase" /> 的結尾。</summary>
      <returns>已加入集合中的物件。</returns>
      <param name="value">要加入至集合之物件的值。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>判斷 <see cref="T:System.Collections.CollectionBase" /> 是否含有特定元素。</summary>
      <returns>如果 <see cref="T:System.Collections.CollectionBase" /> 包含特定項目則為 True，否則為 false。</returns>
      <param name="value">項目的值。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>搜尋指定的物件，並傳回整個 <see cref="T:System.Collections.CollectionBase" /> 中第一個相符項目之以零起始的索引。</summary>
      <returns>物件以零起始的索引。</returns>
      <param name="value">物件的值。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>將項目插入 <see cref="T:System.Collections.CollectionBase" /> 中指定的索引處。</summary>
      <param name="index">將項目插入之處的索引。</param>
      <param name="value">項目的值。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IsFixedSize">
      <summary>取得值，指出 <see cref="T:System.Collections.CollectionBasehas" /> 為固定大小。</summary>
      <returns>如果 <see cref="T:System.Collections.CollectionBasehas" /> 有固定大小，則為 True，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Collections.CollectionBase" /> 是否唯讀。</summary>
      <returns>如果 <see cref="T:System.Collections.CollectionBase" /> 是唯讀的則為 True，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>取得或設定指定之索引處的項目。</summary>
      <returns>在指定之索引處的項目。</returns>
      <param name="index">項目的索引。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAnyElementAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>從 <see cref="T:System.Collections.CollectionBase" /> 移除特定物件的第一個相符項目。</summary>
      <param name="value">已移除物件的值。</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayAttribute">
      <summary>指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 必須將特定類別成員序列化為 XML 項目的陣列。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> 類別的新執行個體，指定 XML 文件執行個體中產生的 XML 項目名稱。</summary>
      <param name="elementName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生的 XML 項目名稱。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.ElementName">
      <summary>取得或設定指定給序列化陣列的 XML 項目名稱。</summary>
      <returns>序列化陣列的 XML 項目名稱。預設值為被指派了 <see cref="T:System.Xml.Serialization.XmlArrayAttribute" /> 的成員名稱。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Form">
      <summary>取得或設定值，指出 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生的 XML 項目名稱是限定的還是非限定的。</summary>
      <returns>其中一個 <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 值。預設為 XmlSchemaForm.None。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.IsNullable">
      <summary>取得或設定值，指出 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否必須將成員序列化為 xsi:nil 屬性設為 true 的空 XML 標記。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生 xsi:nil 屬性，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Namespace">
      <summary>取得或設定 XML 項目的命名空間。</summary>
      <returns>XML 項目的命名空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayAttribute.Order">
      <summary>取得或設定項目序列化或還原序列化的明確順序。</summary>
      <returns>程式碼產生的順序。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttribute">
      <summary>表示屬性，這個屬性會指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可置於序列化陣列中的衍生型別。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 類別的新執行個體，指定 XML 文件中產生的 XML 項目名稱。</summary>
      <param name="elementName">XML 項目的名稱。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.String,System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 類別的新執行個體，指定 XML 文件中產生的 XML 項目名稱，以及可插入所產生之 XML 文件的 <see cref="T:System.Type" />。</summary>
      <param name="elementName">XML 項目的名稱。</param>
      <param name="type">要序列化的物件的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 類別的新執行個體，指定可插入序列化陣列的 <see cref="T:System.Type" />。</summary>
      <param name="type">要序列化的物件的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.DataType">
      <summary>取得或設定產生的 XML 項目的 XML 資料型別。</summary>
      <returns>XML 結構描述定義 (XSD) 資料型別，如全球資訊網協會 (www.w3.org ) 文件＜XML Schema Part 2: DataTypes＞中所定義。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.ElementName">
      <summary>取得或設定產生的 XML 項目的名稱。</summary>
      <returns>產生的 XML 項目的名稱。預設值為成員識別項。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Form">
      <summary>取得或設定值，指出產生的 XML 項目名稱是否為限定的。</summary>
      <returns>其中一個 <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 值。預設為 XmlSchemaForm.None。</returns>
      <exception cref="T:System.Exception">
        <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Form" /> 屬性設定為 XmlSchemaForm.Unqualified，並且指定 <see cref="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace" /> 值。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.IsNullable">
      <summary>取得或設定值，指出 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否必須將成員序列化為 xsi:nil 屬性設為 true 的空 XML 標記。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生 xsi:nil 屬性，則為 true，否則為 false，而且不會產生執行個體。預設為 true。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Namespace">
      <summary>取得或設定產生的 XML 項目之的命名空間。</summary>
      <returns>產生的 XML 項目的命名空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.NestingLevel">
      <summary>取得或設定 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 影響的 XML 項目的階層架構中的層級。</summary>
      <returns>在陣列組成之陣列的一組索引中，以零起始的索引。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttribute.Type">
      <summary>取得或設定陣列中允許的型別。</summary>
      <returns>陣列中所允許的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlArrayItemAttributes">
      <summary>表示 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 物件的集合。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Add(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>將 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 加入集合中。</summary>
      <returns>加入項目的索引。</returns>
      <param name="attribute">要加入到集合中的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Clear">
      <summary>將所有元素從 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 移除。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 是唯讀的。-或-<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定的大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Contains(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>判斷集合是否包含指定的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</summary>
      <returns>如果集合包含指定的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />，則為 true，否則為 false。</returns>
      <param name="attribute">要檢查的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.CopyTo(System.Xml.Serialization.XmlArrayItemAttribute[],System.Int32)">
      <summary>從指定的目標索引，複製 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 陣列至集合。</summary>
      <param name="array">要複製至集合的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 物件陣列。</param>
      <param name="index">複製屬性開始處的索引。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Count">
      <summary>取得 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 中所包含的元素數。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 中所包含的項目數。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.GetEnumerator">
      <summary>傳回整個 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的列舉程式。</summary>
      <returns>整個 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.IndexOf(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>傳回集合中找到的第一個指定 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 之以零起始的索引，如果在集合中找不到屬性，則為 -1。</summary>
      <returns>集合中 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 的第一個索引，如果在集合中找不到屬性，則為 -1。</returns>
      <param name="attribute">要在集合中尋找的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Insert(System.Int32,System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>將 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 插入集合中指定之索引處。</summary>
      <param name="index">插入屬性的索引。</param>
      <param name="attribute">要插入的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.Item(System.Int32)">
      <summary>取得或設定在指定索引處的項目。</summary>
      <returns>在指定索引處的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</returns>
      <param name="index">要取得或設定以零起始的集合成員索引。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.Remove(System.Xml.Serialization.XmlArrayItemAttribute)">
      <summary>如果存在 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />，則從集合移除它。</summary>
      <param name="attribute">要移除的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.RemoveAt(System.Int32)">
      <summary>移除指定之索引處的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 項目。</summary>
      <param name="index">要移除項目之以零啟始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 中的有效索引。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 是唯讀的。-或-<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定的大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從目標陣列的指定索引開始，複製整個 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 至相容的一維 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#ICollection#SyncRoot"></member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Add(System.Object)">
      <summary>將物件加入至 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 的結尾。</summary>
      <returns>已加入 <paramref name="value" /> 的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 索引。</returns>
      <param name="value">要加入至 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 結尾的 <see cref="T:System.Object" />。此值可以是 null。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 是唯讀的。-或-<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定的大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>判斷集合是否包含指定的 <see cref="T:System.Object" />。</summary>
      <returns>如果集合包含指定的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>傳回集合中找到的第一個指定 <see cref="T:System.Object" /> 之以零起始的索引，如果在集合中找不到屬性，則為 -1。</summary>
      <returns>集合中 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 的第一個索引，如果在集合中找不到屬性，則為 -1。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>將項目插入 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 中指定的索引處。</summary>
      <param name="index">應該插入 <paramref name="value" /> 之以零起始的索引。</param>
      <param name="value">要插入的 <see cref="T:System.Object" />。此值可以是 null。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。-或-<paramref name="index" /> 大於 <see cref="P:System.Xml.Serialization.XmlArrayItemAttributes.Count" />。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 是唯讀的。-或-<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定的大小。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IsFixedSize">
      <summary>取得值，指出 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 是否有固定的大小。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 有固定大小，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 是否唯讀。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 是唯讀的則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>取得或設定在指定索引處的項目。</summary>
      <returns>在指定索引處的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" />。</returns>
      <param name="index">要取得或設定以零起始的集合成員索引。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlArrayItemAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>從 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 移除特定物件的第一個相符項目。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 是唯讀的。-或-<see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 具有固定的大小。</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeAttribute">
      <summary>指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 必須將類別成員序列化為 XML 屬性。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 類別的新執行個體，並指定產生的 XML 屬性的名稱。</summary>
      <param name="attributeName">
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生的 XML 屬性名稱。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.String,System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 類別的新執行個體。</summary>
      <param name="attributeName">產生的 XML 屬性名稱。</param>
      <param name="type">
        <see cref="T:System.Type" />，用於儲存屬性。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" /> 類別的新執行個體。</summary>
      <param name="type">
        <see cref="T:System.Type" />，用於儲存屬性。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.AttributeName">
      <summary>取得或設定 XML 屬性的名稱。</summary>
      <returns>XML 屬性的名稱。預設為成員名稱。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.DataType">
      <summary>取得或設定由 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生之 XML 屬性的 XSD 資料型別。</summary>
      <returns>XSD (XML 結構描述文件) 資料型別，如全球資訊網協會 (www.w3.org ) 文件＜XML Schema: DataTypes＞中所定義。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Form">
      <summary>取得或設定值，指出 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生的 XML 屬性名稱是否為限定的。</summary>
      <returns>其中一個 <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 值。預設為 XmlForm.None。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Namespace">
      <summary>取得或設定 XML 屬性的 XML 命名空間。</summary>
      <returns>XML 屬性的 XML 命名空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeAttribute.Type">
      <summary>取得或設定 XML 屬性的複雜型別。</summary>
      <returns>XML 屬性的型別。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributeOverrides">
      <summary>當使用 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 來序列化或還原序列化物件時，允許您覆寫屬性 (Property)、欄位和類別屬性 (Attribute)。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.String,System.Xml.Serialization.XmlAttributes)">
      <summary>將 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 物件加入 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 物件的集合。<paramref name="type" /> 參數會指定要被覆寫的物件。<paramref name="member" /> 參數指定覆寫的成員名稱。</summary>
      <param name="type">要覆寫之物件的 <see cref="T:System.Type" />。</param>
      <param name="member">要覆寫的成員名稱。</param>
      <param name="attributes">表示覆寫屬性的 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 物件。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributeOverrides.Add(System.Type,System.Xml.Serialization.XmlAttributes)">
      <summary>將 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 物件加入 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 物件的集合。<paramref name="type" /> 參數會指定要由 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 物件覆寫的物件。</summary>
      <param name="type">覆寫之物件的 <see cref="T:System.Type" />。</param>
      <param name="attributes">表示覆寫屬性的 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 物件。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type)">
      <summary>取得與指定的、基底類別、型別相關的物件</summary>
      <returns>表示覆寫屬性集合的 <see cref="T:System.Xml.Serialization.XmlAttributes" />。</returns>
      <param name="type">基底類別 <see cref="T:System.Type" />，與要擷取的屬性集合相關聯。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributeOverrides.Item(System.Type,System.String)">
      <summary>取得與指定的 (基底類別) 型別相關的物件。成員參數會指定已覆寫的基底類別成員。</summary>
      <returns>表示覆寫屬性集合的 <see cref="T:System.Xml.Serialization.XmlAttributes" />。</returns>
      <param name="type">基底類別 <see cref="T:System.Type" />，與您想要的屬性集合相關聯。</param>
      <param name="member">指定傳回 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 的覆寫成員名稱。</param>
    </member>
    <member name="T:System.Xml.Serialization.XmlAttributes">
      <summary>表示用來控制 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化與還原序列化物件方式的屬性 (Attribute) 物件集合。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlAttributes.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlAttributes" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyAttribute">
      <summary>取得或設定要覆寫的 <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" />。</summary>
      <returns>要覆寫的 <see cref="T:System.Xml.Serialization.XmlAnyAttributeAttribute" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAnyElements">
      <summary>取得要覆寫的 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 物件的集合。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlAnyElementAttributes" /> 物件，表示 <see cref="T:System.Xml.Serialization.XmlAnyElementAttribute" /> 物件的集合。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArray">
      <summary>取得或設定物件，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化公用欄位或會傳回陣列的讀取/寫入屬性。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlArrayAttribute" />，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化公用欄位或會傳回陣列的讀取/寫入屬性。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlArrayItems">
      <summary>取得或設定物件集合，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化項目 (用來插入至公用欄位或讀取/寫入屬性所傳回的陣列)。</summary>
      <returns>包含 <see cref="T:System.Xml.Serialization.XmlArrayItemAttribute" /> 物件集合的 <see cref="T:System.Xml.Serialization.XmlArrayItemAttributes" /> 物件。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlAttribute">
      <summary>取得或設定物件，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何將公用欄位或公用讀取/寫入屬性序列化為 XML 屬性。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlAttributeAttribute" />，控制將公用欄位或讀取/寫入屬性 (Property) 序列化為 XML 屬性 (Attribute)。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlChoiceIdentifier">
      <summary>取得或設定物件，讓您在一組選項間進行區別。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" />，可以套用至序列化為 xsi:choice 項目的類別成員。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlDefaultValue">
      <summary>取得或設定 XML 項目或屬性的預設值。</summary>
      <returns>
        <see cref="T:System.Object" />，表示 XML 項目或屬性的預設值。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlElements">
      <summary>取得物件的集合，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何將公用欄位或讀取/寫入屬性序列化為 XML 項目。</summary>
      <returns>包含 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 物件集合的 <see cref="T:System.Xml.Serialization.XmlElementAttributes" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlEnum">
      <summary>取得或設定物件，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化列舉型別 (Enumeration) 成員。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlEnumAttribute" />，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化列舉型別成員。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlIgnore">
      <summary>取得或設定數值，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否要序列化公用欄位或公用讀取/寫入屬性。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 必須不序列化欄位或屬性，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.Xmlns">
      <summary>取得或設定數值，指定當物件包含傳回已覆寫 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 物件的成員時，是否要保留所有的命名空間宣告。</summary>
      <returns>如果應該保留命名空間宣告，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlRoot">
      <summary>取得或設定物件，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何將類別序列化為 XML (Root Element)。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlRootAttribute" />，覆寫類別屬性為 XML 根項目。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlText">
      <summary>取得或設定物件，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 將公用欄位或公用讀取/寫入屬性序列化為 XML 文字。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlTextAttribute" />，覆寫公用屬性或欄位的預設序列化。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlAttributes.XmlType">
      <summary>取得或設定物件，指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 如何序列化套用 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> 的類別。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlTypeAttribute" />，會覆寫套用 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> 至類別宣告 (Class Declaration)。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute">
      <summary>指定可以使用列舉型別進一步偵測成員。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlChoiceIdentifierAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlChoiceIdentifierAttribute" /> 類別的新執行個體。</summary>
      <param name="name">成員名稱，傳回用於偵測選擇的列舉型別。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlChoiceIdentifierAttribute.MemberName">
      <summary>取得或設定欄位的名稱，該欄位傳回偵測型別時使用的列舉型別。</summary>
      <returns>傳回列舉型別之欄位的名稱。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttribute">
      <summary>表示在 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化或還原序列化包含 XML 項目的物件時，公用欄位或屬性表示該項目。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 類別的新執行個體，並且指定 XML 項目的名稱。</summary>
      <param name="elementName">序列成員的 XML 項目名稱。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.String,System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 的新執行個體，並針對套用 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 的成員指定 XML 項目名稱和衍生型別。這個成員型別用於 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化包含它的物件時。</summary>
      <param name="elementName">序列成員的 XML 項目名稱。</param>
      <param name="type">衍生自成員型別的物件 <see cref="T:System.Type" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 類別的新執行個體，並針對套用 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 的成員指定型別。序列化或還原序列化包含這個型別的物件時，<see cref="T:System.Xml.Serialization.XmlSerializer" /> 會使用該型別。</summary>
      <param name="type">衍生自成員型別的物件 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.DataType">
      <summary>取得或設定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 所產生 XML 項目的 XML 結構描述定義 (XSD) 資料型別。</summary>
      <returns>XML 結構描述資料型別，如全球資訊網協會 (www.w3.org ) 文件＜XML Schema Part 2: Datatypes＞所定義。</returns>
      <exception cref="T:System.Exception">您指定的 XML 結構描述資料型別無法對應至 .NET 資料型別。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.ElementName">
      <summary>取得或設定產生的 XML 項目的名稱。</summary>
      <returns>產生的 XML 項目的名稱。預設值為成員識別項。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Form">
      <summary>取得或設定值，指出項目是否為限定的。</summary>
      <returns>其中一個 <see cref="T:System.Xml.Schema.XmlSchemaForm" /> 值。預設為 <see cref="F:System.Xml.Schema.XmlSchemaForm.None" />。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.IsNullable">
      <summary>取得或設定值，指出 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否必須將設為 null 的成員序列化為 xsi:nil 屬性設為 true 的空標記。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生 xsi:nil 屬性，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Namespace">
      <summary>取得或設定指派給類別序列化時所產生之 XML 項目的命名空間。</summary>
      <returns>XML 項目的命名空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Order">
      <summary>取得或設定項目序列化或還原序列化的明確順序。</summary>
      <returns>程式碼產生的順序。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttribute.Type">
      <summary>取得或設定用來表示 XML 項目的物件類型。</summary>
      <returns>成員的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlElementAttributes">
      <summary>代表 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 物件的集合，由 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 用於覆寫其序列化類別的預設方式。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Add(System.Xml.Serialization.XmlElementAttribute)">
      <summary>將 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 加入集合中。</summary>
      <returns>新加入項目之以零起始的索引。</returns>
      <param name="attribute">要相加的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Clear">
      <summary>將所有元素從 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 移除。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是唯讀的。-或-<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定的大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Contains(System.Xml.Serialization.XmlElementAttribute)">
      <summary>判斷集合是否包含指定的物件。</summary>
      <returns>如果集合中有該物件則為true，否則為 false。</returns>
      <param name="attribute">要尋找的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.CopyTo(System.Xml.Serialization.XmlElementAttribute[],System.Int32)">
      <summary>複製 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 或其中一部分至一維陣列。</summary>
      <param name="array">要儲存所複製項目的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 陣列。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Count">
      <summary>取得 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中所包含的元素數。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中所包含的項目數。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.GetEnumerator">
      <summary>傳回整個 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的列舉程式。</summary>
      <returns>整個 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.IndexOf(System.Xml.Serialization.XmlElementAttribute)">
      <summary>取得指定 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 的索引。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 的以零起始的索引。</returns>
      <param name="attribute">正在擷取其索引的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Insert(System.Int32,System.Xml.Serialization.XmlElementAttribute)">
      <summary>將 <see cref="T:System.Xml.Serialization.XmlElementAttribute" /> 插入集合。</summary>
      <param name="index">插入成員所在位置之以零起始的索引。</param>
      <param name="attribute">要插入的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.Item(System.Int32)">
      <summary>取得或設定指定之索引處的項目。</summary>
      <returns>在指定之索引處的項目。</returns>
      <param name="index">要取得或設定之以零起始的項目索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中的有效索引。</exception>
      <exception cref="T:System.NotSupportedException">屬性已設定，而且 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是唯讀的。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.Remove(System.Xml.Serialization.XmlElementAttribute)">
      <summary>從集合中移除指定的物件。</summary>
      <param name="attribute">要從集合中移除的 <see cref="T:System.Xml.Serialization.XmlElementAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.RemoveAt(System.Int32)">
      <summary>移除指定之索引處的 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 項目。</summary>
      <param name="index">要移除項目之以零啟始的索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中的有效索引。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是唯讀的。-或-<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定的大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從目標陣列的指定索引開始，複製整個 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 至相容的一維 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步存取 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的物件。</summary>
      <returns>可用來同步存取 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的物件。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Add(System.Object)">
      <summary>將物件加入至 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 的結尾。</summary>
      <returns>已加入 <paramref name="value" /> 的 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 索引。</returns>
      <param name="value">要加入至 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 結尾的 <see cref="T:System.Object" />。此值可以是 null。</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是唯讀的。-或-<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定的大小。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Contains(System.Object)">
      <summary>判斷 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是否包含特定值。</summary>
      <returns>如果在 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中找到 <see cref="T:System.Object" />，則為 true，否則為 false。</returns>
      <param name="value">要在 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中尋找的物件。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IndexOf(System.Object)">
      <summary>判斷 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中特定項目的索引。</summary>
      <returns>如果可在清單中找到，則為 <paramref name="value" /> 的索引，否則為 -1。</returns>
      <param name="value">要在 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中尋找的物件。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>將項目插入 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中指定的索引處。</summary>
      <param name="index">應該插入 <paramref name="value" /> 之以零起始的索引。</param>
      <param name="value">要插入的 <see cref="T:System.Object" />。此值可以是 null。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。-或-<paramref name="index" /> 大於 <see cref="P:System.Xml.Serialization.XmlElementAttributes.Count" />。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是唯讀的。-或-<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定的大小。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IsFixedSize">
      <summary>取得值，指出 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是否有固定的大小。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 有固定大小，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#IsReadOnly">
      <summary>取得值，指出 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是否唯讀。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是唯讀的則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Item(System.Int32)">
      <summary>取得或設定指定之索引處的項目。</summary>
      <returns>在指定之索引處的項目。</returns>
      <param name="index">要取得或設定之以零起始的項目索引。</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 不是 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 中的有效索引。</exception>
      <exception cref="T:System.NotSupportedException">屬性已設定，而且 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是唯讀的。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlElementAttributes.System#Collections#IList#Remove(System.Object)">
      <summary>從 <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 移除特定物件的第一個相符項目。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 是唯讀的。-或-<see cref="T:System.Xml.Serialization.XmlElementAttributes" /> 具有固定的大小。</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlEnumAttribute">
      <summary>控制 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化列舉型別 (Enumeration) 成員的方式。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlEnumAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlEnumAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlEnumAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlEnumAttribute" /> 類別的新執行個體，並指定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 分別在序列化或還原序列化列舉型別時所產生或識別的 XML 值。</summary>
      <param name="name">列舉型別成員的覆寫名稱。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlEnumAttribute.Name">
      <summary>取得或設定當 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化列舉型別時，在 XML 文件執行個體所產生的值，或是當它還原序列化列舉型別成員時所識別的值。</summary>
      <returns>當 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化列舉型別時，在 XML 文件執行個體中所產生的值，或是當它還原序列化列舉型別成員時所識別的值。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlIgnoreAttribute">
      <summary>表示 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 的 <see cref="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)" /> 方法不要序列化公用欄位或公用讀取/寫入屬性值。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIgnoreAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlIgnoreAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlIncludeAttribute">
      <summary>讓 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 在對物件進行序列化或還原序列化時，能夠辨識型別。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlIncludeAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlIncludeAttribute" /> 類別的新執行個體。</summary>
      <param name="type">要包含的物件的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlIncludeAttribute.Type">
      <summary>取得或設定要包含的物件的型別。</summary>
      <returns>要包含的物件的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute">
      <summary>指定目標屬性、參數、傳回值或類別成員，包含與 XML 文件內使用之命名空間相關聯的前置詞。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlNamespaceDeclarationsAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Xml.Serialization.XmlRootAttribute">
      <summary>控制做為 XML 根項目之屬性目標的 XML 序列化。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlRootAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlRootAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlRootAttribute" /> 類別的新執行個體，並指定 XML 根項目的名稱。</summary>
      <param name="elementName">XML 根項目的名稱。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.DataType">
      <summary>取得或設定 XML 根項目的 XSD 資料型別。</summary>
      <returns>XSD (XML 結構描述文件) 資料型別，如全球資訊網協會 (www.w3.org ) 文件＜XML Schema: DataTypes＞中所定義。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.ElementName">
      <summary>取得或設定分別由 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 類別的 <see cref="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)" /> 和 <see cref="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)" /> 方法所產生和辨識的 XML 項目。</summary>
      <returns>在 XML 文件執行個體中所產生或辨識的 XML 根項目名稱。預設值為序列類別的名稱。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.IsNullable">
      <summary>取得或設定值，指出 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否必須將設為 null 的成員序列化為設為 true 的 xsi:nil 屬性。</summary>
      <returns>如果 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 產生 xsi:nil 屬性，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlRootAttribute.Namespace">
      <summary>取得或設定 XML 根項目的命名空間。</summary>
      <returns>XML 根項目的命名空間。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializer">
      <summary>將物件序列化成為 XML 文件，以及從 XML 文件將物件還原序列化。<see cref="T:System.Xml.Serialization.XmlSerializer" /> 可讓您控制如何將物件編碼為 XML。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 類別的新執行個體，該類別可將指定型別的物件序列化為 XML 文件，並可將 XML 文件還原序列化為指定型別的物件。</summary>
      <param name="type">這個 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可序列化的物件型別。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 類別的新執行個體，該類別可將指定型別的物件序列化為 XML 文件，並可將 XML 文件還原序列化為指定型別的物件。指定所有 XML 項目的預設命名空間。</summary>
      <param name="type">這個 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可序列化的物件型別。</param>
      <param name="defaultNamespace">用於所有 XML 項目的預設命名空間。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Type[])">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 類別的新執行個體，該類別可將指定型別的物件序列化為 XML 文件，並可將 XML 文件還原序列化為指定型別的物件。如果屬性或欄位傳回陣列，<paramref name="extraTypes" /> 參數就會指定可插入陣列的物件。</summary>
      <param name="type">這個 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可序列化的物件型別。</param>
      <param name="extraTypes">要序列化之其他物件型別的 <see cref="T:System.Type" /> 陣列。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 類別的新執行個體，該類別可將指定型別的物件序列化為 XML 文件，並可將 XML 文件還原序列化為指定型別的物件。每個要序列化的物件本身會包含類別執行個體，這個多載可以其他類別覆寫。</summary>
      <param name="type">要序列化的物件型別。</param>
      <param name="overrides">
        <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlAttributeOverrides,System.Type[],System.Xml.Serialization.XmlRootAttribute,System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 類別的新執行個體，該類別可將 <see cref="T:System.Object" /> 型別的物件序列化為 XML 文件執行個體，並可將 XML 文件執行個體還原序列化為 <see cref="T:System.Object" /> 型別的物件。每個要序列化的物件本身都可包含類別的執行個體，這個多載會使用其他類別對其進行覆寫。這個多載也會指定所有 XML 項目的預設命名空間，以及要做為 XML 根項目的類別。</summary>
      <param name="type">這個 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可序列化的物件型別。</param>
      <param name="overrides">
        <see cref="T:System.Xml.Serialization.XmlAttributeOverrides" />，延伸或覆寫指定在 <paramref name="type" /> 參數中類別的行為。</param>
      <param name="extraTypes">要序列化之其他物件型別的 <see cref="T:System.Type" /> 陣列。</param>
      <param name="root">定義 XML 根項目屬性的 <see cref="T:System.Xml.Serialization.XmlRootAttribute" />。</param>
      <param name="defaultNamespace">XML 文件中所有 XML 項目的預設命名空間。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.#ctor(System.Type,System.Xml.Serialization.XmlRootAttribute)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 類別的新執行個體，該類別可將指定型別的物件序列化為 XML 文件，並可將 XML 文件還原序列化為指定型別的物件。它還指定做為 XML 根項目的類別。</summary>
      <param name="type">這個 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可序列化的物件型別。</param>
      <param name="root">表示 XML 根項目的 <see cref="T:System.Xml.Serialization.XmlRootAttribute" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.CanDeserialize(System.Xml.XmlReader)">
      <summary>取得值，指出這個 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 是否可以還原序列化指定的 XML 文件。</summary>
      <returns>如果這個 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 可以還原序列化 <see cref="T:System.Xml.XmlReader" /> 所指向的物件，則為 true，否則為 false。</returns>
      <param name="xmlReader">
        <see cref="T:System.Xml.XmlReader" />，指向要還原序列化的文件。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.Stream)">
      <summary>還原序列化指定 <see cref="T:System.IO.Stream" /> 所包含的 XML 文件。</summary>
      <returns>要進行還原序列化的 <see cref="T:System.Object" />。</returns>
      <param name="stream">
        <see cref="T:System.IO.Stream" />，包含要還原序列化的 XML 文件。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.IO.TextReader)">
      <summary>還原序列化指定 <see cref="T:System.IO.TextReader" /> 所包含的 XML 文件。</summary>
      <returns>要進行還原序列化的 <see cref="T:System.Object" />。</returns>
      <param name="textReader">
        <see cref="T:System.IO.TextReader" />，包含要還原序列化的 XML 文件。</param>
      <exception cref="T:System.InvalidOperationException">在還原序列化期間發生錯誤。可以使用 <see cref="P:System.Exception.InnerException" /> 屬性取得原始例外狀況。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Deserialize(System.Xml.XmlReader)">
      <summary>還原序列化指定 <see cref="T:System.xml.XmlReader" /> 所包含的 XML 文件。</summary>
      <returns>要進行還原序列化的 <see cref="T:System.Object" />。</returns>
      <param name="xmlReader">
        <see cref="T:System.xml.XmlReader" />，包含要還原序列化的 XML 文件。</param>
      <exception cref="T:System.InvalidOperationException">在還原序列化期間發生錯誤。可以使用 <see cref="P:System.Exception.InnerException" /> 屬性取得原始例外狀況。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.FromTypes(System.Type[])">
      <summary>傳回由型別陣列建立的 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 物件的陣列。</summary>
      <returns>
        <see cref="T:System.Xml.Serialization.XmlSerializer" /> 物件的陣列。</returns>
      <param name="types">
        <see cref="T:System.Type" /> 物件的陣列。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object)">
      <summary>序列化指定的 <see cref="T:System.Object" />，並使用指定的 <see cref="T:System.IO.Stream" /> 將 XML 文件寫入檔案。</summary>
      <param name="stream">用來寫入 XML 文件的 <see cref="T:System.IO.Stream" />。</param>
      <param name="o">要序列化的 <see cref="T:System.Object" />。</param>
      <exception cref="T:System.InvalidOperationException">在序列化期間發生錯誤。可以使用 <see cref="P:System.Exception.InnerException" /> 屬性取得原始例外狀況。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.Stream,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>序列化指定的 <see cref="T:System.Object" />，使用指定的 <see cref="T:System.IO.Stream" /> 將 XML 文件寫入檔案，並參考指定的命名空間。</summary>
      <param name="stream">用來寫入 XML 文件的 <see cref="T:System.IO.Stream" />。</param>
      <param name="o">要序列化的 <see cref="T:System.Object" />。</param>
      <param name="namespaces">物件所參考的 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />。</param>
      <exception cref="T:System.InvalidOperationException">在序列化期間發生錯誤。可以使用 <see cref="P:System.Exception.InnerException" /> 屬性取得原始例外狀況。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object)">
      <summary>序列化指定的 <see cref="T:System.Object" />，並使用指定的 <see cref="T:System.IO.TextWriter" /> 將 XML 文件寫入檔案。</summary>
      <param name="textWriter">用來寫入 XML 文件的 <see cref="T:System.IO.TextWriter" />。</param>
      <param name="o">要序列化的 <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.IO.TextWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>將指定的 <see cref="T:System.Object" /> 序列化，使用指定的 <see cref="T:System.IO.TextWriter" /> 將 XML 文件寫入檔案，並參考指定的命名空間。</summary>
      <param name="textWriter">用來寫入 XML 文件的 <see cref="T:System.IO.TextWriter" />。</param>
      <param name="o">要序列化的 <see cref="T:System.Object" />。</param>
      <param name="namespaces">
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />，包含產生之 XML 文件的命名空間。</param>
      <exception cref="T:System.InvalidOperationException">在序列化期間發生錯誤。可以使用 <see cref="P:System.Exception.InnerException" /> 屬性取得原始例外狀況。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object)">
      <summary>序列化指定的 <see cref="T:System.Object" />，並使用指定的 <see cref="T:System.Xml.XmlWriter" /> 將 XML 文件寫入檔案。</summary>
      <param name="xmlWriter">用來寫入 XML 文件的 <see cref="T:System.xml.XmlWriter" />。</param>
      <param name="o">要序列化的 <see cref="T:System.Object" />。</param>
      <exception cref="T:System.InvalidOperationException">在序列化期間發生錯誤。可以使用 <see cref="P:System.Exception.InnerException" /> 屬性取得原始例外狀況。</exception>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializer.Serialize(System.Xml.XmlWriter,System.Object,System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>序列化指定的 <see cref="T:System.Object" />，使用指定的 <see cref="T:System.Xml.XmlWriter" /> 將 XML 文件寫入檔案，並參考指定的命名空間。</summary>
      <param name="xmlWriter">用來寫入 XML 文件的 <see cref="T:System.xml.XmlWriter" />。</param>
      <param name="o">要序列化的 <see cref="T:System.Object" />。</param>
      <param name="namespaces">物件所參考的 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" />。</param>
      <exception cref="T:System.InvalidOperationException">在序列化期間發生錯誤。可以使用 <see cref="P:System.Exception.InnerException" /> 屬性取得原始例外狀況。</exception>
    </member>
    <member name="T:System.Xml.Serialization.XmlSerializerNamespaces">
      <summary>將 XML 命名空間 (Namespace) 和 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 用來產生限定名稱的前置詞包含在 XML 文件執行個體中。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor(System.Xml.Serialization.XmlSerializerNamespaces)">
      <summary>使用包含前置詞和命名空間配對集合之 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 的指定執行個體，初始化 XmlSerializerNamespaces 類別的新執行個體。</summary>
      <param name="namespaces">
        <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 的執行個體，包含命名空間和前置詞配對。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.#ctor(System.Xml.XmlQualifiedName[])">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 類別的新執行個體。</summary>
      <param name="namespaces">
        <see cref="T:System.Xml.XmlQualifiedName" /> 物件的陣列。</param>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.Add(System.String,System.String)">
      <summary>將前置詞和命名空間配對加入 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 物件。</summary>
      <param name="prefix">與 XML 命名空間相關的前置詞。</param>
      <param name="ns">XML 命名空間。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlSerializerNamespaces.Count">
      <summary>取得集合中前置詞和命名空間配對的數目。</summary>
      <returns>集合中前置詞和命名空間配對數目。</returns>
    </member>
    <member name="M:System.Xml.Serialization.XmlSerializerNamespaces.ToArray">
      <summary>取得 <see cref="T:System.Xml.Serialization.XmlSerializerNamespaces" /> 物件中前置詞和命名空間配對的陣列。</summary>
      <returns>
        <see cref="T:System.Xml.XmlQualifiedName" /> 物件的陣列，在 XML 文件中用作限定名稱。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTextAttribute">
      <summary>表示 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 在序列化或還原序列化包含它的類別之後，應該將成員視為 XML 文字。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTextAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlTextAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTextAttribute.#ctor(System.Type)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlTextAttribute" /> 類別的新執行個體。</summary>
      <param name="type">要序列化之成員的 <see cref="T:System.Type" />。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTextAttribute.DataType">
      <summary>取得或設定 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 所產生之文字的XML 結構描述定義語言 (XSD) 資料型別。</summary>
      <returns>XML 結構描述 (XSD) 資料型別，如全球資訊網協會 (www.w3.org ) 文件＜XML Schema Part 2: Datatypes＞中所定義。</returns>
      <exception cref="T:System.Exception">您指定的 XML 結構描述資料型別無法對應至 .NET 資料型別。</exception>
      <exception cref="T:System.InvalidOperationException">您指定的 XML 結構描述資料型別對於該屬性無效，且無法轉換為成員型別。</exception>
    </member>
    <member name="P:System.Xml.Serialization.XmlTextAttribute.Type">
      <summary>取得或設定成員的型別。</summary>
      <returns>成員的 <see cref="T:System.Type" />。</returns>
    </member>
    <member name="T:System.Xml.Serialization.XmlTypeAttribute">
      <summary>控制由 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化屬性 (Attribute) 目標後所產生的 XML 結構描述。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Xml.Serialization.XmlTypeAttribute.#ctor(System.String)">
      <summary>初始化 <see cref="T:System.Xml.Serialization.XmlTypeAttribute" /> 類別的新執行個體，並指定 XML 型別的名稱。</summary>
      <param name="typeName">XML 型別的名稱，是 <see cref="T:System.Xml.Serialization.XmlSerializer" /> 序列化類別執行個體時所產生的 (並且對類別執行個體進行還原序列化時所辨識的)。</param>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.AnonymousType">
      <summary>取得或設定值，判斷產生的結構描述型別是否為 XSD 匿名型別。</summary>
      <returns>如果產生的結構描述型別是 XSD 匿名型別則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.IncludeInSchema">
      <summary>取得或設定值，指出是否將型別包含在 XML 結構描述文件中。</summary>
      <returns>若要將型別包含於 XML 結構描述文件中，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.Namespace">
      <summary>取得或設定 XML 型別的命名空間。</summary>
      <returns>XML 型別的命名空間。</returns>
    </member>
    <member name="P:System.Xml.Serialization.XmlTypeAttribute.TypeName">
      <summary>取得或設定 XML 型別的名稱。</summary>
      <returns>XML 型別的名稱。</returns>
    </member>
  </members>
</doc>