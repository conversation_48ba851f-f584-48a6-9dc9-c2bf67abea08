package com.phonecheck.model.util.firmware;

import com.phonecheck.model.cloudapi.LatestFirmwareListResponse;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.util.LogicalFirmwareModelMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class LogicalFirmwareModelMapperTest {
    @Mock
    private LatestFirmwareListResponse latestFirmwareListResponse;
    private LogicalFirmwareModelMapper logicalFirmwareModelMapper;

    @BeforeEach
    public void setUp() {
        logicalFirmwareModelMapper = new LogicalFirmwareModelMapper();
    }

    @Test
    public void generateFirmwareLogicalDataModelListTest() {
        LatestFirmwareListResponse.LatestFirmwareInfo firmwareInfo =
                new LatestFirmwareListResponse.LatestFirmwareInfo();
        firmwareInfo.setId("Iphone 10,1");
        firmwareInfo.setName("Test Firmware");
        LatestFirmwareListResponse.Firmwares firmware = new LatestFirmwareListResponse.Firmwares();
        firmware.setFileName("testfile");
        firmware.setReleaseDate("2023-01-01");
        firmware.setSize(1024L);
        firmware.setVersion("1.0");
        firmware.setSigned(true);

        firmwareInfo.setFirmwares(List.of(new LatestFirmwareListResponse.Firmwares[]{firmware}));
        when(latestFirmwareListResponse.getLatestFirmwareInfo())
                .thenReturn(new LatestFirmwareListResponse.LatestFirmwareInfo[]{firmwareInfo});

        Map<String, FirmwareModel.FirmwareResponse> result =
                logicalFirmwareModelMapper.generateFirmwareLogicalDataModelList(latestFirmwareListResponse);

        assertNotNull(result);
        assertFalse(result.isEmpty());

        FirmwareModel.FirmwareResponse logicalInfo = result.get(firmwareInfo.getId());
        assertEquals(firmwareInfo.getId(), logicalInfo.getId());
        assertEquals(firmwareInfo.getName(), logicalInfo.getName());
        assertEquals(firmware.getFileName(), logicalInfo.getFileName());
        assertEquals(firmware.getReleaseDate(), logicalInfo.getReleaseDate());
        assertEquals(String.valueOf(firmware.getSize()), logicalInfo.getSize());
        assertEquals(firmware.getVersion(), logicalInfo.getVersion());
        assertFalse(logicalInfo.isSelected());
        assertEquals(0, logicalInfo.getPercentage());

        verify(latestFirmwareListResponse, times(1)).getLatestFirmwareInfo();
    }
}
