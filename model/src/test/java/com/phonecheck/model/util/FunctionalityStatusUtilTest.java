package com.phonecheck.model.util;

import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.mdm.MdmInfo;
import com.phonecheck.model.mdm.MdmStatus;
import com.phonecheck.model.status.EsnStatus;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.InitialDefectKey;
import com.phonecheck.model.test.TestResults;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

class FunctionalityStatusUtilTest {
    @Test
    public void isBatteryDegradedTest1() {
        IosDevice device = new IosDevice();
        device.setBatteryDegraded(true);
        CloudCustomizationResponse customization = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .appleFail(true)
                        .build())
                .build();

        Assertions.assertTrue(FunctionalityStatusUtil.isBatteryDegraded(device, customization));
    }

    @Test
    public void isBatteryDegradedTest2() {
        IosDevice device = new IosDevice();
        BatteryInfo batteryInfo = new BatteryInfo();
        batteryInfo.setHealthPercentage(72);
        device.setBatteryInfo(batteryInfo);
        CloudCustomizationResponse customization = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .appleFail(true)
                        .build())
                .build();

        Assertions.assertTrue(FunctionalityStatusUtil.isBatteryDegraded(device, customization));
    }

    @Test
    public void isBatteryDegradedTest3() {
        IosDevice device = new IosDevice();
        device.setBatteryStateHealth(75);
        CloudCustomizationResponse customization = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .appleFail(true)
                        .build())
                .build();
        Assertions.assertTrue(FunctionalityStatusUtil.isBatteryDegraded(device, customization));
    }

    @Test
    public void getBatteryHealthCriteriaTest() {
        IosDevice device = new IosDevice();
        device.setBatteryInfo(BatteryInfo.builder().healthPercentage(75).cycle(1500).build());

        CloudCustomizationResponse customizations = CloudCustomizationResponse.builder().build();
        Assertions.assertNull(FunctionalityStatusUtil.getBatteryHealthCriteria(device, customizations, true));

        customizations = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder().customizeBattery(true).build())
                .build();
        Assertions.assertNull(FunctionalityStatusUtil.getBatteryHealthCriteria(device, customizations, true));

        CloudCustomizationResponse.Rule healthRule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(null).maximumCycle(null).build();
        CloudCustomizationResponse.HealthCriteria healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true).name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{healthRule1}).build();
        customizations = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .customizeBattery(true)
                        .healthCriteria(new CloudCustomizationResponse.HealthCriteria[]{healthCriteria1})
                        .build())
                .build();
        Assertions.assertNull(FunctionalityStatusUtil.getBatteryHealthCriteria(device, customizations, true));

        healthRule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(80).maximumCycle(null).build();
        CloudCustomizationResponse.Rule healthRule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(70).maximumCycle(null).build();
        healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true).name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{healthRule1, healthRule2}).build();
        customizations = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .customizeBattery(true)
                        .healthCriteria(new CloudCustomizationResponse.HealthCriteria[]{healthCriteria1})
                        .build())
                .build();
        Assertions.assertEquals(InitialDefectKey.BH_A,
                FunctionalityStatusUtil.getBatteryHealthCriteria(device, customizations, true));

        healthRule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(null).maximumCycle(1000).build();
        healthRule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(null).maximumCycle(1200).build();
        healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true).name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{healthRule1, healthRule2}).build();
        customizations = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .customizeBattery(true)
                        .healthCriteria(new CloudCustomizationResponse.HealthCriteria[]{healthCriteria1})
                        .build())
                .build();
        Assertions.assertEquals(InitialDefectKey.BH_B,
                FunctionalityStatusUtil.getBatteryHealthCriteria(device, customizations, true));

        healthRule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(50).maximumCycle(1000).build();
        healthRule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(40).maximumCycle(2000).build();
        healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true).name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{healthRule1, healthRule2}).build();
        customizations = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .customizeBattery(true)
                        .healthCriteria(new CloudCustomizationResponse.HealthCriteria[]{healthCriteria1})
                        .build())
                .build();
        Assertions.assertEquals(InitialDefectKey.BH_A,
                FunctionalityStatusUtil.getBatteryHealthCriteria(device, customizations, true));

        device.setBatteryInfo(BatteryInfo.builder().healthPercentage(55).build());
        healthRule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(80).maximumCycle(null).build();
        healthRule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(70).maximumCycle(null).build();
        CloudCustomizationResponse.Rule healthRule3 = CloudCustomizationResponse.Rule.builder()
                .code("BH-C").minimumCapacity(60).maximumCycle(null).build();
        CloudCustomizationResponse.Rule healthRule4 = CloudCustomizationResponse.Rule.builder()
                .code("BH-D").minimumCapacity(50).maximumCycle(null).build();
        healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true).name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]
                        {healthRule1, healthRule2, healthRule3, healthRule4}).build();
        customizations = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .customizeBattery(true)
                        .healthCriteria(new CloudCustomizationResponse.HealthCriteria[]{healthCriteria1})
                        .build())
                .build();
        Assertions.assertEquals(InitialDefectKey.BH_C,
                FunctionalityStatusUtil.getBatteryHealthCriteria(device, customizations, true));

        device.setBatteryInfo(BatteryInfo.builder().healthPercentage(55).cycle(1500).build());
        //Below health criteria is for apple devices only
        healthRule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(50).maximumCycle(1000).build();
        healthRule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(40).maximumCycle(1200).build();
        healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAppleIphoneDevices(true).name("Apple Health Criteria")
                .rules(new CloudCustomizationResponse.Rule[]{healthRule1, healthRule2}).build();
        //Below health criteria is for android devices only
        healthRule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(50).maximumCycle(1000).build();
        healthRule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(40).maximumCycle(1400).build();
        CloudCustomizationResponse.HealthCriteria healthCriteria2 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAndroidDevices(true).name("Android Health Criteria")
                .rules(new CloudCustomizationResponse.Rule[]{healthRule1, healthRule2}).build();
        customizations = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .customizeBattery(true)
                        .healthCriteria(new CloudCustomizationResponse.HealthCriteria[]
                                {healthCriteria1, healthCriteria2})
                        .build())
                .build();
        Assertions.assertEquals(InitialDefectKey.BH_B,
                FunctionalityStatusUtil.getBatteryHealthCriteria(device, customizations, true));
    }

    @Test
    public void isIcloudOnTest() {
        IosDevice device = new IosDevice();
        device.setDeviceLock(DeviceLock.ON);
        Assertions.assertTrue(FunctionalityStatusUtil.isDeviceLockOn(device));
    }

    @Test
    public void isMdmOnTest() {
        IosDevice device = new IosDevice();
        MdmInfo mdmInfo = new MdmInfo();
        mdmInfo.setMdmStatus(MdmStatus.ON);
        device.setMdmInfo(mdmInfo);
        Assertions.assertTrue(FunctionalityStatusUtil.isMdmOn(device));
    }

    @Test
    public void isMdmPreconfiguredTest() {
        IosDevice device = new IosDevice();
        device.setMdmInfo(MdmInfo.builder().build());
        device.setMdmStatus(MdmStatus.PRECONFIGURED);
        Assertions.assertTrue(FunctionalityStatusUtil.isMdmPreconfigured(device));
    }

    @Test
    public void isBadEsnTest() {
        IosDevice device = new IosDevice();
        device.setEsnStatus(EsnStatus.ESN_BAD.getKey());
        Assertions.assertTrue(FunctionalityStatusUtil.isESNBad(device));
    }

    @Test
    public void getFunctionalityStatusTest1() {
        IosDevice device = new IosDevice();
        device.setEsnStatus(EsnStatus.ESN_BAD.getKey());
        CloudCustomizationResponse.Rule rule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(80).build();
        CloudCustomizationResponse.Rule rule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(70).build();
        CloudCustomizationResponse.HealthCriteria healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true)
                .name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{rule1, rule2})
                .build();

        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(List.of(CloudCustomizationResponse.AutomationSteps.SETUP_INSTALL_APP))
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        Assertions.assertEquals(
                FunctionalityStatusUtil.PENDING,
                FunctionalityStatusUtil.getFunctionalityStatus(device, true, assignedCustomization)
        );
    }

    @Test
    public void getFunctionalityStatusTest2() {
        IosDevice device = new IosDevice();

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(new TestResults());
        deviceTestResult.getTestResults().setTestingCompleted(true);

        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setFailed(
                new ArrayList<>(Arrays.asList("failed")));
        device.getDeviceTestResult().getTestResults().setTotal(
                new ArrayList<>(Arrays.asList("failed")));

        CloudCustomizationResponse.Rule rule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(80).build();
        CloudCustomizationResponse.Rule rule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(70).build();
        CloudCustomizationResponse.HealthCriteria healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true)
                .name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{rule1, rule2})
                .build();
        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(List.of(CloudCustomizationResponse.AutomationSteps.SETUP_INSTALL_APP))
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        Assertions.assertEquals(
                FunctionalityStatusUtil.SEE_NOTES,
                FunctionalityStatusUtil.getFunctionalityStatus(device, true, assignedCustomization)
        );
    }

    @Test
    public void getFunctionalityStatusTest3() {
        IosDevice device = new IosDevice();

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(new TestResults());
        deviceTestResult.getTestResults().setTestingCompleted(false);

        device.setDeviceLock(DeviceLock.ON);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setPassed(
                new ArrayList<>(Arrays.asList("passed")));

        CloudCustomizationResponse.Rule rule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(80).build();
        CloudCustomizationResponse.Rule rule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(70).build();
        CloudCustomizationResponse.HealthCriteria healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true)
                .name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{rule1, rule2})
                .build();
        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(List.of(CloudCustomizationResponse.AutomationSteps.SETUP_INSTALL_APP))
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        Assertions.assertEquals(
                FunctionalityStatusUtil.PENDING,
                FunctionalityStatusUtil.getFunctionalityStatus(device, true, assignedCustomization)
        );
    }

    @Test
    public void getFunctionalityStatusTest4() {
        IosDevice device = new IosDevice();

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(new TestResults());
        deviceTestResult.getTestResults().setTestingCompleted(true);

        device.setEsnStatus(EsnStatus.ESN_GOOD.getKey());
        device.setDeviceLock(DeviceLock.OFF);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setPassed(
                new ArrayList<>(Arrays.asList("passed")));
        device.getDeviceTestResult().getTestResults().setTotal(
                new ArrayList<>(Arrays.asList("passed")));
        CloudCustomizationResponse.Rule rule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(80).build();
        CloudCustomizationResponse.Rule rule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(70).build();
        CloudCustomizationResponse.HealthCriteria healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true)
                .name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{rule1, rule2})
                .build();
        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(List.of(CloudCustomizationResponse.AutomationSteps.SETUP_INSTALL_APP))
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        Assertions.assertEquals(
                FunctionalityStatusUtil.FULLY_FUNCTIONAL,
                FunctionalityStatusUtil.getFunctionalityStatus(device, true, assignedCustomization)
        );
    }

    @Test
    public void getFunctionalityStatusTest5() {
        IosDevice device = new IosDevice();

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(new TestResults());

        device.setEsnStatus(EsnStatus.ESN_GOOD.getKey());
        device.setDeviceLock(DeviceLock.OFF);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().getTestResults().setPassed(
                new ArrayList<>(Arrays.asList("Touch Id")));
        CloudCustomizationResponse.Rule rule1 = CloudCustomizationResponse.Rule.builder()
                .code("BH-A").minimumCapacity(80).build();
        CloudCustomizationResponse.Rule rule2 = CloudCustomizationResponse.Rule.builder()
                .code("BH-B").minimumCapacity(70).build();
        CloudCustomizationResponse.HealthCriteria healthCriteria1 = CloudCustomizationResponse.HealthCriteria.builder()
                .applyAllDevices(true)
                .name("Health Criteria 1")
                .rules(new CloudCustomizationResponse.Rule[]{rule1, rule2})
                .build();
        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(List.of(CloudCustomizationResponse.AutomationSteps.SETUP_INSTALL_APP))
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        Assertions.assertEquals(
                FunctionalityStatusUtil.PENDING,
                FunctionalityStatusUtil.getFunctionalityStatus(device, true, assignedCustomization)
        );
    }

    @Test
    @DisplayName("Get functionality status if found failed result, app not installed and setup not found")
    public void getFunctionalityStatusTest6() {
        IosDevice device = new IosDevice();
        device.setAppInstalled(false);
        device.setEsnStatus(EsnStatus.ESN_BAD.getKey());
        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(null)
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        Assertions.assertEquals(
                "",
                FunctionalityStatusUtil.getFunctionalityStatus(device, true, assignedCustomization)
        );
    }

    @Test
    @DisplayName("Get functionality status if found failed result, app installed and setup not found")
    public void getFunctionalityStatusIfFoundFailedButAppInstalled() {
        IosDevice device = new IosDevice();
        device.setAppInstalled(true);
        device.setEsnStatus(EsnStatus.ESN_BAD.getKey());
        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(null)
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        Assertions.assertEquals(
                FunctionalityStatusUtil.PENDING,
                FunctionalityStatusUtil.getFunctionalityStatus(device, true, assignedCustomization)
        );
    }

    @Test
    @DisplayName("Get functionality status if found failed result, app not installed and setup step found")
    public void getFunctionalityStatusIfFoundFailedAndFoundSetupInstalled() {
        IosDevice device = new IosDevice();
        device.setAppInstalled(false);
        device.setEsnStatus(EsnStatus.ESN_BAD.getKey());
        CloudCustomizationResponse.WorkflowSettings settings = CloudCustomizationResponse.WorkflowSettings.builder()
                .connectionWorkflow(List.of(CloudCustomizationResponse.AutomationSteps.SETUP_INSTALL_APP))
                .build();
        CloudCustomizationResponse assignedCustomization = CloudCustomizationResponse.builder()
                .workflow(settings)
                .build();
        Assertions.assertEquals(
                FunctionalityStatusUtil.PENDING,
                FunctionalityStatusUtil.getFunctionalityStatus(device, true, assignedCustomization)
        );
    }
}
