package com.phonecheck.model.manual;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ManualEntryModel {
    private String transactionId;
    private String packageName;
    private String stationId;
    private String testerName;
    private String boxNo;
    private String vendorName;
    private String invoiceNo;
    private String imei;
    private String serial;
    private String os;
    private String make;
    private String model;
    private String gb;
    private String color;
    private String modelNumber;
    private String regulatoryModelNumber;
    private String carrier;
    private String unlockStatus;
    private String esn;
    private String deviceLock;
    private String grade;
    private String cosmetic;
    private String bhPercentage;
    private String notes;
    private String working;
    private String failed;
    private String lpn;
    private String custom1;
    private String skuCode;
    private String firmware;
    private String ram;
    private String appleId;
    private String rooted;
    private String defectsCode;
    private boolean isSelected;
}

