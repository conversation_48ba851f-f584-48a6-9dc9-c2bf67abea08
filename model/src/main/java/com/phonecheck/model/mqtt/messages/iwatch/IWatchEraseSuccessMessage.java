package com.phonecheck.model.mqtt.messages.iwatch;

import com.phonecheck.model.mqtt.messages.MqttConnectionMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * MQTT message passed to the frontend when iWatch erase successfully
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IWatchEraseSuccessMessage extends MqttConnectionMessage {
    private String erasedIwatchSerial;
}
