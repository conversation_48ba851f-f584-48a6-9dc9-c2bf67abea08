package com.phonecheck.model.mqtt;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.mqtt.messages.GenericMqttRequestMessage;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class AutoPrintRequestMessage extends GenericMqttRequestMessage {
    private Device device;
    private String label1Name;
    private String label2Name;
    private String printer1Name;
    private String rollTypePrinter1;
    private String selectedTrayPrinter1;
    private boolean isDymoTwinTurboPrinter1;
    private String printer2Name;
    private String rollTypePrinter2;
    private String selectedTrayPrinter2;
    private boolean isDymoTwinTurboPrinter2;
    private String selectedPaperType1;
    private String selectedPaperType2;
}