package com.phonecheck.model.mqtt.messages.iwatch;

import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.mqtt.messages.MqttConnectionMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * MQTT message passed to the frontend post 2-way API call for iWatch
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IWatchApiStatusMessage extends MqttConnectionMessage {
    private DeviceStage deviceStage;
}
