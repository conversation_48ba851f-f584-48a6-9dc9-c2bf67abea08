package com.phonecheck.model.mqtt.messages;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Message class representing a request to get active grading information via MQTT.
 * This message contains the profile ID associated with the grading request.
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class GetActiveGradingRequestMessage extends GenericMqttRequestMessage {
    private String profileId;
    private List<String> subGrades;
}