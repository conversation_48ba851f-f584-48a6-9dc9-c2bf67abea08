package com.phonecheck.model.grading;

/**
 * Enum representing the various statuses for vendor criteria evaluation.
 */
public enum VendorCriteriaStatus {
    CRITERIA_PASSED,
    CRIT<PERSON>IA_FAILED,
    CRITERIA_NOT_TESTED,
    <PERSON><PERSON>ERIA_IGNORED,
    CRITERIA_BATTERY_QUALIFIED,
    CRITERIA_BATTERY_NOT_QUALIFIED,
    CRITERIA_BATTERY_IGNORED,
    CR<PERSON>ERIA_MISSING_TEST_REQUIREMENT,
    CRITERIA_MISSING_ESN,
    CRITERIA_ESN_BAD,
    CRITERIA_MISSING_MDM,
    CRITERIA_MISSING_KNOX,
    CRITERIA_MDM_ON,
    CRITERIA_KNOX_ON,
    CRITERIA_MISSING_DEVICE_LOCK,
    CRITERIA_MISSING_I_CLOUD,
    CRITERIA_DEVICE_LOCK,
    CRITERIA_I_DEVICE_LOCK,
    CRITERIA_MISSING_DEVICE_ROOTED,
    CRITERIA_MISSING_DEVICE_JAIL_BREAK,
    CRITERIA_DEVICE_ROOTED,
    CRITERIA_DEVICE_JAIL_BREAK,
    CRITERIA_MISSING_OEM,
    CRITERIA_OEM_NA,
    CRITERIA_OEM_NOT_GENUINE,
    CRITERIA_WORKING_NO,
    CRITERIA_WORKING_PENDING;
}
