package com.phonecheck.model.util;

import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.device.*;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.manual.ManualEntryModel;
import com.phonecheck.model.status.RootedStatus;
import com.phonecheck.model.test.CosmeticsResults;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.TestResults;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

public class ManualEntryUtil {
    /**
     * Creates a Device object by mapping data from a ManualEntryModel.
     * This method converts data from a ManualEntryModel into a syncable Device object.
     *
     * @param manualEntry The ManualEntryModel containing data to be mapped to the Device.
     * @return A Device object with data mapped from the ManualEntryModel.
     */
    public static Device setupDeviceFromManualEntry(final ManualEntryModel manualEntry) {
        Device device;
        DeviceFamily family = DeviceFamily.IOS;
        if (StringUtils.isNotBlank(manualEntry.getOs())) {
            family = StringUtils.containsIgnoreCase(manualEntry.getOs(), "ios") ||
                    StringUtils.containsIgnoreCase(manualEntry.getOs(), "watchos") ||
                    StringUtils.containsIgnoreCase(manualEntry.getOs(), "ipados") ?
                    DeviceFamily.IOS : DeviceFamily.ANDROID;
        }
        if (DeviceFamily.ANDROID.equals(family)) {
            device = new AndroidDevice();
            device.setCustom1(manualEntry.getRegulatoryModelNumber());
        } else {
            device = new IosDevice();
            ((IosDevice) device).setRegulatoryModelNumber(manualEntry.
                    getRegulatoryModelNumber());
            ((IosDevice) device).setCurrentAppleId(manualEntry.getAppleId());
        }

        device.setRam(manualEntry.getRam());

        device.setId(StringUtils.isBlank(manualEntry.getSerial()) ?
                manualEntry.getImei() : manualEntry.getSerial());
        device.setImei(manualEntry.getImei());
        device.setSerial(StringUtils.isBlank(manualEntry.getSerial()) ?
                manualEntry.getImei() : manualEntry.getSerial());
        device.setOperatingSystem(StringUtils.isBlank(manualEntry.getOs()) ? "iOS" : manualEntry.getOs());
        device.setMake(manualEntry.getMake());

        if (StringUtils.isNotBlank(manualEntry.getGb())) {
            // we are getting disk-size like this "64GB" or "1TB"
            // fetch size from record to show on label
            String memory;
            MemoryUnit memoryUnit;
            if (StringUtils.containsIgnoreCase(manualEntry.getGb(), "TB")) {
                memory = manualEntry.getGb().replace("TB", "");
                memoryUnit = MemoryUnit.TERABYTES;
            } else {
                memory = manualEntry.getGb().replace("GB", "");
                memoryUnit = MemoryUnit.GIGABYTES;
            }

            device.setDiskSize(
                    DiskSize.builder()
                            .size(NumberUtils.isParsable(memory) ? Long.parseLong(memory) : 0L)
                            .memoryUnit(memoryUnit)
                            .build()
            );
        }
        device.setVendorName(manualEntry.getVendorName());
        device.setInvoiceNo(manualEntry.getInvoiceNo());
        device.setBoxNo(manualEntry.getBoxNo());
        device.setPackageName(manualEntry.getPackageName());
        device.setStationId(manualEntry.getStationId());
        device.setTesterName(manualEntry.getTesterName());
        device.setModel(manualEntry.getModel());
        device.setColor(manualEntry.getColor());
        device.setModelNo(manualEntry.getModelNumber());
        device.setCarrier(manualEntry.getCarrier());
        device.setUnlockStatus(manualEntry.getUnlockStatus());
        device.setEsnStatus(manualEntry.getEsn());
        device.setDeviceLock(DeviceLock.fromKey(manualEntry.getDeviceLock()));
        device.setGrade(manualEntry.getGrade());
        device.setNotes(manualEntry.getNotes());
        device.setLpn(manualEntry.getLpn());
        device.setCustom1(manualEntry.getCustom1());
        device.setSkuCode(manualEntry.getSkuCode());
        device.setFirmware(manualEntry.getFirmware());
        device.setDefectsCode(manualEntry.getDefectsCode());
        if (StringUtils.isNotBlank(manualEntry.getRooted())) {
            device.setRooted((StringUtils.equalsIgnoreCase(manualEntry.getRooted(), "On"))
                    ? RootedStatus.ROOTED : RootedStatus.NOT_ROOTED);
        }
        device.setStage(DeviceStage.MANUAL_ENTRY);
        device.setDeviceFamily(family);
        device.setManualEntry(true);

        // Add test results and battery info into syncable device
        setAdditionalDeviceDetail(manualEntry, device);

        return device;
    }

    /**
     * Sets additional device details based on the provided manual entry data.
     *
     * @param manualEntry The manual entry model containing data to set additional device details.
     * @param device      The device object to which additional details will be set.
     */
    private static void setAdditionalDeviceDetail(final ManualEntryModel manualEntry, final Device device) {
        BatteryInfo batteryInfo = null;
        if (StringUtils.isNotBlank(manualEntry.getBhPercentage())) {
            batteryInfo = BatteryInfo.builder()
                    .healthPercentage(Integer.parseInt(manualEntry.getBhPercentage()))
                    .build();
        }

        String working = manualEntry.getWorking();
        if ("Yes".equalsIgnoreCase(working) ||
                "No".equalsIgnoreCase(working) ||
                "Pending".equalsIgnoreCase(working)) {
            device.setManualEntryWorkingStatus(working);
        }

        CosmeticsResults cosmeticsResults = null;
        if (StringUtils.isNotBlank(manualEntry.getCosmetic())) {
            cosmeticsResults = new CosmeticsResults();
            cosmeticsResults.setFailed(manualEntry.getCosmetic());
        }
        TestResults testResults = null;
        if (StringUtils.isNotBlank(manualEntry.getFailed())) {
            testResults = new TestResults();
            testResults.setFailed(
                    TestResultsUtil.commaSeparatedStringToList(manualEntry.getFailed()));
        }

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setCosmeticResults(cosmeticsResults);
        deviceTestResult.setTestResults(testResults);

        device.setDeviceTestResult(deviceTestResult);
        device.setBatteryInfo(batteryInfo);
    }
}
