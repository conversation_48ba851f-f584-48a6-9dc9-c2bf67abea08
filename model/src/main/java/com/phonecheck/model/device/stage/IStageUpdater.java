package com.phonecheck.model.device.stage;


public interface IStageUpdater {
    void update(final InitialConnectionStage stage);

    void update(final PairSuccessStage stage);

    void update(final PairFailureStage stage);

    void update(final InfoCollectionSuccessStage stage);

    void update(final InfoCollectionFailureStage stage);

    void update(final BatteryInfoSuccessStage stage);

    void update(final BatteryInfoFailureStage stage);

    void update(final ActivationSuccessStage stage);

    void update(final ActivationFailureStage stage);

    void update(final AppInstallSuccessStage stage);

    void update(final AppInstallFailureStage stage);

    void update(final MountImageSuccessStage stage);

    void update(final MountImageFailureStage stage);

    void update(final StartTestsSuccessStage stage);

    void update(final StartTestsFailureStage stage);

    void update(final TestConfigSuccessStage stage);

    void update(final AppTestingDoneStage stage);

    void update(final MdmStatusSuccessStage stage);

    void update(final OemDataCollectionSuccessStage stage);

    void update(final IcloudInfoSuccessStage stage);

    void update(final EsnInfoSuccessStage stage);

    void update(final SimLockInfoSuccessStage stage);

    void update(final CarrierLockInfoSuccessStage stage);

    void update(final ReadyStage stage);

    void update(final NotReadyStage stage);

    void update(final FrpInfoSuccessStage stage);

    void update(final MeidInfoSuccessStage stage);

    void update(final PcUtilityInfoSuccessStage stage);

    void update(final KnoxInfoSuccessStage stage);

    void update(final ImeiCollectionSuccessStage stage);

    void update(final ImeiCollectionFailureStage stage);

    void update(final AndroidMdmStatusSuccessStage stage);

    void update(final RootInfoSuccessStage stage);
}
