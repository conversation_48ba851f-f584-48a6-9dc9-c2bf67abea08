package com.phonecheck.model.device;

import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class IWatchInfo {
    private String imei;
    private String serial;
    private String productType;
    private String batteryCapacity;
    private String chargingStatus;
    private String amountDataAvailable;
    private String hardwareModel;
    private String name;
    private String bluetoothAddress;
    private String regionInfo;
    private String modelNumber;
    private String productName;
    private String wifiAddress;
    private String productVersion;
    private String color;
    private String colorCode;
    private String enclosureColorCode;
    private String totalDataCapacity;
    private String uniqueDeviceID;
    private boolean isSourceApiCalled;
}
