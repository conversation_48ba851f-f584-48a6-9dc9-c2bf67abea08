package com.phonecheck.model.device.stage;

import lombok.Getter;

public enum DeviceState {
    HELLO("Hello"),
    HOME("Home"),

    DFU("Dfu");

    @Getter
    private final String text;

    DeviceState(final String text) {
        this.text = text;
    }

    public static DeviceState fromText(String text) {
        for (DeviceState state : values()) {
            if (state.text.equalsIgnoreCase(text)) {
                return state;
            }
        }
        return null;
    }
}
