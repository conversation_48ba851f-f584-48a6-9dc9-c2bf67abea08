package com.phonecheck.model.device.stage;

import com.phonecheck.model.status.ActivationStatus;
import lombok.Builder;
import lombok.Data;

/**
 * Models a device activation failure
 */
@Data
@Builder
public class ActivationFailureStage implements IStage {
    private final DeviceStage stage = DeviceStage.ACTIVATION_FAILED;
    private String id;
    private String transactionId;
    private ActivationStatus activationStatus;
    private long timestamp;

    @Override
    public void updateStage(IStageUpdater updater) {
        updater.update(this);
    }
}