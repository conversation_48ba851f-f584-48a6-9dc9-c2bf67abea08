package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CloudCustomizationResponse {

    private String id;
    private String name;
    private String password;
    private String labels;
    @JsonProperty("masterId")
    private String masterId;
    @JsonProperty("requiredFields")
    private RequiredFields requiredFields;
    @JsonProperty("restrictiveActions")
    private RestrictiveActions restrictiveActions;
    @JsonProperty("fields")
    private Fields fields;
    private List<String> grading;
    @JsonProperty("defaultGrade")
    private String defaultGrade;
    @JsonProperty("carriers")
    private List<String> carriers;
    @JsonProperty("colors")
    private List<String> colors;
    @JsonProperty("defaultCarrier")
    private String defaultCarrier;
    @JsonProperty("wifiSettings")
    private WifiSettings wifiSettings;
    @JsonProperty("batterySettings")
    private BatterySettings batterySettings;
    @JsonProperty("printerSettings")
    private PrinterSettings printerSettings;
    @JsonProperty("imeiCheck")
    private ImeiCheck imeiCheck;
    @JsonProperty("cosmeticSettings")
    private CosmeticSettings cosmeticSettings;
    @JsonProperty("testPlan")
    private TestPlan testPlan;
    @JsonProperty("customTest")
    private List<CustomTest> customTestList;
    @JsonProperty("eraseSettings")
    private EraseSettings eraseSettings;
    @JsonProperty("workflow")
    private WorkflowSettings workflow;
    @JsonProperty("createdAt")
    private String createdAt;
    @JsonProperty("updatedAt")
    private String updatedAt;
    @JsonProperty("advancedSettings")
    private AdvancedSettings advancedSettings;
    @JsonProperty("passwordOptions")
    private PasswordOptions passwordOptions;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RequiredFields {
        private boolean enabled;
        @JsonProperty("ESN")
        private boolean ESN;
        @JsonProperty("LPN")
        private boolean LPN;
        private boolean color;
        private boolean grade;
        private boolean custom;
        private boolean carrier;
        private boolean cosmetics;
        @JsonProperty("unlockStatus")
        private boolean unlockStatus;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RestrictiveActions {
        private boolean erase;
        private boolean print;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WifiSettings {
        private String name;
        private String password;
        private boolean disconnect;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BatterySettings {
        private boolean drain;
        @JsonProperty("appleFailBatteryWarningMessage")
        private boolean appleFail;
        @JsonProperty("drainAlways")
        private boolean drainAlways;
        @JsonProperty("drainDuration")
        private int drainDuration;
        @JsonProperty("healthDevices")
        private String[] healthDevices;
        @JsonProperty("healthCriteria")
        private HealthCriteria[] healthCriteria;
        @JsonProperty("customizeBattery")
        private boolean customizeBattery;
        @JsonProperty("healthDevicesModel")
        private String[] healthDevicesModel;
        @JsonProperty("failDrainIfDischarge")
        private boolean failDrainIfDischarge;
        @JsonProperty("allowDrainDurationChange")
        private boolean allowDrainDurationChange;
        @JsonProperty("drainTestAfterTestResults")
        private boolean drainTestAfterTestResults;
        @JsonProperty("healthDevicesSelectedModel")
        private String[] healthDevicesSelectedModel;
        @JsonProperty("failDrainIfDischargePercentage")
        private int failDrainIfDischargePercentage;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HealthCriteria {
        private String name;
        private Rule[] rules;
        private Device[] devices;
        private String description;
        @JsonProperty("specifyDevices")
        private boolean specifyDevices;
        @JsonProperty("applyAllDevices")
        private boolean applyAllDevices;
        @JsonProperty("applyAppleIphoneDevices")
        private boolean applyAppleIphoneDevices;
        @JsonProperty("applyAndroidDevices")
        private boolean applyAndroidDevices;
        @JsonProperty("applyAppleIpadDevices")
        private boolean applyAppleIpadDevices;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Device {
        @JsonProperty("make")
        private String make;
        @JsonProperty("modelName")
        private String modelName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Rule {
        private String code;
        @JsonProperty("maximumCycle")
        private Integer maximumCycle;
        @JsonProperty("minimumCapacity")
        private Integer minimumCapacity;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImeiCheck {
        private String name;
        @JsonProperty("automaticIMEICheck")
        private boolean automaticIMEICheck;
        @JsonProperty("usFinancialCarrierSetting")
        private boolean usFinancialCarrierSetting;
        private ImeiCheckConfiguration configuration;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImeiCheckConfiguration {
        @JsonProperty("appleIPadAction")
        private String appleIpadAction;
        @JsonProperty("appleIPhoneAction")
        private String appleIphoneAction;
        @JsonProperty("samsungAction")
        private String samsungAction;
        @JsonProperty("attAction")
        private String attAction;
        @JsonProperty("tmobileAction")
        private String tmobileAction;
        @JsonProperty("verizonAction")
        private String verizonAction;
        @JsonProperty("unlockedAction")
        private String unlockedAction;
        @JsonProperty("notSupportedOrUnknownCarrierAction")
        private String notSupportedOrUnknownCarrierAction;
        @JsonProperty("tracfoneAction")
        private String tracfoneAction;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CosmeticSettings {
        @JsonProperty("cosmetics")
        private String[] cosmetics;
        @JsonProperty("cosmeticType")
        private CosmeticType cosmeticType;
        @JsonProperty("saveCosmetics")
        private SaveCosmetics saveCosmetics;
        @JsonProperty("enableCosmetics")
        private boolean enableCosmetics;
        @JsonProperty("preferredFormat")
        private PreferredCosmeticFormat preferredFormat;
        @JsonProperty("cosmeticQuestions")
        private CosmeticQuestion[] cosmeticQuestions;

    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public enum SaveCosmetics {
        COSMETIC_ONLY("cosmeticOnly"),
        COSMETIC_AND_FAILED("cosmeticAndFailed"),
        COSMETIC_AND_PASSED("cosmeticAndPassed"),
        COSMETIC_AND_PASSED_OR_FAILED("cosmeticAndPassedOrFailed"),
        @JsonEnumDefaultValue
        DEFAULT_SAVE("");

        private final String value;

        SaveCosmetics(final String value) {
            this.value = value;
        }

        @JsonValue
        public final String getValue() {
            return this.value;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public enum CosmeticType {
        MULTI("multi"),
        QA("qa"),
        @JsonEnumDefaultValue
        DEFAULT_TYPE("");

        private final String value;

        CosmeticType(final String value) {
            this.value = value;
        }

        @JsonValue
        public final String getValue() {
            return this.value;
        }
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public enum PreferredCosmeticFormat {
        NAME("name"),
        NAME_AND_FAILED_RESPONSE("name-failed"),
        NAME_AND_PASSED_RESPONSE("name-passed"),
        COSMETIC_AND_BOTH_RESPONSE("name-both"),
        @JsonEnumDefaultValue
        DEFAULT_FORMAT("");

        private final String value;

        PreferredCosmeticFormat(final String value) {
            this.value = value;
        }

        @JsonValue
        public final String getValue() {
            return this.value;
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CosmeticQuestion {
        @JsonProperty("os")
        private CosmeticOS os;
        private String name;
        private String question;
        @JsonProperty("responses")
        private List<CosmeticResponse> responses;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public enum CosmeticOS {
        IOS("ios"),
        ANDROID("android"),
        BOTH("both"),
        @JsonEnumDefaultValue
        DEFAULT_OS("");

        private final String key;

        CosmeticOS(final String key) {
            this.key = key;
        }

        @JsonValue
        public final String getKey() {
            return this.key;
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CosmeticResponse {
        @JsonProperty("criteria")
        private boolean criteria;
        private String response;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PrinterSettings {
        @JsonProperty("onTestResults")
        private boolean onTestResults;
        @JsonProperty("onErasureSuccess")
        private boolean onErasureSuccess;
        @JsonProperty("powerOffDeviceAfterPrinter")
        private boolean powerOffDeviceAfterPrinter;
        @JsonProperty("onAppResultsFullyFunctional")
        private boolean onAppResultsFullyFunctional;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EraseSettings {
        @JsonProperty("iosMDM")
        private boolean iosMDM;
        private boolean automated;
        @JsonProperty("iosDoNotPEO")
        private boolean iosDoNotPEO;
        @JsonProperty("reconnectMessage")
        private boolean reconnectMessage;
        @JsonProperty("reconnectMinutes")
        private int reconnectMinutes;
        @JsonProperty("showEraseWarning")
        private boolean showEraseWarning;
        @JsonProperty("eraseAfterTestResult")
        private boolean eraseAfterTestResult;
        @JsonProperty("eraseAfterOnlySuccessful")
        private boolean eraseAfterOnlySuccessful;
        @JsonProperty("oneAutoErasePerTransactions")
        private boolean oneAutoErasePerTransactions;
        @JsonProperty("autoEraseEveryDeviceOnConnection")
        private boolean autoEraseEveryDeviceOnConnection;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TestPlan {
        @JsonProperty("apple")
        private List<DeviceTest> apple;
        @JsonProperty("android")
        private List<DeviceTest> android;
        @JsonProperty("both")
        private List<DeviceTest> both;
        @JsonProperty("planType")
        private String planType;
        @JsonProperty("watchTests")
        private List<DeviceTest> iWatchTests;
        @JsonProperty("watchPlanType")
        private String iWatchTestPlanType;
        @JsonProperty("airpodsTests")
        private List<DeviceTest> airpodsTests;
        @JsonProperty("airpodsPlanType")
        private String airpodsPlanType;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DeviceTest {
        private String id;
        private String key;
        private String platform;
        private String description;
        @JsonProperty("displayName")
        private String displayName;
        private String inputValue;
        @JsonProperty("requiresParameter")
        private int requiresParameter;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CustomTest {
        private int id;
        @JsonProperty("platform")
        private Platform platform;
        @JsonProperty("testName")
        private String testName;
        @JsonProperty("modelNumber")
        private String modelNumber;
        private String instructions;
        @JsonProperty("modifiedDate")
        private String modifiedDate;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public enum Platform {
        ANDROID("Android"),
        IOS("iOS"),
        @JsonEnumDefaultValue
        DEFAULT("");

        private final String key;

        Platform(final String key) {
            this.key = key;
        }

        @JsonValue
        public final String getKey() {
            return this.key;
        }

        public static String getReformKey(Platform platform) {
            if (Objects.equals(platform.key, IOS.getKey())) {
                return platform.key.toUpperCase();
            }
            return platform.key;
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Group {
        @JsonProperty("id")
        private String id;
        @JsonProperty("displayName")
        private String displayName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Value {
        @JsonProperty("key")
        private String key;
        @JsonProperty("value")
        private String value;
        @JsonProperty("isDefault")
        private int isDefault;
        @JsonProperty("displayValue")
        private String displayValue;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WorkflowSettings {
        private boolean enabled;
        @JsonProperty("checkIMEI")
        private boolean checkIMEI;
        @JsonProperty("separateFlow")
        private boolean separateFlow;
        @JsonProperty("connectionWorkflow")
        private List<AutomationSteps> connectionWorkflow;
        @JsonProperty("testResultWorkflow")
        private List<AutomationSteps> testResultWorkflow;
        @JsonProperty("failedWorkFlow")
        private List<AutomationSteps> failedWorkFlow;
        @JsonProperty("initialDefectWorkflowsActions")
        private List<AutomationSteps> initialDefectsWorkflow;
        @JsonProperty("initialDefectsWorkflow")
        private WorkflowInitialDefect initialDefect;
        @JsonProperty("manualEraseSelectedActions")
        private List<AutomationSteps> manualEraseWorkflow;
        @JsonProperty("manualRestoreSelectedActions")
        private List<AutomationSteps> manualRestoreWorkflow;
        @JsonProperty("manualPrintSelectedActions")
        private List<AutomationSteps> manualPrintWorkflow;
        @JsonProperty("manualPowerOffSelectedActions")
        private List<AutomationSteps> manualPowerOffWorkflow;
        @JsonProperty("connectionWorkflowEnabled")
        private boolean connectionWorkflowEnabled;
        @JsonProperty("testResultWorkflowEnabled")
        private boolean testResultWorkflowEnabled;
        @JsonProperty("customTriggerWorkflowEnabled")
        private boolean customTriggerWorkflowEnabled;
        @JsonProperty("initialDefectsWorkflowEnabled")
        private boolean initialDefectsWorkflowEnabled;
        @JsonProperty("warningMessagesEnabled")
        private boolean warningMessagesEnabled;
        @JsonProperty("warningMessages")
        private WarningMessage warningMessages;
        @JsonProperty("skipAllSetupAssistant")
        private boolean skipAllSetupAssistant;
        @JsonProperty("skipSetupAssistant")
        private Map<String, Boolean> skipSetupAssistant;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WarningMessage {
        @JsonProperty("sdCardIsDetected")
        private boolean isSdCardCheckEnabled;
        @JsonProperty("simCardIsDetected")
        private boolean isSimCardCheckEnabled;
        @JsonProperty("failSDCardNotRemoved")
        private boolean isFailSDCardNotRemoved;
        @JsonProperty("failSimCardNotRemoved")
        private boolean isFailSimCardNotRemoved;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public enum AutomationSteps {
        PRINT("print"),
        ERASE("erase"),
        POWER_OFF("off"),
        SETUP_INSTALL_APP("install"),
        SETUP("setup"),
        UNINSTALL_APP("remove"),
        SCAN_CUSTOM1("scanCustom1"),
        SCAN_LPN("scanLPN"),
        RESTORE("restore"),
        RESTORE_JAIL_BROKEN("restoreJailbroken"),
        RESTORE_IOS_BETA("restoreIOSBeta"),
        DATA_VERIFICATION("dataVerification"),
        ANDROID_UNLOCK_WITH_CODE("androidUnlockWithCode"),
        ERASE_FOR_MDM("eraseiOSMDM"),
        @JsonEnumDefaultValue
        DEFAULT_STEP("");

        private final String key;

        AutomationSteps(final String key) {
            this.key = key;
        }

        @JsonValue
        public final String getKey() {
            return this.key;
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WorkflowInitialDefect {
        @JsonProperty("mdm")
        private boolean mdm;
        @JsonProperty("noEID")
        private boolean noEid;
        @JsonProperty("betaOS")
        private boolean betaOs;
        @JsonProperty("noIMEI")
        private boolean noImei;
        @JsonProperty("samsungKnox")
        private boolean samsungKnox;
        @JsonProperty("batteryHealth")
        private boolean batteryHealth;
        @JsonProperty("IMEISerialValidation")
        private boolean ImeiSerialValidation;
        @JsonProperty("appleIcloudGoogleLock")
        private boolean appleIcloudGoogleLock;

    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AdvancedSettings {
        @JsonProperty("apiDataSharingEnabled")
        private boolean apiDataSharingEnabled;
        @JsonProperty("sourceEnabled")
        private boolean sourceApiEnabled;
        @JsonProperty("sourceURL")
        private String sourceURL;
        @JsonProperty("sourceAPIKey")
        private String sourceAPIKey;
        @JsonProperty("resultsEnabled")
        private boolean resultsEnabled;
        @JsonProperty("resultsURL")
        private String resultsURL;
        @JsonProperty("resultsAPIKey")
        private String resultsAPIKey;
        @JsonProperty("labelEnabled")
        private boolean labelEnabled;
        @JsonProperty("labelURL")
        private String labelURL;
        @JsonProperty("labelAPIKey")
        private String labelAPIKey;
        @JsonProperty("shopfloorEnabled")
        private boolean shopfloorEnabled;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Fields {
        @JsonProperty("allowUserModifyColorField")
        private boolean allowUserModifyColorField;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PasswordOptions {
        @JsonProperty("editTransaction")
        private boolean editTransaction;
        @JsonProperty("newTransaction")
        private boolean newTransaction;
        @JsonProperty("updateVendorInfo")
        private boolean updateVendorInfo;
    }
}
