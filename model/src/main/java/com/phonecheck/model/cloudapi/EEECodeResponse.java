package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EEECodeResponse {
    @JsonProperty("OEM PARTS")
    private List<OemPart> oemParts;

    @Data
    public static class OemPart {
        @JsonProperty("Part Name")
        private String partName;
        @JsonProperty("Devices")
        private List<EEECodeResponse.Device> devices;
    }

    @Data
    public static class Device {
        @JsonProperty("Name")
        private String name;
        @JsonProperty("EEE")
        private String eee;
        @JsonProperty("Exception_keys")
        private List<String> exceptionKeys;
    }
}


