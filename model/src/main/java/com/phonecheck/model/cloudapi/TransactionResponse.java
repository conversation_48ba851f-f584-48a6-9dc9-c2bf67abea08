package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionResponse {
    @JsonProperty("transactionRecordsResponse")
    private TransactionRecordResponse[] transactionRecords;

    @Getter
    @Setter
    public static class TransactionRecordResponse {
        @JsonProperty("InfoID")
        private String infoId;
        @JsonProperty("TransactionID")
        private String transactionId;
        @JsonProperty("master_id")
        private String masterId;
        @JsonProperty("WareHouse")
        private String wareHouse;
        @JsonProperty("MasterName")
        private String masterName;
        @JsonProperty("Model")
        private String model;
        @JsonProperty("Memory")
        private String memory;
        @JsonProperty("IMEI")
        private String imei;
        @JsonProperty("Carrier")
        private String carrier;
        @JsonProperty("PCCarrier")
        private String pcCarrier;
        @JsonProperty("Serial")
        private String serial;
        @JsonProperty("UDID")
        private String udid;
        @JsonProperty("LicenseIdentifier")
        private String licenseIdentifier;
        @JsonProperty("DeviceLock")
        private String deviceLock;
        @JsonProperty("AppleID")
        private String appleId;
        @JsonProperty("Rooted")
        private String rooted;
        @JsonProperty("Color")
        private String color;
        @JsonProperty("color_code")
        private String colorCode;
        @JsonProperty("Grade")
        private String grade;
        @JsonProperty("Version")
        private String version;
        @JsonProperty("OS")
        private String os;
        @JsonProperty("Make")
        private String make;
        @JsonProperty("Firmware")
        private String firmware;
        @JsonProperty("Notes")
        private String notes;
        @JsonProperty("ESN")
        private String esn;
        @JsonProperty("LicenseID")
        private String licenseId;
        @JsonProperty("DeviceCreatedDate")
        private String deviceCreatedDate;
        @JsonProperty("DeviceUpdatedDate")
        private String deviceUpdatedDate;
        @JsonProperty("BatteryPercentage")
        private String batteryPercentage;
        @JsonProperty("BatteryCycle")
        private String batteryCycle;
        @JsonProperty("BatteryHealthPercentage")
        private String batteryHealthPercentage;
        @JsonProperty("BatteryDesignMaxCapacity")
        private String batteryDesignMaxCapacity;
        @JsonProperty("BatteryCurrentMaxCapacity")
        private String batteryCurrentMaxCapacity;
        @JsonProperty("BatterySerial")
        private String batterySerial;
        @JsonProperty("BatteryModel")
        private String batteryModel;
        @JsonProperty("BatterySource")
        private String batterySource;
        @JsonProperty("ModelNo")
        private String modelNo;
        @JsonProperty("UnlockStatus")
        private String unlockStatus;
        @JsonProperty("TesterName")
        private String testerName;
        @JsonProperty("Cosmetics")
        private String cosmetics;
        @JsonProperty("BuildNo")
        private String buildNo;
        @JsonProperty("AppVersion")
        private String appVersion;
        @JsonProperty("ManualEntry")
        private String manualEntry;
        @JsonProperty("ESNResponse")
        private String esnResponse;
        @JsonProperty("SimLockResponse")
        private String simLockResponse;
        @JsonProperty("LPN")
        private String lpn;
        @JsonProperty("Custom1")
        private String custom1;
        @JsonProperty("SKUCode")
        private String skuCode;
        @JsonProperty("Platform")
        private String platform;
        @JsonProperty("RegulatoryModelNumber")
        private String regulatoryModelNumber;
        @JsonProperty("CosmeticsFailed")
        private String cosmeticsFailed;
        @JsonProperty("CosmeticsPassed")
        private String cosmeticsPassed;
        @JsonProperty("CosmeticsPending")
        private String cosmeticsPending;
        @JsonProperty("CosmeticsWorking")
        private String cosmeticsWorking;
        @JsonProperty("Network")
        private String network;
        @JsonProperty("Network1")
        private String network1;
        @JsonProperty("Network2")
        private String network2;
        @JsonProperty("SIM1MCC")
        private String sim1MCC;
        @JsonProperty("SIM1MNC")
        private String sim1MNC;
        @JsonProperty("SIM2MCC")
        private String sim2MCC;
        @JsonProperty("SIM2MNC")
        private String sim2MNC;
        @JsonProperty("SIM1Name")
        private String sim1Name;
        @JsonProperty("SIM2Name")
        private String sim2Name;
        @JsonProperty("IMEI2")
        private String imei2;
        @JsonProperty("SimTechnology")
        private String simTechnology;
        @JsonProperty("BatteryTemperature")
        private String batteryTemperature;
        @JsonProperty("AvgBatteryTemperature")
        private String avgBatteryTemperature;
        @JsonProperty("MaxBatteryTemperature")
        private String maxBatteryTemperature;
        @JsonProperty("MinBatteryTemperature")
        private String minBatteryTemperature;
        @JsonProperty("BatteryResistance")
        private String batteryResistance;
        @JsonProperty("MEID")
        private String meid;
        @JsonProperty("MEID2")
        private String meid2;
        @JsonProperty("PESN")
        private String pEsn;
        @JsonProperty("PESN2")
        private String pEsn2;
        @JsonProperty("SIMSERIAL")
        private String simSerial;
        @JsonProperty("SIMSERIAL2")
        private String simSerial2;
        @JsonProperty("DecimalMEID")
        private String decimalMeid;
        @JsonProperty("DecimalMEID2")
        private String decimalMeid2;
        @JsonProperty("SimErased")
        private String simErased;
        @JsonProperty("MDM")
        private String mdm;
        @JsonProperty("ResolvedMake")
        private String resolvedMake;
        @JsonProperty("ResolvedModelNo")
        private String resolvedModelNo;
        @JsonProperty("ResolvedModel")
        private String resolvedModel;
        @JsonProperty("ResolvedCarrier")
        private String resolvedCarrier;
        @JsonProperty("ResolvedMemory")
        private String resolvedMemory;
        @JsonProperty("ResolvedColor")
        private String resolvedColor;
        @JsonProperty("CountryOfOrigin")
        private String countryOfOrigin;
        @JsonProperty("BatteryDrainDuration")
        private String batteryDrainDuration;
        @JsonProperty("BatteryChargeStart")
        private String batteryChargeStart;
        @JsonProperty("BatterChargeEnd")
        private String batteryChargeEnd;
        @JsonProperty("BatteryDrain")
        private String batteryDrain;
        @JsonProperty("WifiMacAddress")
        private String wifiMacAddress;
        @JsonProperty("SimHistory")
        private String simHistory;
        @JsonProperty("iCloudInfo")
        private String iCloudInfo;
        @JsonProperty("BatteryDrainInfo")
        private String batteryDrainInfo;
        @JsonProperty("CompatibleSim")
        private String compatibleSim;
        @JsonProperty("NotCompatibleSim")
        private String notCompatibleSim;
        @JsonProperty("DeviceState")
        private String deviceState;
        @JsonProperty("BatteryDrainType")
        private String batteryDrainType;
        @JsonProperty("CocoCurrentCapacity")
        private String cocoCurrentCapacity;
        @JsonProperty("CocoDesignCapacity")
        private String cocoDesignCapacity;
        @JsonProperty("OEMBatteryHealth")
        private String oemBatteryHealth;
        @JsonProperty("CocoBatteryHealth")
        private String cocoBatteryHealth;
        @JsonProperty("PortNumber")
        private String portNumber;
        @JsonProperty("startHeat")
        private String startHeat;
        @JsonProperty("endHeat")
        private String endHeat;
        @JsonProperty("ProductCode")
        private String productCode;
        @JsonProperty("device_shutdown")
        private String deviceShutdown;
        @JsonProperty("SimLock")
        private String simLock;
        @JsonProperty("BMic")
        private String bMic;
        @JsonProperty("VMic")
        private String vMic;
        @JsonProperty("FMic")
        private String fMic;
        @JsonProperty("DefectsCode")
        private String defectsCode;
        @JsonProperty("ManualFailure")
        private String manualFailure;
        @JsonProperty("ErrorCode")
        private String errorCode;
        @JsonProperty("ScreenTime")
        private String screenTime;
        @JsonProperty("testerDeviceTime")
        private String testerDeviceTime;
        @JsonProperty("gradePerformed")
        private String gradePerformed;
        @JsonProperty("isLabelPrint")
        private String isLabelPrint;
        @JsonProperty("Knox")
        private String knox;
        @JsonProperty("GUID")
        private String guid;
        @JsonProperty("Erased")
        private String erased;
        @JsonProperty("Type")
        private String type;
        @JsonProperty("StartTime")
        private String startTime;
        @JsonProperty("EndTime")
        private String endTime;
        @JsonProperty("RestoreCode")
        private String restoreCode;
        @JsonProperty("erasedSD")
        private String erasedSD;
        @JsonProperty("erasedNotes")
        private String erasedNotes;
        @JsonProperty("EraserDiff")
        private String eraserDiff;
        @JsonProperty("id")
        private String id;
        @JsonProperty("Working")
        private String working;
        @JsonProperty("Passed")
        private String passed;
        @JsonProperty("Failed")
        private String failed;
        @JsonProperty("Pending")
        private String pending;
        @JsonProperty("Pre_check_working")
        private String preCheckWorking;
        @JsonProperty("Pre_check_Passed")
        private String preCheckPassed;
        @JsonProperty("Pre_check_Failed")
        private String preCheckFailed;
        @JsonProperty("Pre_check_Pending")
        private String preCheckPending;
        @JsonProperty("VendorName")
        private String vendorName;
        @JsonProperty("InvoiceNo")
        private String invoiceNo;
        @JsonProperty("TransactionDate")
        private String transactionDate;
        @JsonProperty("isCloudTransaction")
        private String isCloudTransaction;
        @JsonProperty("StationID")
        private String stationId;
        @JsonProperty("BoxNo")
        private String boxNo;
        @JsonProperty("QTY")
        private String qty;
        @JsonProperty("EraseStartTime")
        private String eraseStartTime;
        @JsonProperty("EraseEndTime")
        private String eraseEndTime;
        @JsonProperty("EraseType")
        private String eraseType;
        @JsonProperty("Model#")
        private String modelNumber;
        @JsonProperty("BatteryShutdown")
        private String batteryShutdown;
        @JsonProperty("TestPlanName")
        private String testPlanName;
        @JsonProperty("CarrierLockResponse")
        private String carrierLockResponse;
        @JsonProperty("Parts")
        private Object parts;
        @JsonProperty("Ram")
        private String ram;
        @JsonProperty("IFT_Codes")
        private String iftCodes;
        @JsonProperty("GradingResults")
        private String gradingResults;
        @JsonProperty("transaction_type")
        private String transactionType;
        @JsonProperty("final_price")
        private String finalPrice;
        @JsonProperty("MDMResponse")
        private String mdmResponse;
        @JsonProperty("BatteryHealthGrade")
        private String batteryHealthGrade;
        @JsonProperty("EID")
        private String eid;
        @JsonProperty("esim_present")
        private String eSimPresent;
        @JsonProperty("esim_erased")
        private String eSimErased;
        @JsonProperty("eBayRefurbished")
        private String eBayRefurbished;
        @JsonProperty("eBayRejection")
        private String eBayRejection;
        @JsonProperty("amazonRenewed")
        private String amazonRenewed;
        @JsonProperty("amazonRenewedRejection")
        private String amazonRenewedRejection;
        @JsonProperty("androidCarrierId")
        private String androidCarrierId;
        @JsonProperty("swappaQualified")
        private String swappaQualified;
        @JsonProperty("swappaRejection")
        private String swappaRejection;
        @JsonProperty("backMarketQualified")
        private String backMarketQualified;
        @JsonProperty("backMarketRejection")
        private String backMarketRejection;
        @JsonProperty("isMobileCosmetics")
        private String isMobileCosmetics;
        @JsonProperty("data_verification")
        private String dataVerification;
        @JsonProperty("start_battery_charge")
        private String startBatteryCharge;
        @JsonProperty("end_battery_charge")
        private String endBatteryCharge;
        @JsonProperty("total_battery_drain")
        private String totalBatteryDrain;
        @JsonProperty("warranty")
        private String warranty;
    }

    @Data
    public static class Parts {
        @JsonProperty("Remarks")
        private String remarks;
        @JsonProperty("Data")
        private PartsData[] data;
    }

    @Data
    public static class PartsData {
        @JsonProperty("Status")
        private String status;
        private String name;
        @JsonProperty("FactorySerial")
        private String factorySerial;
        private String checkSum;
        private String currentCheckSum;
        private String notice;
        @JsonProperty("CurrentSerial")
        private String currentSerial;
    }
}