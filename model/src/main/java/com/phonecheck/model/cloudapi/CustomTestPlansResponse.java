package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomTestPlansResponse {
    @JsonProperty("ctp_id")
    private String ctpId;
    @JsonProperty("ctp_title")
    private String ctpTitle;
    @JsonProperty("ctp_platform")
    private String ctpPlatform;
    @JsonProperty("ctp_model_no")
    private String ctpModelNo;
    @JsonProperty("ctp_description")
    private String ctpDescription;
    @JsonProperty("ctp_added_on")
    private String ctpAddedOn;
    @JsonProperty("ctp_status")
    private String ctpStatus;
}
