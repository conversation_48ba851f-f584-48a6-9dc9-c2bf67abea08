package com.phonecheck.model.cloudapi;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SyncedCloudDevice {
    @JsonProperty("erasedNotes")
    private String erasedNotes;
    @JsonProperty("GUID")
    private String guid;
    @JsonProperty("InfoID")
    private String infoId;
    // error response is a field we will populate if the cloud call fails due to cloud device not being present
    private String errorResponse;
}
