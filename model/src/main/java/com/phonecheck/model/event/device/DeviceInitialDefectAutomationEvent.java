package com.phonecheck.model.event.device;

import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractAutomationEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Event to trigger initial defects automated steps on the device, like print erase and power off
 */
@Getter
@Setter
public class DeviceInitialDefectAutomationEvent extends AbstractAutomationEvent {
    public DeviceInitialDefectAutomationEvent(final Object source,
                                              final Device device) {
        super(source, device, AutomationWorkflow.INITIAL_DEFECTS);
    }

    @Override
    public String toString() {
        return "DeviceInitialDefectAutomationEvent{" +
                "automationWorkflow=" + automationWorkflow +
                ", source=" + source.getClass() +
                '}';
    }
}
