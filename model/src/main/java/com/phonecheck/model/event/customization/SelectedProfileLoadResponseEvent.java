package com.phonecheck.model.event.customization;

import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.event.AbstractEvent;
import com.phonecheck.model.phonecheckapi.LabelFxmlResponse;
import lombok.Getter;

import java.util.Map;

/**
 * Event that stores a cloud customization response
 */
@Getter
public class SelectedProfileLoadResponseEvent extends AbstractEvent {
    private final CloudCustomizationResponse customizationResponse;
    private final Map<String, LabelFxmlResponse.Data> labelDataMap;


    public SelectedProfileLoadResponseEvent(final Object source,
                                            final CloudCustomizationResponse customizationResponse,
                                            final Map<String, LabelFxmlResponse.Data> labelDataMap) {
        super(source);
        this.customizationResponse = customizationResponse;
        this.labelDataMap = labelDataMap;
    }
}
