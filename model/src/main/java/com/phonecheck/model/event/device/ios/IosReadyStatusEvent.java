package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Raised when iphone is ready to test
 */
@Getter
public class IosReadyStatusEvent extends AbstractDeviceEvent {

    public IosReadyStatusEvent(final Object source,
                               final IosDevice device) {
        super(source, device);
    }
}
