package com.phonecheck.model.event.transaction;

import com.phonecheck.model.event.AbstractEvent;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.transaction.TransactionChangeRecord;
import lombok.Getter;

import java.util.List;


@Getter
public class TransactionChangeEvent extends AbstractEvent {
    private final List<TransactionChangeRecord> selectedTransactions;
    private final Transaction newTransaction;
    public TransactionChangeEvent(final Object source,
                                  final List<TransactionChangeRecord> selectedTransactions,
                                  final Transaction newTransaction) {
        super(source);
        this.selectedTransactions = selectedTransactions;
        this.newTransaction = newTransaction;
    }
}
