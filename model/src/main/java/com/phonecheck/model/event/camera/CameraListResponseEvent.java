package com.phonecheck.model.event.camera;

import com.phonecheck.model.event.AbstractEvent;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Event that stores connected camera names list
 */
@Getter
@Setter
public class CameraListResponseEvent extends AbstractEvent {
    private List<String> cameraList;

    public CameraListResponseEvent(final Object source, final List<String> cameraList) {
        super(source);
        this.cameraList = cameraList;
    }
}
