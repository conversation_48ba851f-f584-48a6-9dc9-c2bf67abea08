package com.phonecheck.model.event.device.ios;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

@Getter
public class IosActivationNeededEvent extends AbstractDeviceEvent {
    private final boolean shouldInstallApp;

    public IosActivationNeededEvent(final Object source, final IosDevice device,
                                    final boolean shouldInstallApp) {
        super(source, device);
        this.shouldInstallApp = shouldInstallApp;
    }
}
