package com.phonecheck.model.event.port;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Raised when device connected for port mapping
 */
@Getter
@Setter
public class PortMapDeviceConnectionEvent extends AbstractDeviceEvent {
    private int portNumber;

    public PortMapDeviceConnectionEvent(final Object source) {
        super(source, null);
    }

    public PortMapDeviceConnectionEvent(final Object source, final Device device) {
        super(source, device);
    }
}

