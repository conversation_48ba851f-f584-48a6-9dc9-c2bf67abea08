package com.phonecheck.model.event.device.iwatch;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Raised when an iWatch unpair successfully
 */
@Getter
public class IWatchUnpairEvent extends AbstractDeviceEvent {
    public IWatchUnpairEvent(final Object source, final IosDevice device) {
        super(source, device);
    }
}
