package com.phonecheck.model.event.device.android;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class AndroidPushFilesStatusEvent extends AbstractDeviceEvent {
    private final List<String> successfullyPushedFiles;
    private final List<String> failedPushFiles;

    public AndroidPushFilesStatusEvent(final Object source, final AndroidDevice device,
                                       final List<String> successfullyPushedFiles,
                                       final List<String> failedPushFiles) {
        super(source, device);
        this.successfullyPushedFiles = successfullyPushedFiles;
        this.failedPushFiles = failedPushFiles;
    }
}
