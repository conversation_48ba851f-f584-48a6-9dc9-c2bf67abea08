package com.phonecheck.model.event.device.ios.precheck;

import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.PreCheckInfo;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;

/**
 * Event when preCheck semi results retrieved
 */
@Getter
public class PreCheckSemiResultsSuccessEvent extends AbstractDeviceEvent {
    private PreCheckInfo.SemiAutoResults semiAutoResults;
    public PreCheckSemiResultsSuccessEvent(final Object source, final IosDevice device,
                                           final PreCheckInfo.SemiAutoResults semiAutoResults) {
        super(source, device);
        this.semiAutoResults = semiAutoResults;
    }
}
