package com.phonecheck.model.event.device;

import com.phonecheck.model.device.Device;
import com.phonecheck.model.event.AbstractDeviceEvent;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DeviceSimCardWarningEvent extends AbstractDeviceEvent {
    private boolean showSimCardDefect;
    private boolean eraseRequired;

    public DeviceSimCardWarningEvent(final Object source,
                                     final Device device,
                                     final boolean showSimCardDefect,
                                     final boolean eraseRequired
    ) {
        super(source, device);
        this.showSimCardDefect = showSimCardDefect;
        this.eraseRequired = eraseRequired;
    }
}
