package com.phonecheck.model.event.port;

import com.phonecheck.model.event.AbstractEvent;
import lombok.Getter;
import lombok.Setter;

/**
 * Event that contains port map type response
 */
@Getter
@Setter
public class PortMapTypeResponseEvent extends AbstractEvent {
    private boolean isThunderSync;

    public PortMapTypeResponseEvent(final Object source, final boolean isThunderSync) {
        super(source);
        this.isThunderSync = isThunderSync;
    }
}

