package com.phonecheck.model.test.precheck;

import java.util.HashMap;
import java.util.Map;

import static com.phonecheck.model.precheck.PreCheckTestConstants.*;

public enum PreCheckInfoTestKey {
    LCD_COLOR_TEST("lcdColor", P_LCD_COLOR),
    DIGITIZER_TEST("digitizerTest", P_DIGITIZER),
    SPEAKER_TEST("speaker", P_SPEAKER);

    private static final Map<String, String> lookup = new HashMap<>();

    static {
        for (PreCheckInfoTestKey test : PreCheckInfoTestKey.values()) {
            lookup.put(test.key, test.value);
        }
    }

    private final String key;
    private final String value;

    PreCheckInfoTestKey(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValue(String key) {
        return lookup.getOrDefault(key, null);
    }
}