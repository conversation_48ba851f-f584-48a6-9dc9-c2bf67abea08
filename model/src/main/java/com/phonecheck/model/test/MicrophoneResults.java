package com.phonecheck.model.test;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode
public class MicrophoneResults {
    @JsonProperty("RMAmplitude")
    private double rmAmplitude;
    @JsonProperty("FMAmplitude")
    private double fmAmplitude;
    @JsonProperty("BMAmplitude")
    private double bmAmplitude;
    // TODO: Below four attributes are only for android, verify with mobile team
    @JsonProperty("Microphone")
    private String microphone;
    @JsonProperty("Video Microphone")
    private String videoMicrophone;
    @JsonProperty("Mic ES")
    private String micES;
    @JsonProperty("Vid Mic ES")
    private String vidMicES;
}
