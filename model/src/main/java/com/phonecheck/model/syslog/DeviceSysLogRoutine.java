package com.phonecheck.model.syslog;

import lombok.Getter;
import lombok.Setter;

/**
 * A routine(process) class to hold the device Syslog process during its processing.
 * It contains thread on which the Syslog is being executed and the syslog process
 * itself. With this we can check if the syslog process for target device is already
 * alive or not and if it is on device disconnection we will destroy the process to
 * release computer resources.
 */
@Getter
@Setter
public class DeviceSysLogRoutine {
    private Thread sysLogTask;
    private Thread traceUtilTask;
    private Process sysLogProcess;
    private Process traceUtilProcess;
    private boolean sysLogRunning;
    private boolean keepObserving;
    private boolean traceUtilRunning;
//    private SysLogFileInfo sysLogFileInfo;
}
