package com.phonecheck.model.autoexport;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@XmlRootElement(name = "root")
@XmlAccessorType(XmlAccessType.FIELD)
public class AutoExportData {
    @JsonProperty("FMic")
    @XmlElement(name = "FMic")
    private String fMic;

    @JsonProperty("RegulatoryModelNumber")
    @XmlElement(name = "RegulatoryModelNumber")
    private String regulatoryModelNumber;

    @JsonProperty("Platform")
    @XmlElement(name = "Platform")
    private String platform;

    @JsonProperty("Memory")
    @XmlElement(name = "Memory")
    private String memory;

    @JsonProperty("EraseType")
    @XmlElement(name = "EraseType")
    private String eraseType;

    @JsonProperty("SIM2MNC")
    @XmlElement(name = "SIM2MNC")
    private String sim2Mnc;

    @JsonProperty("TransactionID")
    @XmlElement(name = "TransactionID")
    private int transactionId;

    @JsonProperty("PCCarrier")
    @XmlElement(name = "PCCarrier")
    private String pcCarrier;

    @JsonProperty("Network")
    @XmlElement(name = "Network")
    private String network;

    @JsonProperty("BatteryDrainDuration")
    @XmlElement(name = "BatteryDrainDuration")
    private String batteryDrainDuration;

    @JsonProperty("BatteryChargeStart")
    @XmlElement(name = "BatteryChargeStart")
    private String batteryChargeStart;

    @JsonProperty("BatteryDrainInfo")
    @XmlElement(name = "BatteryDrainInfo")
    private String batteryDrainInfo;

    @JsonProperty("BoxNo")
    @XmlElement(name = "BoxNo")
    private String boxNo;

    @JsonProperty("Firmware")
    @XmlElement(name = "Firmware")
    private String firmware;

    @JsonProperty("Rooted")
    @XmlElement(name = "Rooted")
    private String rooted;

    @JsonProperty("UnlockStatus")
    @XmlElement(name = "UnlockStatus")
    private String unlockStatus;

    @JsonProperty("WareHouse")
    @XmlElement(name = "WareHouse")
    private String wareHouse;

    @JsonProperty("ESNResponse")
    @XmlElement(name = "ESNResponse")
    private String esnResponse;

    @JsonProperty("EID")
    @XmlElement(name = "EID")
    private String eId;

    @JsonProperty("VendorName")
    @XmlElement(name = "VendorName")
    private String vendorName;

    @JsonProperty("AppleID")
    @XmlElement(name = "AppleID")
    private String appleId;

    @JsonProperty("ProductCode")
    @XmlElement(name = "ProductCode")
    private String productCode;

    @JsonProperty("WifiMacAddress")
    @XmlElement(name = "WifiMacAddress")
    private String wifiMacAddress;

    @JsonProperty("Make")
    @XmlElement(name = "Make")
    private String make;

    @JsonProperty("CosmeticsPending")
    @XmlElement(name = "CosmeticsPending")
    private String cosmeticsPending;

    @JsonProperty("ManualFailure")
    @XmlElement(name = "ManualFailure")
    private String manualFailure;

    @JsonProperty("SIMSERIAL2")
    @XmlElement(name = "SIMSERIAL2")
    private String simSerial2;

    @JsonProperty("device_shutdown")
    @XmlElement(name = "device_shutdown")
    private String fccId;

    @JsonProperty("Working")
    @XmlElement(name = "Working")
    private String working;

    @JsonProperty("PESN")
    @XmlElement(name = "PESN")
    private String pesn;

    @JsonProperty("VMic")
    @XmlElement(name = "VMic")
    private String vMic;

    @JsonProperty("BatteryModel")
    @XmlElement(name = "BatteryModel")
    private String batteryModel;

    @JsonProperty("iCloudInfo")
    @XmlElement(name = "iCloudInfo")
    private String iCloudInfo;

    @JsonProperty("BatterySerial")
    @XmlElement(name = "BatterySerial")
    private String batterySerial;

    @JsonProperty("IMEI2")
    @XmlElement(name = "IMEI2")
    private String imei2;

    @JsonProperty("endHeat")
    @XmlElement(name = "endHeat")
    private String endHeat;

    @JsonProperty("DecimalMEID")
    @XmlElement(name = "DecimalMEID")
    private String decimalMeId;

    @JsonProperty("LPN")
    @XmlElement(name = "LPN")
    private String lpn;

    @JsonProperty("DefectsCode")
    @XmlElement(name = "DefectsCode")
    private String defectsCode;

    @JsonProperty("CarrierLockResponse")
    @XmlElement(name = "CarrierLockResponse")
    private String carrierLockResponse;

    @JsonProperty("StationID")
    @XmlElement(name = "StationID")
    private String stationId;

    @JsonProperty("SIMSERIAL")
    @XmlElement(name = "SIMSERIAL")
    private String simSerial;

    @JsonProperty("SimTechnology")
    @XmlElement(name = "SimTechnology")
    private String simTechnology;

    @JsonProperty("BuildNo")
    @XmlElement(name = "BuildNo")
    private String buildNo;

    @JsonProperty("SIM1MCC")
    @XmlElement(name = "SIM1MCC")
    private String sim1Mcc;

    @JsonProperty("ManualEntry")
    @XmlElement(name = "ManualEntry")
    private String manualEntry;

    @JsonProperty("OEMBatteryHealth")
    @XmlElement(name = "OEMBatteryHealth")
    private String oemBatteryHealth;

    @JsonProperty("TesterName")
    @XmlElement(name = "TesterName")
    private String testerName;

    @JsonProperty("TransactionDate")
    @XmlElement(name = "TransactionDate")
    private String transactionDate;

    @JsonProperty("ESN")
    @XmlElement(name = "ESN")
    private String esn;

    @JsonProperty("startHeat")
    @XmlElement(name = "startHeat")
    private String startHeat;

    @JsonProperty("esim_present")
    @XmlElement(name = "esim_present")
    private String eSimPresent;

    @JsonProperty("BatterySource")
    @XmlElement(name = "BatterySource")
    private String batterySource;

    @JsonProperty("InvoiceNo")
    @XmlElement(name = "InvoiceNo")
    private String invoiceNo;

    @JsonProperty("esim_erased")
    @XmlElement(name = "esim_erased")
    private String eSimErased;

    @JsonProperty("EraseEndTime")
    @XmlElement(name = "EraseEndTime")
    private String eraseEndTime;

    @JsonProperty("Serial")
    @XmlElement(name = "Serial")
    private String serial;

    @JsonProperty("DeviceState")
    @XmlElement(name = "DeviceState")
    private String deviceState;

    @JsonProperty("SimLock")
    @XmlElement(name = "SimLock")
    private String simLock;

    @JsonProperty("SIM2Name")
    @XmlElement(name = "SIM2Name")
    private String sim2Name;

    @JsonProperty("RestoreCode")
    @XmlElement(name = "RestoreCode")
    private String restoreCode;

    @JsonProperty("Model")
    @XmlElement(name = "Model")
    private String model;

    @JsonProperty("Passed")
    @XmlElement(name = "Passed")
    private String passed;

    @JsonProperty("MEID2")
    @XmlElement(name = "MEID2")
    private String meId2;

    @JsonProperty("BatteryDrain")
    @XmlElement(name = "BatteryDrain")
    private String batteryDrain;

    @JsonProperty("BatteryCurrentMaxCapacity")
    @XmlElement(name = "BatteryCurrentMaxCapacity")
    private String batteryCurrentMaxCapacity;

    @JsonProperty("MDMResponse")
    @XmlElement(name = "MDMResponse")
    private String mdmResponse;

    @JsonProperty("BatteryCycle")
    @XmlElement(name = "BatteryCycle")
    private String batteryCycle;

    @JsonProperty("CountryOfOrigin")
    @XmlElement(name = "CountryOfOrigin")
    private String countryOfOrigin;

    @JsonProperty("ScreenTime")
    @XmlElement(name = "ScreenTime")
    private String screenTime;

    @JsonProperty("ModelNo")
    @XmlElement(name = "ModelNo")
    private String modelNumber;

    @JsonProperty("Knox")
    @XmlElement(name = "Knox")
    private String knox;

    @JsonProperty("Version")
    @XmlElement(name = "Version")
    private String osVersion;

    @JsonProperty("MEID")
    @XmlElement(name = "MEID")
    private String meId;

    @JsonProperty("QTY")
    @XmlElement(name = "QTY")
    private String qty;

    @JsonProperty("MDM")
    @XmlElement(name = "MDM")
    private String mdm;

    @JsonProperty("CocoCurrentCapacity")
    @XmlElement(name = "CocoCurrentCapacity")
    private String cocoCurrentCapacity;

    @JsonProperty("Carrier")
    @XmlElement(name = "Carrier")
    private String carrier;

    @JsonProperty("TestPlanName")
    @XmlElement(name = "TestPlanName")
    private String testPlanName;

    @JsonProperty("DeviceLock")
    @XmlElement(name = "DeviceLock")
    private String deviceLock;

    @JsonProperty("BatteryTemperature")
    @XmlElement(name = "BatteryTemperature")
    private String batteryTemperature;

    @JsonProperty("SIM1MNC")
    @XmlElement(name = "SIM1MNC")
    private String sim1mnc;

    @JsonProperty("PESN2")
    @XmlElement(name = "PESN2")
    private String pesn2;

    @JsonProperty("BatteryPercentage")
    @XmlElement(name = "BatteryPercentage")
    private String batteryPercentage;

    @JsonProperty("Failed")
    @XmlElement(name = "Failed")
    private String failed;

    @JsonProperty("Color")
    @XmlElement(name = "Color")
    private String color;

    @JsonProperty("Grade")
    @XmlElement(name = "Grade")
    private String grade;

    @JsonProperty("Erased")
    @XmlElement(name = "Erased")
    private String erased;

    @JsonProperty("LicenseID")
    @XmlElement(name = "LicenseID")
    private String licenseId;

    @JsonProperty("BatterChargeEnd")
    @XmlElement(name = "BatterChargeEnd")
    private String batterChargeEnd;

    @JsonProperty("Custom1")
    @XmlElement(name = "Custom1")
    private String custom1;

    @JsonProperty("BatteryDrainType")
    @XmlElement(name = "BatteryDrainType")
    private String batteryDrainType;

    @JsonProperty("SIM2MCC")
    @XmlElement(name = "SIM2MCC")
    private String sim2Mcc;

    @JsonProperty("BMic")
    @XmlElement(name = "BMic")
    private String bMic;

    @JsonProperty("BatteryDesignMaxCapacity")
    @XmlElement(name = "BatteryDesignMaxCapacity")
    private String batteryDesignMaxCapacity;

    @JsonProperty("PortNumber")
    @XmlElement(name = "PortNumber")
    private String portNumber;

    @JsonProperty("SimLockResponse")
    @XmlElement(name = "SimLockResponse")
    private String simLockResponse;

    @JsonProperty("Network2")
    @XmlElement(name = "Network2")
    private String network2;

    @JsonProperty("EraseStartTime")
    @XmlElement(name = "EraseStartTime")
    private String eraseStartTime;

    @JsonProperty("Network1")
    @XmlElement(name = "Network1")
    private String network1;

    @JsonProperty("BatteryResistance")
    @XmlElement(name = "BatteryResistance")
    private String batteryResistance;

    @JsonProperty("UDID")
    @XmlElement(name = "UDID")
    private String udid;

    @JsonProperty("CosmeticsPassed")
    @XmlElement(name = "CosmeticsPassed")
    private String cosmeticsPassed;

    @JsonProperty("SimErased")
    @XmlElement(name = "SimErased")
    private String simErased;

    @JsonProperty("AppVersion")
    @XmlElement(name = "AppVersion")
    private String appVersion;

    @JsonProperty("Cosmetics")
    @XmlElement(name = "Cosmetics")
    private String cosmetics;

    @JsonProperty("CocoBatteryHealth")
    @XmlElement(name = "CocoBatteryHealth")
    private String cocoBatteryHealth;

    @JsonProperty("Notes")
    @XmlElement(name = "Notes")
    private String notes;

    @JsonProperty("SIM1Name")
    @XmlElement(name = "SIM1Name")
    private String sim1Name;

    @JsonProperty("LicenseIdentifier")
    @XmlElement(name = "LicenseIdentifier")
    private String licenseIdentifier;

    @JsonProperty("BatteryShutdown")
    @XmlElement(name = "BatteryShutdown")
    private String batteryShutdown;

    @JsonProperty("BatteryHealthPercentage")
    @XmlElement(name = "BatteryHealthPercentage")
    private String batteryHealthPercentage;

    @JsonProperty("OS")
    @XmlElement(name = "OS")
    private String os;

    @JsonProperty("SKUCode")
    @XmlElement(name = "SKUCode")
    private String skuCode;

    @JsonProperty("DecimalMEID2")
    @XmlElement(name = "DecimalMEID2")
    private String decimalMEID2;

    @JsonProperty("CosmeticsFailed")
    @XmlElement(name = "CosmeticsFailed")
    private String cosmeticsFailed;

    @JsonProperty("IMEI")
    @XmlElement(name = "IMEI")
    private String imei;

    @JsonProperty("CocoDesignCapacity")
    @XmlElement(name = "CocoDesignCapacity")
    private String cocoDesignCapacity;

    @JsonProperty("isMobileCosmetics")
    @XmlElement(name = "isMobileCosmetics")
    private int isMobileCosmetics;

    @JsonProperty("ErrorCode")
    @XmlElement(name = "ErrorCode")
    private String errorCode;

    @JsonProperty("Pending")
    @XmlElement(name = "Pending")
    private String pending;

    @JsonProperty("Ram")
    @XmlElement(name = "Ram")
    private String ram;

    @JsonProperty("color_code")
    @XmlElement(name = "color_code")
    private String colorCode;

    @JsonProperty("gradePerformed")
    @XmlElement(name = "gradePerformed")
    private boolean gradePerformed;

    @JsonProperty("erasedNotes")
    @XmlElement(name = "erasedNotes")
    private String erasedNotes;

    @JsonProperty("grade_profile_id")
    @XmlElement(name = "grade_profile_id")
    private String gradingProfileId;
}
