package com.phonecheck.model.phonecheckapi;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetIWatchInfoResponse {
    private IWatchData data;
    private List<ServiceProvider> serviceProviders;
    private boolean fromCache;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IWatchData {
        private String overall;

        @JsonProperty("Description")
        private String description;

        @JsonProperty("IMEI")
        private String imei;

        @JsonProperty("MEID")
        private String meid;

        @JsonProperty("Model")
        private String model;

        @JsonProperty("Serial Number")
        private String serialNumber;

        @JsonProperty("Warranty Status")
        private String warrantyStatus;

        @JsonProperty("Purchase Date")
        private String purchaseDate;

        @JsonProperty("Replaced Device")
        private String replacedDevice;

        @JsonProperty("SIM-Lock")
        private String simLock;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ServiceProvider {
        private String serviceId;
        private String provider;
    }
}
