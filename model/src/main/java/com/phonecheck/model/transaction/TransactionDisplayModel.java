package com.phonecheck.model.transaction;

import lombok.*;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionDisplayModel {
    private TransactionRecord[] transactionRecords;

    @Getter
    @Setter
    public static class TransactionRecord {
        private String infoId;
        private String transactionId;
        private String masterId;
        private String wareHouse;
        private String masterName;
        private String model;
        private String memory;
        private String imei;
        private String carrier;
        private String pcCarrier;
        private String serial;
        private String udid;
        private String licenseIdentifier;
        private String deviceLock;
        private String appleId;
        private String rooted;
        private String color;
        private String colorCode;
        private String grade;
        private String version;
        private String os;
        private String make;
        private String firmware;
        private String notes;
        private String esn;
        private String licenseId;
        private String deviceCreatedDate;
        private String deviceUpdatedDate;
        private String batteryPercentage;
        private String batteryCycle;
        private String batteryHealthPercentage;
        private String batteryDesignMaxCapacity;
        private String batteryCurrentMaxCapacity;
        private String batterySerial;
        private String batteryModel;
        private String batterySource;
        private String modelNo;
        private String unlockStatus;
        private String testerName;
        private String cosmetics;
        private String buildNo;
        private String appVersion;
        private String manualEntry;
        private String esnResponse;
        private String simLockResponse;
        private String lpn;
        private String custom1;
        private String skuCode;
        private String platform;
        private String regulatoryModelNumber;
        private String cosmeticsFailed;
        private String cosmeticsPassed;
        private String cosmeticsPending;
        private String cosmeticsWorking;
        private String network;
        private String network1;
        private String network2;
        private String sim1MCC;
        private String sim1MNC;
        private String sim2MCC;
        private String sim2MNC;
        private String sim1Name;
        private String sim2Name;
        private String imei2;
        private String simTechnology;
        private String batteryTemperature;
        private String avgBatteryTemperature;
        private String maxBatteryTemperature;
        private String minBatteryTemperature;
        private String batteryResistance;
        private String meid;
        private String meid2;
        private String pEsn;
        private String pEsn2;
        private String simSerial;
        private String simSerial2;
        private String decimalMeid;
        private String decimalMeid2;
        private String simErased;
        private String mdm;
        private String resolvedMake;
        private String resolvedModelNo;
        private String resolvedModel;
        private String resolvedCarrier;
        private String resolvedMemory;
        private String resolvedColor;
        private String countryOfOrigin;
        private String batteryDrainDuration;
        private String batteryChargeStart;
        private String batteryChargeEnd;
        private String batteryDrain;
        private String wifiMacAddress;
        private String simHistory;
        private String iCloudInfo;
        private String batteryDrainInfo;
        private String compatibleSim;
        private String notCompatibleSim;
        private String deviceState;
        private String batteryDrainType;
        private String cocoCurrentCapacity;
        private String cocoDesignCapacity;
        private String oemBatteryHealth;
        private String cocoBatteryHealth;
        private String portNumber;
        private String startHeat;
        private String endHeat;
        private String productCode;
        private String deviceShutdown;
        private String simLock;
        private String bMic;
        private String vMic;
        private String fMic;
        private String defectsCode;
        private String manualFailure;
        private String errorCode;
        private String screenTime;
        private String testerDeviceTime;
        private String gradePerformed;
        private String isLabelPrint;
        private String knox;
        private String guid;
        private String erased;
        private String type;
        private String startTime;
        private String endTime;
        private String restoreCode;
        private String erasedSD;
        private String erasedNotes;
        private String eraserDiff;
        private String id;
        private String working;
        private String passed;
        private String failed;
        private String pending;
        private String preCheckWorking;
        private String preCheckPassed;
        private String preCheckFailed;
        private String preCheckPending;
        private String vendorName;
        private String invoiceNo;
        private String transactionDate;
        private String isCloudTransaction;
        private String stationId;
        private String boxNo;
        private String qty;
        private String eraseStartTime;
        private String eraseEndTime;
        private String eraseType;
        private String modelNumber;
        private String batteryShutdown;
        private String testPlanName;
        private String carrierLockResponse;
        private String parts;
        private String ram;
        private String iftCodes;
        private String gradingResults;
        private String transactionType;
        private String finalPrice;
        private String mdmResponse;
        private String batteryHealthGrade;
        private String eid;
        private String eSimPresent;
        private String eSimErased;
        private String eBayRefurbished;
        private String eBayRejection;
        private String amazonRenewed;
        private String amazonRenewedRejection;
        private String androidCarrierId;
        private String swappaQualified;
        private String swappaRejection;
        private String backMarketQualified;
        private String backMarketRejection;
        private String isMobileCosmetics;
        private String dataVerification;
        private String startBatteryCharge;
        private String endBatteryCharge;
        private String totalBatteryDrain;
        private String warranty;
        private String oemStatus;
        private boolean isSelected;
    }
}