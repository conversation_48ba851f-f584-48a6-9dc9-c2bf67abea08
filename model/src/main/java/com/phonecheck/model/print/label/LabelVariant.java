package com.phonecheck.model.print.label;

import lombok.Getter;

/**
 * Label variant enum represents all the available labels
 * and their file(fxml/image) names. For consistency When
 * creating new label one must declare a new enum constant
 * for it here and use it in the respective label controller.
 */
@Getter
public enum LabelVariant {
    LPN_LABEL_PORTRAIT("LPN Label Portrait", "label_lpn_portrait"),
    BRANDED_LABEL_LANDSCAPE("Branded Label Landscape", "label_branded_landscape"),
    BRANDED_LABEL_PORTRAIT("Branded Label Portrait", "label_branded_portrait"),
    TEST_RESULTS_LABEL_LANDSCAPE("Test Results Landscape", "label_test_results"),
    ANDROID_PROVISION_QR_LABEL("Android Provision QR", "label_android_provision_qr"),
    LABEL_2x1("Label 2x1", "label_2x1"),
    BRANDED_LABEL_LANDSCAPE_2X4("Branded Label Landscape 2x4", "label_branded_landscape_2x4"),
    REPAIR_LABEL_3X2("Repair Label 3x2", "label_repair_3x2"),
    LABEL_BS_4x2("BS 4x2", "label_bs_4x2"),
    LABEL_DP_4x2("DP 4x2", "label_dp_4x2");

    private final String name;
    private final String fileName;

    LabelVariant(String name, String fileName) {
        this.name = name;
        this.fileName = fileName;
    }

    public static LabelVariant fromName(String name) {
        for (LabelVariant labelVariant : values()) {
            if (name.equalsIgnoreCase(labelVariant.name)) {
                return labelVariant;
            }
        }
        return null;
    }
}
