package com.phonecheck.model.mdm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MdmInfo {
    private boolean allowPairing;
    private boolean cloudConfigurationUiComplete;
    private boolean configurationWasApplied;
    private boolean isRemovable;
    private boolean isMandatory;
    private boolean isSupervised;
    private String organizationName;
    private String status;
    private String configurationSource;
    private boolean postSetupProfileWasInstalled;
    private MdmStatus mdmStatus;
}
