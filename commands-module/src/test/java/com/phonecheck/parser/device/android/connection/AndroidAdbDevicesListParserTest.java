package com.phonecheck.parser.device.android.connection;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.status.AuthorizationStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Iterator;
import java.util.Set;

class AndroidAdbDevicesListParserTest {
    @Test
    @DisplayName("One device is parsed")
    void testParseOne() throws IOException {
        final String output = "List of devices attached\n" +
                "ce0317131441621303\tdevice";

        final Set<AndroidDevice> results = new AndroidAdbDevicesListParser().parse(output);
        Assertions.assertNotNull(results);
        Assertions.assertEquals(1, results.size());

        final AndroidDevice device = results.iterator().next();
        Assertions.assertNotNull(device);
        Assertions.assertEquals("ce0317131441621303", device.getId());
        Assertions.assertEquals(AuthorizationStatus.AUTHORIZED, device.getAuthorizationStatus());
    }

    @Test
    @DisplayName("Multiple devices are parsed")
    void testParseMultiple() throws IOException {
        final String output = """
                 List of devices attached
                ce0317131441621303\tdevice
                ce0317131441621302\toffline""";

        final Set<AndroidDevice> results = new AndroidAdbDevicesListParser().parse(output);
        Assertions.assertNotNull(results);
        Assertions.assertEquals(2, results.size());

        Iterator<AndroidDevice> iterator = results.iterator();
        AndroidDevice device = iterator.next();
        Assertions.assertNotNull(device);
        Assertions.assertEquals("ce0317131441621302", device.getId());
        Assertions.assertEquals(AuthorizationStatus.OFFLINE, device.getAuthorizationStatus());

        device = iterator.next();
        Assertions.assertNotNull(device);
        Assertions.assertEquals("ce0317131441621303", device.getId());
        Assertions.assertEquals(AuthorizationStatus.AUTHORIZED, device.getAuthorizationStatus());
    }

    @Test
    @DisplayName("Multiple devices are parsed")
    void testParseNull() throws IOException {
        final Set<AndroidDevice> results = new AndroidAdbDevicesListParser().parse(null);
        Assertions.assertNotNull(results);
        Assertions.assertEquals(0, results.size());
    }

    @Test
    @DisplayName("Multiple devices are parsed")
    void testParseEmpty() throws IOException {
        final String output = "";

        final Set<AndroidDevice> results = new AndroidAdbDevicesListParser().parse(output);
        Assertions.assertNotNull(results);
        Assertions.assertEquals(0, results.size());
    }
}