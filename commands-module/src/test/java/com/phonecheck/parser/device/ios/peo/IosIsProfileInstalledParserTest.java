package com.phonecheck.parser.device.ios.peo;

import com.phonecheck.model.ios.IosConfigProfile;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;

public class IosIsProfileInstalledParserTest {
    @Test
    void expectedInputTest() throws IOException {
        final String input = "com.PhoneCheck.wifiProfile,\n" +
                "com.phonecheck.WiFi2ed,\n" +
                "com.phonecheck.WiFi.venom";
        final Boolean result = new IosIsProfileInstalledParser().parse(input, IosConfigProfile.WIFI);
        final Boolean expectedResult = true;
        Assertions.assertNotNull(result);
        Assertions.assertEquals(expectedResult, result);
    }
}
