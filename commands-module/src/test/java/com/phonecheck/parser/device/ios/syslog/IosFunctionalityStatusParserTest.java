package com.phonecheck.parser.device.ios.syslog;

import com.phonecheck.model.device.IosDevice;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class IosFunctionalityStatusParserTest {
    @Test
    public void testFunctionalityParserExpectedInput() {
        final String functionality = "NA";
        final String input = "Sep 11 20:48:25 Test-iPhone backboardd(MultitouchHID)[66]" +
                " <Notice>: [HID] [MT] dispatchEvent Dispatching event with 1 children," +
                " _eventMask=0x2 _childEventMask=0x2 Cancel=0 Touching=0 inRange=1\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962]" +
                " <Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags pre: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone sharingd (Working100Start-" + functionality +
                "-Working100End)\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags post: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962]" +
                " <Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n";

        IosDevice device = new IosDevice();
        boolean functionalityStatus = new IosFunctionalityStatusParser().parse(device, input);

        Assertions.assertTrue(functionalityStatus);
        Assertions.assertEquals(functionality, device.getBrightStarFunctionality());
    }

    @Test
    public void testFunctionalityParserUnExpectedInput() {
        final String functionality = "NA";
        final String input = "Sep 11 20:48:25 Test-iPhone backboardd(MultitouchHID)[66] <Notice>: " +
                "[HID] [MT] dispatchEvent Dispatching event with 1 children, _eventMask=0x2" +
                " _childEventMask=0x2 Cancel=0 Touching=0 inRange=1\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962]" +
                " <Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags pre: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone sharingd (-" + functionality + "-Working100End)\n" +
                "Sep 11 20:48:25 Test-iPhone powerd[42] <Notice>: calib: svcFlags post: 0x3\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n" +
                "Sep 11 20:48:25 Test-iPhone Preferences(libsystem_containermanager.dylib)[962] " +
                "<Notice>: Requesting disk usage; class = 7, identifier = <private>\n";

        IosDevice device = new IosDevice();
        boolean functionalityStatus = new IosFunctionalityStatusParser().parse(device, input);

        Assertions.assertFalse(functionalityStatus);
        Assertions.assertNull(device.getBrightStarFunctionality());
    }
}