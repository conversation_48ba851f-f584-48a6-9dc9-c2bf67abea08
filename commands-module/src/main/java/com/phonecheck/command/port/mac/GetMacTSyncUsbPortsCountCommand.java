package com.phonecheck.command.port.mac;


import com.phonecheck.command.AbstractCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves ports count of connected ThunderSync hub
 */
@AllArgsConstructor
public class GetMacTSyncUsbPortsCountCommand extends AbstractCommand {
    @Override
    public String[] getCmd() {
        return new String[]{"python3", "./misc/thundersync.py", "portsCount"};
    }
}