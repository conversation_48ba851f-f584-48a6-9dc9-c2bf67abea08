package com.phonecheck.command.device.android.action;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

/**
 * Command to rebrand an AT device
 */
@AllArgsConstructor
public class AndroidAtRebrandCommand extends AbstractAndroidCommand {
    private final String portName;

    @Override
    public String[] getCmd() {
        return new String[]{"./androidmodels", portName, "R"};
    }
}