package com.phonecheck.command.device.ios.info;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves current battery capacity of a specific iOS device
 */
@AllArgsConstructor
public class IosCurrentBatteryCapacityCommand extends AbstractIosCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{
                "./ideviceinfo", "-u", identifier, "-k", "BatteryCurrentCapacity", "-q", "com.apple.mobile.battery"};
    }

}
