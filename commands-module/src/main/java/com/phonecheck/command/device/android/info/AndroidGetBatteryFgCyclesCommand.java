package com.phonecheck.command.device.android.info;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

/**
 * Retrieves connected android device battery fg cycles
 */
//TODO Permission denied error
@AllArgsConstructor
public class AndroidGetBatteryFgCyclesCommand extends AbstractAndroidCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell", "cat", "/sys/class/power_supply/battery/fg_cycle"};
    }
}
