package com.phonecheck.command.device.ios.precheck;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

/**
 * Command to execute pre-check device info results on iOS device
 */
@AllArgsConstructor
public class PreCheckDeviceInfoCommand extends AbstractIosCommand {
    private final String identifier;
    private final String value;
    private final String status;

    @Override
    public String[] getCmd() {
        return new String[]{"./ideviceinfo", value, status, "-u", identifier};
    }
}