package com.phonecheck.command.device.android.fingerprint;

import com.phonecheck.command.AbstractAndroidCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class AndroidPutFingerPrintGestureCommand extends AbstractAndroidCommand {
    private final String identifier;

    @Override
    public String[] getCmd() {
        return new String[]{"./adb", "-s", identifier, "shell",
                "settings", "put", "system", "fingerprint_gesture_quick", "1"};
    }
}
