package com.phonecheck.command.device.ios.test;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor

public class IosObserveNotificationProxyCommand extends AbstractIosCommand {
    private final String udid;
    private final String[] keys;


    @Override
    public String[] getCmd() {
        // Attempt to observe notification proxy
        return new String[]{"./idevicenotificationproxy", "-u", udid, "observe", String.join(" ", keys)};
    }
}
