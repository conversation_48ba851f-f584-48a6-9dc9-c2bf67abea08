package com.phonecheck.command.print.windows;

import com.phonecheck.command.AbstractCommand;
import com.phonecheck.model.print.label.LabelOrientation;
import com.phonecheck.model.print.label.LabelSizeUnit;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@AllArgsConstructor
public class WindowsPrintWithZebraPrinterCommand extends AbstractCommand {
    private final String printerName;
    private final String imageFilePath;
    private final float width;
    private final float height;
    private final LabelSizeUnit labelSizeUnit;
    private final LabelOrientation orientation;

    @Override
    public String[] getCmd() {
        return new String[]{
                ".\\print\\zebra\\ZebraPrinter",
                "-print",
                printerName,
                imageFilePath,
                String.valueOf(width),
                String.valueOf(height),
                labelSizeUnit == LabelSizeUnit.INCH ? "inch" : "mm",
                orientation != null ? orientation.name().toLowerCase() : StringUtils.EMPTY

        };
    }
}
