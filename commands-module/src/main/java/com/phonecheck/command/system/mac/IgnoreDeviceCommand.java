package com.phonecheck.command.system.mac;

import com.phonecheck.command.AbstractIosCommand;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class IgnoreDeviceCommand extends AbstractIosCommand {
    private final boolean shouldIgnore;

    @Override
    public String[] getCmd() {
        return new String[]{
                "defaults", "write", "-g", "ignore-devices", "-bool", shouldIgnore ? "true" : "false"
        };
    }
}
