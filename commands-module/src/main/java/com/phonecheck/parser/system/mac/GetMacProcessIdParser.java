package com.phonecheck.parser.system.mac;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class GetMacProcessIdParser {
    public String parse(final String output) throws IOException {
        String processId = null;

        if (StringUtils.isNotBlank(output)) {
            String[] lines = output.split("\n");
            for (String line : lines) {
                if (line.contains("LISTEN")) {
                    String[] parts = line.split("\\s+");
                    if (parts.length > 1) { // Make sure the line has enough parts
                        processId = parts[1]; // The second column is the PID
                        break; // Exit the loop once you find the PID
                    }
                }
            }
        }

        return processId;
    }
}
