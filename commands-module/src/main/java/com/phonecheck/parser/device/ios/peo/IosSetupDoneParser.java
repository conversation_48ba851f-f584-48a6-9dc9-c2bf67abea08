package com.phonecheck.parser.device.ios.peo;

import com.phonecheck.model.status.SetupDoneStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.time.DateTimeException;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Parser for determining iOS device setup completion status by analyzing device information output.
 * <p>
 * This parser checks multiple indicators in the device information output to determine
 * whether the iOS device has completed its initial setup process and is on the home screen.
 * </p>
 */
@Component
public class IosSetupDoneParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosSetupDoneParser.class);

    private static final String SETUP_DONE_MARKER = "SetupDone: true";
    private static final String SETUP_FINISHED_MARKER = "SetupFinishedAllSteps: true";
    private static final String SCREEN_TIME_PRESENTED_MARKER = "ScreenTimePresented: true";
    private static final String SETUP_LAST_EXIT_PREFIX = "SetupLastExit: ";

    /**
     * Parses the device information output to determine setup completion status.
     * <p>
     * The method checks for multiple indicators of setup completion:
     * <ul>
     *   <li>Valid SetupLastExit timestamp</li>
     *   <li>SetupFinishedAllSteps: true</li>
     *   <li>SetupDone: true</li>
     *   <li>ScreenTimePresented: true</li>
     * </ul>
     * If any of these indicators are present, the setup is considered complete.
     * </p>
     *
     * @param output The raw device information output to parse
     * @return SetupDoneStatus indicating whether setup is complete (DONE) or not (NOT_DONE)
     * @throws IOException if there's an error reading the output
     */
    public SetupDoneStatus parse(final String output) throws IOException {
        LOGGER.debug("Setup done Command parsing output: {}", output);

        if (output == null || output.isEmpty()) {
            return SetupDoneStatus.NOT_DONE;
        }

        // Check for direct setup completion markers
        boolean setupDone = output.contains(SETUP_DONE_MARKER);
        boolean setupFinished = output.contains(SETUP_FINISHED_MARKER);
        boolean screenTimePresented = output.contains(SCREEN_TIME_PRESENTED_MARKER);

        // Check for SetupLastExit timestamp
        boolean hasValidSetupLastExit = false;
        try (BufferedReader reader = new BufferedReader(new StringReader(output))) {
            String line;
            while (null != (line = reader.readLine())) {
                if (line.startsWith(SETUP_LAST_EXIT_PREFIX)) {
                    String timestamp = line.substring(SETUP_LAST_EXIT_PREFIX.length()).trim();
                    if (isValidTimestamp(timestamp)) {
                        hasValidSetupLastExit = true;
                        break;
                    }
                }
            }
        }

        // Determine status based on multiple indicators
        if (hasValidSetupLastExit || setupFinished || setupDone || screenTimePresented) {
            return SetupDoneStatus.DONE;
        }

        return SetupDoneStatus.NOT_DONE;
    }

    /**
     * Validates if a timestamp string is in valid ISO-8601 format.
     *
     * @param timestamp The timestamp string to validate
     * @return true if the timestamp is valid, false otherwise
     */
    private boolean isValidTimestamp(final String timestamp) {
        try {
            OffsetDateTime.parse(timestamp, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            return true;
        } catch (DateTimeException e) {
            LOGGER.debug("Invalid timestamp format: {}", timestamp);
            return false;
        }
    }
}