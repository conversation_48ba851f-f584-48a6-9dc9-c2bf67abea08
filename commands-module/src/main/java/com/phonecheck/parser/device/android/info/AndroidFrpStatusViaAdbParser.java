package com.phonecheck.parser.device.android.info;

import com.phonecheck.model.device.DeviceLock;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
public class AndroidFrpStatusViaAdbParser {
    public static final String GOOGLE_ACCOUNT_MARKER = "com.google";
    public static final String ACTION_ACCOUNT_ADD_MARKER = "action_account_add";
    public static final String ACTION_ACCOUNT_REMOVE_MARKER = "action_account_remove";
    public static final String ACCOUNT_EXIST_MARKER = "Accounts:";
    public static final String ACCOUNT_HISTORY_EXIST_MARKER = "Accounts History";
    public static final String UID_MARKER = "uid";

    public DeviceLock parse(final String response) {
        List<String> lines = Arrays.asList(response.split("\n"));
        boolean accountExist = false;
        boolean accountHistoryExist = false;
        List<String> accountsList = new ArrayList<>();
        List<String> accountsHistoryList = new ArrayList<>();
        Set<String> googleAccountsUidSet = new HashSet<>();

        for (String line : lines) {
            String trimmedLine = line.trim();

            if (accountExist && !trimmedLine.isEmpty() && trimmedLine.contains(GOOGLE_ACCOUNT_MARKER)) {
                accountsList.add(trimmedLine);
                break;
            }

            if (accountHistoryExist && !trimmedLine.isEmpty() &&
                    (trimmedLine.contains(ACTION_ACCOUNT_REMOVE_MARKER) ||
                            trimmedLine.contains(ACTION_ACCOUNT_ADD_MARKER))) {
                accountsHistoryList.add(trimmedLine);
            } else if (trimmedLine.isEmpty()) {
                accountExist = false;
                accountHistoryExist = false;
            }

            if (trimmedLine.contains(ACCOUNT_EXIST_MARKER)) {
                accountExist = true;
            } else if (trimmedLine.contains(ACCOUNT_HISTORY_EXIST_MARKER)) {
                accountHistoryExist = true;
            } else if (trimmedLine.contains(UID_MARKER) && trimmedLine.contains(GOOGLE_ACCOUNT_MARKER)) {
                String[] uids = trimmedLine.split(UID_MARKER);
                if (uids.length > 1) {
                    String uid = uids[1].trim();
                    googleAccountsUidSet.add(uid);
                }
            }
        }

        if (!accountsList.isEmpty()) {
            return DeviceLock.ON;
        } else {
            return filterAndCheckFrpData(accountsHistoryList, googleAccountsUidSet);
        }
    }


    private DeviceLock filterAndCheckFrpData(final List<String> accountsHistoryList,
                                             final Set<String> googleAccountsUidSet) {
        Map<String, String> uidMap = new HashMap<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (String item : accountsHistoryList) {
            String[] parts = item.split(",");
            if (parts.length > 3) {
                String dateStrVal = parts[2];
                String uidVal = parts[3].trim();
                String serialNumber = parts[0].trim();

                if (googleAccountsUidSet.contains(uidVal)) {
                    if (uidMap.containsKey(uidVal)) {
                        if (!uidMap.get(uidVal).split(",")[2].contains(UID_MARKER)) {
                            LocalDateTime start = LocalDateTime.parse(uidMap.get(uidVal).split(",")[2], formatter);
                            LocalDateTime stop = LocalDateTime.parse(dateStrVal, formatter);

                            if (start.isBefore(stop)) {
                                uidMap.put(uidVal + serialNumber, item);
                            }
                        }
                    } else {
                        uidMap.put(uidVal + serialNumber, item);
                    }
                }
            }
        }
        for (String value : uidMap.values()) {
            if (value.contains(ACTION_ACCOUNT_ADD_MARKER)) {
                return DeviceLock.ON;
            }
        }
        return DeviceLock.OFF;
    }
}
