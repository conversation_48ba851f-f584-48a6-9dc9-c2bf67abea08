package com.phonecheck.parser.device.ios.info;

import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.ios.BatteryHealthDetectionMethod;
import com.phonecheck.model.ios.BatteryInfoProperty;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Parses battery info from iDeviceDiagnostics IO registry.
 * <p>
 * Note: Battery health calculated from this method has low priority than the one retrieved from syslog.
 */
@Component
public class IosBatteryInfoParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosBatteryInfoParser.class);

    private static final String NA = "N/A";
    private static final Map<String, Integer> IPHONE_DESIGN_CAPACITY_MAPPINGS = new HashMap<>() {
        {
            put("iPhone XS MAX", 3174);
            put("iPhone XS", 2658);
            put("iPhone XR", 2942);
            put("iPhone X", 2716);
            put("iPhone 8+", 2691);
            put("iPhone 8", 1821);
            put("iPhone 7+", 2900);
            put("iPhone 7", 1960);
            put("iPhone SE", 1624);
            put("iPhone 6S+", 2750);
            put("iPhone 6S", 1715);
            put("iPhone 6+", 2915);
            put("iPhone 6", 1810);
            put("iPhone 5S", 1560);
            put("iPhone 5C", 1510);
            put("iPhone 5", 1440);
            put("iPhone 4S", 1432);
            put("iPhone 4", 1420);
            put("iPad Pro (12.9 inch)", 9720);
            put("iPad Pro (11 inch)", 7812);
            put("iPad Pro 2 12.9", 10891);
            put("iPad Pro 2 10.5", 8134);
            put("iPad 2", 6930);
            put("iPad 3", 11560);
            put("iPad 4", 11560);
            put("iPad Mini 2", 6470);
            put("iPad Air", 8820);
            put("iPad Mini 3", 6470);
            put("iPad Air 2", 7340);
            put("iPad Mini 4", 5124);
            put("iPad Pro 9.7", 7306);
            put("iPad Pro 12.9", 10307);
            put("iPad 5", 8827);
            put("iPhone 11", 3046);
            put("iPhone 11 Pro", 3190);
            put("iPhone 11 Pro Max", 3969);
            put("iPad Mini 5 WiFi", 5124);
            put("iPad Mini 5 Cellular", 5124);
            put("iPad Air 3 WiFi", 8134);
            put("iPad Air 3 Cellular", 8134);
            put("iPad 7 WiFi", 8827);
            put("iPad 7 Cellular", 8827);
            put("iPad Pro 4 WiFi (11 inch)", 8827);
            put("iPad Pro 4 Cellular (11 inch)", 8827);
            put("iPad Pro 4 WiFi (12.9 inch)", 8827);
            put("iPad Pro 4 Cellular (12.9 inch)", 8827);
            put("iPhone SE(2020)", 1821);
            put("iPhone 12 Mini", 2227);
            put("iPhone 12", 2815);
            put("iPhone 12 Pro", 2815);
            put("iPhone 12 Pro Max", 3687);
        }
    };

    private static final List<String> PROPERTIES_TO_BE_PARSED = List.of("NominalChargeCapacity",
            "AppleRawMaxCapacity",
            "FullAvailableCapacity",
            "StateOfCharge",
            "DesignCapacity",
            "IsCharging",
            "CycleCount",
            "Serial",
            "BatterySerialNumber",
            "Model",
            "Temperature",
            "AverageTemperature",
            "MinimumTemperature",
            "MaximumTemperature",
            "Voltage",
            "InstantAmperage",
            "CurrentCapacity"
    );

    private static final List<String> BOOLEAN_PROPERTIES = List.of(BatteryInfoProperty.IS_CHARGING.getName());

    public BatteryInfo parse(final String output,
                             final String deviceModel,
                             final BatteryHealthDetectionMethod batteryHealthDetectionMethod) {
        LOGGER.debug("Command parsing output: {}", output);

        // Check if null or valid xml
        if (StringUtils.isEmpty(output) || !output.matches("(?s).*(<(\\w+)[^>]*>.*</\\2>|<(\\w+)[^>]*/>).*")) {
            return null;
        }

        final Map<BatteryInfoProperty, String> results = new HashMap<>();

        String[] splits = output.split("(?<=</plist>)");

        // Load xml string into a Document object
        Document document = null;
        try {
            document = loadXMLFromString(splits[0]);
        } catch (Exception e) {
            LOGGER.error("Exception occurred while loading xml document from string", e);
        }

        // Normalize xml structure
        if (document == null) {
            return null;
        }
        document.getDocumentElement().normalize();

        // Iterate through the whole xml
        NodeList nodeList = document.getElementsByTagName("*");
        for (int i = 0; i < nodeList.getLength(); i++) {
            Node node = nodeList.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                // if node is <key> and value is present in the list of properties needed to be parsed
                if (node.getNodeName().equals("key") && PROPERTIES_TO_BE_PARSED.contains(node.getTextContent())) {
                    if (BOOLEAN_PROPERTIES.contains(node.getTextContent())) {
                        results.put(BatteryInfoProperty.getByName(
                                node.getTextContent()), nodeList.item(++i).getNodeName());
                    } else {
                        results.put(BatteryInfoProperty.getByName(
                                node.getTextContent()), nodeList.item(++i).getTextContent());
                    }
                }
            }
        }

        // Initialise new battery info object
        final BatteryInfo batteryInfo = new BatteryInfo();

        // Parse battery info
        parseCurrentAndDesignedCapacities(deviceModel, batteryHealthDetectionMethod, results, batteryInfo);
        parseOemCocoHealth(batteryHealthDetectionMethod, batteryInfo);
        parseMiscellaneousProperties(results, batteryInfo);
        parseResistance(results, batteryInfo);

        return batteryInfo;
    }

    /**
     * Method to load xml document from provided xml string
     *
     * @param xml input string
     * @return Document
     * @throws Exception in case of failure occurred while loading xml document
     */
    private Document loadXMLFromString(final String xml) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
        DocumentBuilder builder = factory.newDocumentBuilder();
        InputSource is = new InputSource(new StringReader(xml));
        return builder.parse(is);
    }

    /**
     * Method to parse current and designed battery health capacities from IO registry response
     *
     * @param deviceModel           device model
     * @param healthDetectionMethod battery health calculation method
     * @param results               properties results map retrieved from IO registry
     * @param batteryInfo           battery info object to be updated
     */
    private void parseCurrentAndDesignedCapacities(final String deviceModel,
                                                   final BatteryHealthDetectionMethod healthDetectionMethod,
                                                   final Map<BatteryInfoProperty, String> results,
                                                   final BatteryInfo batteryInfo) {
        // Set designed and current capacity for chosen battery health method
        final String designedCapacity = results.get(BatteryInfoProperty.DESIGN_CAPACITY);
        final String nominalChargeCapacity = results.get(BatteryInfoProperty.NOMINAL_CHARGE_CAPACITY);
        final String appleRawMaxCapacity = results.get(BatteryInfoProperty.APPLE_RAW_MAX_CAPACITY);

        switch (healthDetectionMethod) {
            case OEM -> {
                // Design Capacity
                if (StringUtils.isNotBlank(designedCapacity)) {
                    batteryInfo.setDesignedCapacity(Integer.parseInt(designedCapacity));
                }

                // Current Capacity
                if (StringUtils.isNotBlank(nominalChargeCapacity)) {
                    batteryInfo.setCurrentCapacity(
                            Integer.parseInt(nominalChargeCapacity)
                    );
                } else if (StringUtils.isNotBlank(appleRawMaxCapacity)) {
                    batteryInfo.setCurrentCapacity(
                            Integer.parseInt(appleRawMaxCapacity)
                    );
                }
            }
            case COCO -> {
                // Design Capacity
                if (IPHONE_DESIGN_CAPACITY_MAPPINGS.containsKey(deviceModel)) {
                    batteryInfo.setDesignedCapacity(IPHONE_DESIGN_CAPACITY_MAPPINGS.get(deviceModel));
                } else if (StringUtils.isNotBlank(designedCapacity)) {
                    batteryInfo.setDesignedCapacity(Integer.parseInt(designedCapacity));
                }

                // Current Capacity
                if (StringUtils.isNotBlank(appleRawMaxCapacity)) {
                    batteryInfo.setCurrentCapacity(
                            Integer.parseInt(appleRawMaxCapacity)
                    );
                }
            }
            case OEM_AND_COCO -> {
                // Design Capacity
                if (StringUtils.isNotBlank(designedCapacity)) {
                    batteryInfo.setDesignedCapacity(Integer.parseInt(designedCapacity));
                }
                if (IPHONE_DESIGN_CAPACITY_MAPPINGS.containsKey(deviceModel)) {
                    batteryInfo.setCocoDesignedCapacity(IPHONE_DESIGN_CAPACITY_MAPPINGS.get(deviceModel));
                } else if (StringUtils.isNotBlank(designedCapacity)) {
                    batteryInfo.setCocoDesignedCapacity(Integer.parseInt(designedCapacity));
                }

                // Current Capacity
                if (StringUtils.isNotBlank(nominalChargeCapacity)) {
                    batteryInfo.setCurrentCapacity(
                            Integer.parseInt(nominalChargeCapacity)
                    );
                }
                if (StringUtils.isNotBlank(appleRawMaxCapacity)) {
                    batteryInfo.setCocoCurrentCapacity(
                            Integer.parseInt(appleRawMaxCapacity)
                    );
                }
            }
            default -> LOGGER.error("Invalid method of calculating battery health: {}", healthDetectionMethod);
        }
        if (batteryInfo.getCurrentCapacity() > batteryInfo.getDesignedCapacity()) {
            batteryInfo.setCurrentCapacity(batteryInfo.getDesignedCapacity());
        }
    }

    /**
     * Method to parse the battery health based on the provided battery health calculation method.
     *
     * @param healthDetectionMethod method to be used to calculate battery health
     * @param batteryInfo           battery info object to be updated
     */
    private void parseOemCocoHealth(final BatteryHealthDetectionMethod healthDetectionMethod,
                                    final BatteryInfo batteryInfo) {
        // Calculate battery health from battery information retrieved from IO registry
        if (batteryInfo.getCurrentCapacity() > 0 && batteryInfo.getDesignedCapacity() > 0) {
            // For precise calculation, we need it to be done in decimals and then convert it to int
            int oemHealth = (int) ((batteryInfo.getCurrentCapacity() * 100f) / batteryInfo.getDesignedCapacity());
            batteryInfo.setOemHealthPercentage(oemHealth);

            if (healthDetectionMethod == BatteryHealthDetectionMethod.OEM_AND_COCO) {
                if (batteryInfo.getCocoCurrentCapacity() > 0 && batteryInfo.getCocoDesignedCapacity() > 0) {
                    // For precise calculation, we need it to be done in decimals and then convert it to int
                    int cocoHealth =
                            (int) (batteryInfo.getCurrentCapacity() * 100f) / batteryInfo.getDesignedCapacity();
                    batteryInfo.setCocoHealthPercentage(cocoHealth);

                    int health = (oemHealth + cocoHealth) / 2;
                    if (batteryInfo.getHealthPercentage() > 0 && health <= 0) {
                       LOGGER.info("Battery percentage was greater than zero before and hence not overriding value");
                    } else {
                        batteryInfo.setHealthPercentage(health);
                    }
                } else {
                    batteryInfo.setHealthPercentage(oemHealth);
                }
            } else {
                batteryInfo.setHealthPercentage(oemHealth);
            }
        }

        if (batteryInfo.getHealthPercentage() > 100) {
            batteryInfo.setHealthPercentage(100);
        } else if (batteryInfo.getHealthPercentage() < 0) {
            batteryInfo.setHealthPercentage(0);
        }
    }

    /**
     * Method to parse the battery resistance from IO registry response
     *
     * @param results     properties results map retrieved from IO registry
     * @param batteryInfo battery info object to be updated
     */
    private void parseResistance(final Map<BatteryInfoProperty, String> results, final BatteryInfo batteryInfo) {
        int voltage = results.containsKey(BatteryInfoProperty.VOLTAGE) ?
                Integer.parseInt(results.get(BatteryInfoProperty.VOLTAGE)) : 0;
        int amperage = results.containsKey(BatteryInfoProperty.INSTANT_AMPERAGE) ?
                Integer.parseInt(results.get(BatteryInfoProperty.INSTANT_AMPERAGE)) : 0;
        if (amperage < 0) {
            amperage *= -1;
        }
        if (amperage > 0) {
            double resistance = (voltage * 0.001) / (amperage * 0.001);
            double roundedResistance = Math.ceil(resistance * 10) / 10;
            batteryInfo.setBatteryResistance(roundedResistance);
        }
    }

    /**
     * Method to parse the Miscellaneous battery properties from IO registry response
     *
     * @param results     properties results map retrieved from IO registry
     * @param batteryInfo battery info object to be updated
     */
    private void parseMiscellaneousProperties(final Map<BatteryInfoProperty, String> results,
                                              final BatteryInfo batteryInfo) {
        boolean isCharging = results.containsKey(BatteryInfoProperty.IS_CHARGING)
                && Boolean.parseBoolean(results.get(BatteryInfoProperty.IS_CHARGING));
        batteryInfo.setIsCharging(isCharging);

        int chargingPercentage = NumberUtils.toInt(results.get(BatteryInfoProperty.CURRENT_PERCENTAGE), 0);
        batteryInfo.setBatteryPercentage(chargingPercentage);

        String model = results.getOrDefault(BatteryInfoProperty.MODEL, NA);
        batteryInfo.setModel(model);
        String serial = results.get(BatteryInfoProperty.SERIAL_NUMBER);
        if (StringUtils.isBlank(serial)) {
            serial = results.getOrDefault(BatteryInfoProperty.SERIAL_NUMBER_SECONDARY, NA);
        }
        batteryInfo.setSerial(serial);

        int cycleCount = results.containsKey(BatteryInfoProperty.CYCLE_COUNT) ?
                Integer.parseInt(results.get(BatteryInfoProperty.CYCLE_COUNT)) : 0;
        if (cycleCount > 0) {
            batteryInfo.setCycle(cycleCount);
        }

        int temperature = results.containsKey(BatteryInfoProperty.TEMPERATURE) ?
                Integer.parseInt(results.get(BatteryInfoProperty.TEMPERATURE)) : 0;
        if (cycleCount > 0) {
            temperature /= 100;
            batteryInfo.setTemperature(temperature);
        }

        int averageTemperature = results.containsKey(BatteryInfoProperty.AVERAGE_TEMPERATURE) ?
                Integer.parseInt(results.get(BatteryInfoProperty.AVERAGE_TEMPERATURE)) : 0;
        if (cycleCount > 0) {
            averageTemperature /= 100;
            batteryInfo.setTemperature(averageTemperature);
        }

        int minTemperature = results.containsKey(BatteryInfoProperty.MINIMUM_TEMPERATURE) ?
                Integer.parseInt(results.get(BatteryInfoProperty.MINIMUM_TEMPERATURE)) : 0;
        if (cycleCount > 0) {
            minTemperature /= 100;
            batteryInfo.setMinTemperature(minTemperature);
        }

        int maxTemperature = results.containsKey(BatteryInfoProperty.MAXIMUM_TEMPERATURE) ?
                Integer.parseInt(results.get(BatteryInfoProperty.MAXIMUM_TEMPERATURE)) : 0;
        if (cycleCount > 0) {
            maxTemperature /= 100;
            batteryInfo.setMaxTemperature(maxTemperature);
        }
    }
}
