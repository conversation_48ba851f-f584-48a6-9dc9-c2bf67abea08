package com.phonecheck.parser.device.android.connection;

import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.status.AuthorizationStatus;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.util.HashSet;
import java.util.Set;

/**
 * Output example (one device per line):
 * List of devices attached
 * ce0317131441621303 device
 * ce0317131441621304 unauthorized
 * ce0317131441621302 offline
 */
@Component
public class AndroidAdbDevicesListParser {
    private static final String ATTACHED_DEVICES_MARKER = "List of devices attached";
    private static final String AUTHORIZED_MARKER = "device";
    private static final String UNAUTHORIZED_MARKER = "unauthorized";
    private static final String OFFLINE_MARKER = "offline";

    public Set<AndroidDevice> parse(final String output) throws IOException {
        final Set<AndroidDevice> devices = new HashSet<>();
        if (null == output) {
            return devices;
        }

        try (BufferedReader reader = new BufferedReader(new StringReader(output))) {
            String line;
            while (null != (line = reader.readLine())) {
                if (StringUtils.isBlank(line)
                        || StringUtils.containsIgnoreCase(line, ATTACHED_DEVICES_MARKER)) {
                    continue;
                }

                if (line.indexOf('\t') != -1) {
                    String[] d = line.split("\t");
                    if (d.length == 2) {
                        AndroidDevice device = new AndroidDevice();
                        device.setAndroidConnectionMode(AndroidConnectionMode.ADB);

                        if (d[1] != null) {
                            device.setId(d[0]);

                            if (StringUtils.containsIgnoreCase(d[1], AUTHORIZED_MARKER)) {
                                device.setAuthorizationStatus(AuthorizationStatus.AUTHORIZED);
                            } else if (StringUtils.containsIgnoreCase(d[1], UNAUTHORIZED_MARKER)) {
                                device.setAuthorizationStatus(AuthorizationStatus.UNAUTHORIZED);
                            } else if (StringUtils.containsIgnoreCase(d[1], OFFLINE_MARKER)) {
                                device.setAuthorizationStatus(AuthorizationStatus.OFFLINE);
                            }
                        }

                        devices.add(device);
                    }
                }
            }
        }

        return devices;
    }
}
