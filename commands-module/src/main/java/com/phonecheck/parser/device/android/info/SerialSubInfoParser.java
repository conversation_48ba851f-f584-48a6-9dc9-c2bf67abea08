package com.phonecheck.parser.device.android.info;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.io.IOException;

@Component
public class SerialSubInfoParser {
    private static final String REQUIRES_MARKER = "iRequiresREAD";

    public String parse(final String output) throws IOException {
        String result = output;
        String[] lines = result.split(System.lineSeparator());
        if (lines.length < 4) {
            lines = (result.replace("\r", "")).split("\n");
        }
        if (lines.length < 4) {
            return StringUtils.EMPTY;
        }
        String chunk1 = StringUtils.substringBetween(lines[1], "'", "'");
        if (chunk1 != null) {
            chunk1 = chunk1.replace(".", "").replace(" ", "");
        }
        String chunk2 = StringUtils.substringBetween(lines[2], "'", "'");
        if (chunk2 != null) {
            chunk2 = chunk2.replace(".", "").replace(" ", "");
        }
        String chunk3 = StringUtils.substringBetween(lines[3], "'", "'");
        if (chunk3 != null) {
            chunk3 = chunk3.replace(".", "").replace(" ", "");
        }
        result = chunk1 + chunk2 + chunk3;
        if (result.contains(REQUIRES_MARKER)) {
            return StringUtils.EMPTY;
        }
        return result;
    }
}