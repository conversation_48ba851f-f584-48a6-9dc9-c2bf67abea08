package com.phonecheck.parser.device.ios.mount;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class IosTurnOnDevModeParser {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosTurnOnDevModeParser.class);

    public boolean parse(final String output) throws IOException {
        LOGGER.debug("Command parsing output: {}", output);

        if (StringUtils.isNotBlank(output)) {
            return output.contains("SUCCESS");
        }

        return false;
    }
}
