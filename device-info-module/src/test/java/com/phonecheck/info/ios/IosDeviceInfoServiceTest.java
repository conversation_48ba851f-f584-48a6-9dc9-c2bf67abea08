package com.phonecheck.info.ios;

import com.phonecheck.command.CommandOutput;
import com.phonecheck.command.device.ios.info.*;
import com.phonecheck.command.device.ios.peo.IosSetupDoneCommand;
import com.phonecheck.command.device.ios.process.IosProcessIdCommand;
import com.phonecheck.command.device.ios.test.IosPostNotificationProxyCommand;
import com.phonecheck.dao.service.lookup.ModelDBLookupService;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.ios.util.DiskSizeUtil;
import com.phonecheck.model.constants.ios.IosOemNoticeConstants;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.DiskSize;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.ios.IosFaceIdSensorInfoEvent;
import com.phonecheck.model.ios.*;
import com.phonecheck.model.simcarrier.SimCarrierBundleInfo;
import com.phonecheck.model.status.*;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.syslog.ios.IosSysLogKey;
import com.phonecheck.model.util.MemoryUnit;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.model.util.TimerLoggerUtil;
import com.phonecheck.parser.device.ios.info.*;
import com.phonecheck.parser.device.ios.peo.IosSetupDoneParser;
import com.phonecheck.parser.device.ios.process.GetIosProcessIdParser;
import com.phonecheck.parser.device.ios.syslog.IosFaceIdEnrollmentScreenParser;
import com.phonecheck.parser.device.ios.syslog.IosOemRepairHistoryParser;
import com.phonecheck.parser.device.test.DevicePostNotificationProxyParser;
import com.phonecheck.parser.device.test.DeviceWirelessChargingParser;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.phonecheck.model.ios.ProcessName.POWERD;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IosDeviceInfoServiceTest {
    @Mock
    private CommandExecutor executor;
    @Mock
    private IosGetPropertiesParser iosGetPropertiesParser;
    @Mock
    private IosFaceIdWorkingStatusParser iosFaceIdWorkingStatusParser;
    @Mock
    private IosRootedDevicesParser iosRootedDevicesParser;
    @Mock
    private IosDiskSizeParser iosDiskSizeParser;
    @Mock
    private IosSetupDoneParser iosSetupDoneParser;
    @Mock
    private IosGetSerialNumbersParser iosGetSerialNumbersParser;
    @Mock
    private IosSimCarrierBundleInfoParser iosSimCarrierBundleInfoParser;
    @Mock
    private IosCloudConfigInfoResponseParser iosCloudConfigInfoResponseParser;
    @Mock
    private IosFaceIdSensorParser iosFaceIdSensorParser;
    @Mock
    private DiskSizeUtil diskSizeUtil;
    @Mock
    private ApplicationEventPublisher eventPublisher;

    @Mock
    private IosTouchIdCommand1Parser iosTouchIdCommand1Parser;

    @Mock
    private IosTouchIdCommand2Parser iosTouchIdCommand2Parser;

    @Mock
    private IosIsDeviceOnHelloParser iosIsDeviceOnHelloParser;
    @Mock
    private IosFaceIdEnrollmentScreenParser iosFaceIdEnrollmentScreenParser;
    @Mock
    private IosWifiStatusParser iosWifiStatusParser;
    @Mock
    private GetIosProcessIdParser getIosProcessIdParser;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;
    @Mock
    private EIDParser eidParser;
    @Mock
    private IosDeviceLockParser iosDeviceLockParser;
    @Mock
    private IosAppleIdParser iosAppleIdParser;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private OsChecker osChecker;
    @Mock
    private DevicePostNotificationProxyParser postNotificationProxyParser;

    @Mock
    private ModelDBLookupService modelDBLookupService;
    @Mock
    private CommandOutput commandOutput;
    @Mock
    private DeviceWirelessChargingParser deviceWirelessChargingParser;
    @Mock
    private IosOemRepairHistoryParser oemRepairHistoryParser;

    @InjectMocks
    private IosDeviceInfoService iosDeviceInfoService;
    private final String outputString = "output value";
    private IosDevice device;

    @BeforeEach
    public void beforeEach() {
        device = new IosDevice();
        device.setId("123");
    }

    @Test
    public void getPropertiesTest() throws IOException {

        IosProperty[] iosProperties = {IosProperty.SERIAL_NUMBER, IosProperty.IMEI, IosProperty.IMEI2};
        Map<IosProperty, String> outputMap = new HashMap<>();
        when(executor.execute(any(IosGetPropertiesCommand.class))).thenReturn(outputString);
        when(iosGetPropertiesParser.parse(outputString, iosProperties)).thenReturn(outputMap);

        Map<IosProperty, String> propertiesMap = iosDeviceInfoService.getProperties(device, true, iosProperties);

        verify(executor).execute(any(IosGetPropertiesCommand.class));
        verify(iosGetPropertiesParser).parse(outputString, iosProperties);
        assertEquals(outputMap, propertiesMap);
    }

    @Test
    public void getPropertyByKeyTest() throws IOException {

        when(executor.execute(any(IosGetPropertyByKeyCommand.class))).thenReturn(outputString);

        String imei = iosDeviceInfoService.getPropertyByKey(device, IosProperty.IMEI);

        verify(executor).execute(any(IosGetPropertyByKeyCommand.class));
        assertEquals(outputString, imei);
    }

    @Test
    public void isTouchIdSupportedTest() throws IOException {
        String touchIdString = "<key>HasMesa</key>\n\t<true/>";
        when(executor.execute(any(IosTouchIdSupportedCommand.class))).thenReturn(touchIdString);

        Boolean isTouchIdSupportedOutput = iosDeviceInfoService.isTouchIdSupported(device);

        verify(executor).execute(any(IosTouchIdSupportedCommand.class));
        assertTrue(isTouchIdSupportedOutput);
    }

    @Test
    public void isFaceIdSupportedTest() throws IOException {
        String faceIdString = "<key>PearlIDCapability</key>\n\t<false/>";
        when(executor.execute(any(IosFaceIdSupportedCommand.class))).thenReturn(faceIdString);

        Boolean isFaceIdSupportedOutput = iosDeviceInfoService.isFaceIdSupported(device);

        verify(executor).execute(any(IosFaceIdSupportedCommand.class));
        assertFalse(isFaceIdSupportedOutput);
    }

    @Test
    @DisplayName("FaceId working status as TRUE")
    public void isFaceIdWorkingStatusTest() throws IOException {
        String output1 = "output1";
        String output2 = "";
        String output3 = "output3";

        when(executor.execute(any(IosFaceIdWorkingCommand.class)))
                .thenReturn(output1)
                .thenReturn(output2)
                .thenReturn(output3);
        when(iosFaceIdWorkingStatusParser.checkPearlSelfTestResult(eq(output1))).thenReturn(true);
        when(iosFaceIdWorkingStatusParser.checkMbArmedResult(eq(output3))).thenReturn(true);
        when(iosFaceIdWorkingStatusParser.checkFrontCameraSerialNumberResult(eq(output3))).thenReturn(true);

        boolean isFaceIdWorkingOutput = iosDeviceInfoService.isFaceIdWorking(device);

        verify(executor, times(3)).execute(any(IosFaceIdWorkingCommand.class));
        assertTrue(isFaceIdWorkingOutput);
    }

    @Test
    @DisplayName("FaceId working status as FALSE")
    public void isFaceIdNotWorkingStatusTest() throws IOException {
        String output1 = "output1";
        String output2 = "output2";

        when(executor.execute(any(IosFaceIdWorkingCommand.class)))
                .thenReturn(output1)
                .thenReturn(output2);
        when(iosFaceIdWorkingStatusParser.checkPearlSelfTestResult(eq(output1))).thenReturn(true);
        when(iosFaceIdWorkingStatusParser.checkMbArmedResult(eq(output2))).thenReturn(true);
        when(iosFaceIdWorkingStatusParser.checkFrontCameraSerialNumberResult(eq(output2))).thenReturn(false);

        boolean isFaceIdWorkingOutput = iosDeviceInfoService.isFaceIdWorking(device);

        verify(executor, times(2)).execute(any(IosFaceIdWorkingCommand.class));
        assertFalse(isFaceIdWorkingOutput);
    }

    @Test
    public void getRootedStatusTest() throws IOException {
        when(executor.execute(any(IosGetRootedStatusCommand.class), anyLong())).thenReturn(outputString);
        when(iosRootedDevicesParser.parse(eq(outputString))).thenReturn(RootedStatus.ROOTED);

        RootedStatus rootedStatusOutput = iosDeviceInfoService.getRootedStatus(device);

        verify(executor).execute(any(IosGetRootedStatusCommand.class), anyLong());
        verify(iosRootedDevicesParser).parse(eq(outputString));
        assertEquals(RootedStatus.ROOTED, rootedStatusOutput);
    }

    @Test
    public void getRootedStatusTestException() throws IOException {
        when(executor.execute(any(IosGetRootedStatusCommand.class), anyLong())).thenReturn(null);

        RootedStatus rootedStatusOutput = iosDeviceInfoService.getRootedStatus(device);

        verify(executor).execute(any(IosGetRootedStatusCommand.class), anyLong());
        verify(iosRootedDevicesParser, never()).parse(eq(outputString));
        assertEquals(RootedStatus.NA, rootedStatusOutput);
    }

    @Test
    public void getDiskSizeTest() throws IOException {
        when(executor.execute(any(IosGetDiskSizeCommand.class))).thenReturn(outputString);
        when(iosDiskSizeParser.parse(eq(outputString))).thenReturn(512000000000L);
        when(diskSizeUtil.roundDeviceDiskSize(anyLong())).thenCallRealMethod();

        DiskSize diskSizeOutput = iosDeviceInfoService.getDiskSize(device);

        verify(executor).execute(any(IosGetDiskSizeCommand.class));
        verify(iosDiskSizeParser).parse(eq(outputString));
        assertEquals(512L, diskSizeOutput.getSize());
        assertEquals(MemoryUnit.GIGABYTES, diskSizeOutput.getMemoryUnit());
    }

    @Test
    public void getDiskSizeTest1TB() throws IOException {
        when(executor.execute(any(IosGetDiskSizeCommand.class))).thenReturn(outputString);
        when(iosDiskSizeParser.parse(eq(outputString))).thenReturn(1024000000000L);
        when(diskSizeUtil.roundDeviceDiskSize(anyLong())).thenCallRealMethod();
        when(diskSizeUtil.getDiskSizeInTB(anyLong())).thenCallRealMethod();

        DiskSize diskSizeOutput = iosDeviceInfoService.getDiskSize(device);

        verify(executor).execute(any(IosGetDiskSizeCommand.class));
        verify(iosDiskSizeParser).parse(eq(outputString));
        assertEquals(1L, diskSizeOutput.getSize());
        assertEquals(MemoryUnit.TERABYTES, diskSizeOutput.getMemoryUnit());
    }

    @Test
    public void getCurrentBatteryCapacityTest() throws IOException {
        when(executor.execute(any(IosCurrentBatteryCapacityCommand.class))).thenReturn("10 ");

        int currentBatteryCapacityOutput = iosDeviceInfoService.getCurrentBatteryChargePercentage(device);

        verify(executor).execute(any(IosCurrentBatteryCapacityCommand.class));
        assertEquals(10, currentBatteryCapacityOutput);
    }

    @Test
    public void getCurrentBatteryCapacityExceptionTest() throws IOException {
        when(executor.execute(any(IosCurrentBatteryCapacityCommand.class))).thenReturn(" ");

        Integer currentBatteryCapacityOutput = iosDeviceInfoService.getCurrentBatteryChargePercentage(device);

        verify(executor).execute(any(IosCurrentBatteryCapacityCommand.class));
        assertEquals(0, currentBatteryCapacityOutput);
    }

    @Test
    public void getDeviceCurrentScreenTest() throws IOException {
        when(executor.execute(any(IosSetupDoneCommand.class))).thenReturn(outputString);
        when(iosSetupDoneParser.parse(eq(outputString))).thenReturn(SetupDoneStatus.DONE);

        SetupDoneStatus setupDoneStatus = iosDeviceInfoService.getDeviceSetupDoneStatus(device);

        verify(executor).execute(any(IosSetupDoneCommand.class));
        verify(iosSetupDoneParser).parse(eq(outputString));
        assertEquals(SetupDoneStatus.DONE, setupDoneStatus);
    }

    @Test
    public void getSerialNumbersTestWithStringOutput() throws IOException {
        final SerialNumbers serialNumbers = SerialNumbers.builder().build();
        device.setOsMajorVersion(17.2f);
        when(executor.execute(any(IosGetSerialNumbersCommand.class))).thenReturn(outputString);
        when(iosGetSerialNumbersParser.parse(eq(outputString))).thenReturn(serialNumbers);

        iosDeviceInfoService.getOemRawSerialNumbers(device);

        verify(executor).execute(any(IosGetSerialNumbersCommand.class));
        verify(executor, times(22)).execute(any(IosDeviceGetSerialNoCommand.class));
        verify(executor).execute(any(IosDeviceGetLCDCoverGlassSerialNoCommand.class));
        verify(executor, times(4)).execute(any(IosDeviceGetSerialNoViaRegNameCommand.class));
        verify(iosGetSerialNumbersParser).parse(eq(outputString));
        verify(iosGetSerialNumbersParser).updateBatterySerialNumber(eq(serialNumbers), eq(null));
        verify(iosGetSerialNumbersParser).updateCameraSerialNumbers(eq(serialNumbers), eq(null));
        verify(iosGetSerialNumbersParser).updateLcdSerialNumbers(eq(serialNumbers), eq(null));
    }

    @Test
    public void getSerialNumbersTestWhenStringOutputFails() throws IOException {
        final SerialNumbers serialNumbers = SerialNumbers.builder().build();
        serialNumbers.setSerial("expectedSerial");
        serialNumbers.setBatterySerial("expectedBatterySerial");
        serialNumbers.setFactoryBatterySerial("expectedFactoryBatterySerial");
        when(executor.execute(any(IosGetSerialNumbersCommand.class))).thenReturn(outputString);
        when(iosGetSerialNumbersParser.parse(eq(outputString))).thenReturn(serialNumbers);

        SerialNumbers actualSerialNumbers = iosDeviceInfoService.getOemRawSerialNumbers(device);

        verify(executor).execute(any(IosGetSerialNumbersCommand.class));
        verify(iosGetSerialNumbersParser).parse(eq(outputString));
        assertEquals(serialNumbers.getSerial(), actualSerialNumbers.getSerial());
        assertEquals(serialNumbers.getBatterySerial(), actualSerialNumbers.getBatterySerial());
        assertEquals(serialNumbers.getFactoryBatterySerial(), actualSerialNumbers.getFactoryBatterySerial());
    }

    @Test
    public void getSerialNumbersTestWithMapOutput() throws IOException {
        device.setOsMajorVersion(17.4f);
        final SerialNumbers serialNumbers = SerialNumbers.builder().build();
        final Map<OemProperty, String> oemMap = new HashMap<>();

        // Camera Serial Number Map
        final Map<OemProperty, String> cameraSerialsMap = new HashMap<>();
        cameraSerialsMap.put(OemProperty.FRONT_CAMERA_SERIAL_NUMBER, null);
        cameraSerialsMap.put(OemProperty.BACK_CAMERA_SERIAL_NUMBER, null);

        when(iosGetSerialNumbersParser.parse(eq(oemMap))).thenReturn(serialNumbers);

        SerialNumbers actualSerialNumbers = iosDeviceInfoService.getOemRawSerialNumbers(device);

        when(executor.execute(any(IosDeviceGetSerialNoViaRegNameCommand.class))).thenReturn("Sample Output");
        Map<OemProperty, String> lcdSerialNumbers = iosDeviceInfoService.getLCDSerialNumbers(device);

        when(executor.execute(any(IosDeviceGetSerialNoCommand.class))).thenReturn("Sample Output");
        Map<OemProperty, String> cameraSerialNumbers = iosDeviceInfoService.getCameraSerialNumbers(device);

        verify(iosGetSerialNumbersParser).parse(eq(oemMap));
        assertEquals(serialNumbers.getSerial(), actualSerialNumbers.getSerial());
        assertEquals(serialNumbers.getBatterySerial(), actualSerialNumbers.getBatterySerial());
        assertEquals(serialNumbers.getFactoryBatterySerial(), actualSerialNumbers.getFactoryBatterySerial());
        assertEquals(oemMap, lcdSerialNumbers);
        assertEquals(cameraSerialsMap, cameraSerialNumbers);
    }

    @Test
    public void getCameraSerialsFromBasicDriversTest() throws IOException {
        //Get camera serials from basic diagnostics drivers
        when(executor.execute(any(IosDeviceGetSerialNoCommand.class))).thenReturn("Sample Output");
        when(iosGetSerialNumbersParser.parseFrontCameraSerialNumber(eq("Sample Output")))
                .thenReturn("1234567");
        when(iosGetSerialNumbersParser.parseBackCameraSerialNumber(eq("Sample Output")))
                .thenReturn("7654321");
        Map<OemProperty, String> cameraSerialNumbers = iosDeviceInfoService.getCameraSerialNumbers(device);

        assertEquals("1234567", cameraSerialNumbers.get(OemProperty.FRONT_CAMERA_SERIAL_NUMBER));
        assertEquals("7654321", cameraSerialNumbers.get(OemProperty.BACK_CAMERA_SERIAL_NUMBER));
        verify(executor, times(1)).execute(any(IosDeviceGetSerialNoCommand.class));
    }

    @Test
    public void getCameraSerialsFromAdvancedDriversTest() throws IOException {
        // Get camera serials from all diagnostics drivers
        when(executor.execute(any(IosDeviceGetSerialNoCommand.class)))
                .thenReturn(null).thenReturn(null).thenReturn(null)
                .thenReturn(null).thenReturn(null)
                .thenReturn("Sample Output 2");
        when(iosGetSerialNumbersParser.parseFrontCameraSerialNumber(eq("Sample Output 2")))
                .thenReturn("123");
        when(iosGetSerialNumbersParser.parseBackCameraSerialNumber(eq("Sample Output 2")))
                .thenReturn("321");
        Map<OemProperty, String> cameraSerialNumbers = iosDeviceInfoService.getCameraSerialNumbers(device);

        verify(executor, times(6)).execute(any(IosDeviceGetSerialNoCommand.class));
        assertEquals("123", cameraSerialNumbers.get(OemProperty.FRONT_CAMERA_SERIAL_NUMBER));
        assertEquals("321", cameraSerialNumbers.get(OemProperty.BACK_CAMERA_SERIAL_NUMBER));
    }

    @Test
    public void fetchOemRepairHistoryForLatestIosTest1() throws Exception {
        final String oemRepairHistory = """
                WARNING: Sending query with unknown domain “com.apple.corerepair”.
                DCSignedComponents[0]:
                IssueComponents[3]:
                 0:
                  Name: Display
                  State: 3
                 1:
                  Name: Battery
                  State: 3
                 2:
                  Name: Camera
                  State: 3
                RepairHistory[4]:
                 0:
                  Name: vcrt#
                  State: 4
                 1:
                  Name: FCCL#
                  State: 4
                 2:
                  Name: CmCl#
                  State: 4
                 3:
                  Name: HmCl#
                  State: 4
                Status: 0
                UnsealedComponents[1]:
                 0:
                  Name: drp#
                  State: 4
                Version: 0.2""";
        when(commandOutput.dt(any())).thenReturn("");
        when(executor.execute(any(IosOemRepairHistoryCommand.class))).thenReturn(oemRepairHistory);
        when(oemRepairHistoryParser.parse(eq(oemRepairHistory))).thenCallRealMethod();

        iosDeviceInfoService.fetchOemRepairHistoryForLatestIos(device);

        assertEquals(IosOemNoticeConstants.OEM_REPAIRED_NOTICE_VALUE, device.getOemBatteryNotice());
        assertEquals(IosOemNoticeConstants.OEM_REPAIRED_NOTICE_VALUE, device.getOemFrontCameraNotice());
        assertEquals(IosOemNoticeConstants.OEM_REPAIRED_NOTICE_VALUE, device.getOemBackCameraNotice());
        assertEquals(IosOemNoticeConstants.OEM_REPAIRED_NOTICE_VALUE, device.getOemDisplayNotice());
    }

    @Test
    public void fetchOemRepairHistoryForLatestIosTest2() throws Exception {
        final String oemRepairHistory = """
                DCSignedComponents[0]:
                RepairHistory[1]:
                 1:
                  Name: FCCL#
                  State: 4
                IssueComponents[3]:
                 0:
                  Name: Display
                  State: 3
                 1:
                  Name: Battery
                  State: 3
                 2:
                  Name: Camera
                  State: 3
                Status: 0
                UnsealedComponents[0]:
                Version: 0.2
                WARNING: Sending query with unknown domain “com.apple.corerepair”.""";
        when(commandOutput.dt(any())).thenReturn("");
        when(executor.execute(any(IosOemRepairHistoryCommand.class))).thenReturn(oemRepairHistory);
        when(oemRepairHistoryParser.parse(eq(oemRepairHistory))).thenCallRealMethod();

        iosDeviceInfoService.fetchOemRepairHistoryForLatestIos(device);

        assertEquals(IosOemNoticeConstants.OEM_ISSUED_COMPONENT_NOTICE_VALUE, device.getOemBatteryNotice());
        assertEquals(IosOemNoticeConstants.OEM_REPAIRED_NOTICE_VALUE, device.getOemFrontCameraNotice());
        assertEquals(IosOemNoticeConstants.OEM_ISSUED_COMPONENT_NOTICE_VALUE, device.getOemDisplayNotice());
    }

    @Test
    public void getSimCarrierBundleInfoTest() throws IOException {
        SimCarrierBundleInfo simCarrierBundleInfo = new SimCarrierBundleInfo();
        when(executor.execute(any(IosGetSimCarrierBundleInfoCommand.class))).thenReturn(outputString);
        when(iosSimCarrierBundleInfoParser.parse(eq(outputString))).thenReturn(simCarrierBundleInfo);

        SimCarrierBundleInfo simCarrierBundleInfoOutput = iosDeviceInfoService.getSimCarrierBundleInfo(device, true);

        verify(executor).execute(any(IosGetSimCarrierBundleInfoCommand.class));
        verify(iosSimCarrierBundleInfoParser).parse(eq(outputString));
        assertEquals(simCarrierBundleInfo, simCarrierBundleInfoOutput);
    }

    @Test
    public void getMdmInfoTest() throws Exception {
        when(executor.execute(any(IosCloudConfigInfoCommand.class))).thenReturn(outputString);
        when(iosCloudConfigInfoResponseParser.parse(eq(outputString), eq(device.getSetupDoneStatus())))
                .thenReturn(new HashMap<>());

        Map<MdmInfoProperty, String> result = iosDeviceInfoService.getMdmDataFromCloudConfig(device);

        Assertions.assertNotNull(result);
        verify(executor).execute(any(IosCloudConfigInfoCommand.class));
        verify(iosCloudConfigInfoResponseParser).parse(eq(outputString), eq(device.getSetupDoneStatus()));
    }

    @Test
    public void parseFaceIdSensorWorkingSysLogStreamTest() {
        device.setFaceIdSensor(WorkingStatus.PENDING);
        when(iosFaceIdSensorParser.parse(eq("sysLogResponse"))).thenReturn(WorkingStatus.YES);

        boolean output = iosDeviceInfoService.parseSysLogStream(device, IosSysLogKey.FACE_ID_WORKING_3,
                "sysLogResponse");

        verify(eventPublisher).publishEvent(any(IosFaceIdSensorInfoEvent.class));
        assertTrue(output);
    }

    @Test
    public void parseManualFaceIdSensorWorkingSysLogStreamTest() {
        device.setFaceIdSensor(WorkingStatus.YES);
        device.setManualFaceId(true);

        boolean output = iosDeviceInfoService.parseSysLogStream(device, IosSysLogKey.FACE_ID_WORKING_3,
                "sysLogResponse");

        verify(iosFaceIdSensorParser, never()).parse(any());
        verify(eventPublisher, never()).publishEvent(any(IosFaceIdSensorInfoEvent.class));
        assertFalse(output);
        assertEquals(WorkingStatus.YES, device.getFaceIdSensor());
    }

    @Test
    public void parseManualFaceIdSysLogStreamTest() {
        when(iosFaceIdEnrollmentScreenParser.parse(eq("sysLogResponse"))).thenReturn(true);

        boolean output = iosDeviceInfoService.parseSysLogStream(device,
                IosSysLogKey.MANUAL_FACE_ID_ENROLLMENT_SCREEN_OPEN_1,
                "sysLogResponse");

        assertTrue(output);
    }

    @Test
    public void parseFaceIdSensorNotWorkingSysLogStreamTest() {
        device.setFaceIdSensor(WorkingStatus.PENDING);
        when(iosFaceIdSensorParser.parse(eq("sysLogResponse"))).thenReturn(WorkingStatus.NO);

        boolean output = iosDeviceInfoService.parseSysLogStream(device, IosSysLogKey.FACE_ID_NOT_WORKING_1,
                "sysLogResponse");

        verify(eventPublisher).publishEvent(any(IosFaceIdSensorInfoEvent.class));
        assertTrue(output);
    }

    @Test
    public void parseFaceIdSensorSysLogStreamTest() {
        device.setFaceIdSensor(WorkingStatus.NO);

        boolean output = iosDeviceInfoService.parseSysLogStream(device, IosSysLogKey.FACE_ID_NOT_WORKING_1,
                "sysLogResponse");

        verify(iosFaceIdSensorParser, never()).parse(anyString());
        verify(eventPublisher, never()).publishEvent(any(IosFaceIdSensorInfoEvent.class));
        assertFalse(output);
    }

    @Test
    public void getTouchIdStatusCommand1Test() throws Exception {
        when(executor.execute(any(IosGetTouchIdStatusCommand1.class))).thenReturn(outputString);
        when(iosTouchIdCommand1Parser.parse(eq(outputString))).thenReturn(WorkingStatus.NO);

        WorkingStatus workingStatus = iosDeviceInfoService.getTouchIdStatus(device);

        verify(executor).execute(any(IosGetTouchIdStatusCommand1.class));
        verify(iosTouchIdCommand1Parser).parse(eq(outputString));
        assertEquals(WorkingStatus.NO, workingStatus);
    }

    @Test
    public void getTouchIdStatusCommand2Test() throws Exception {
        when(executor.execute(any(IosGetTouchIdStatusCommand1.class))).thenReturn(outputString);
        when(executor.execute(any(IosGetTouchIdStatusCommand2.class))).thenReturn(outputString);
        when(iosTouchIdCommand1Parser.parse(eq(outputString))).thenReturn(WorkingStatus.PENDING);
        when(iosTouchIdCommand2Parser.parse(eq(outputString))).thenReturn(WorkingStatus.NO);

        WorkingStatus workingStatus = iosDeviceInfoService.getTouchIdStatus(device);

        verify(executor).execute(any(IosGetTouchIdStatusCommand1.class));
        verify(executor).execute(any(IosGetTouchIdStatusCommand2.class));
        verify(iosTouchIdCommand1Parser).parse(eq(outputString));
        verify(iosTouchIdCommand2Parser).parse(eq(outputString));
        assertEquals(WorkingStatus.NO, workingStatus);
    }

    @Test
    public void getTouchIdStatusWorkingTest() throws Exception {
        when(executor.execute(any(IosGetTouchIdStatusCommand1.class))).thenReturn(outputString);
        when(executor.execute(any(IosGetTouchIdStatusCommand2.class))).thenReturn(outputString);
        when(iosTouchIdCommand1Parser.parse(eq(outputString))).thenReturn(WorkingStatus.PENDING);
        when(iosTouchIdCommand2Parser.parse(eq(outputString))).thenReturn(WorkingStatus.PENDING);

        WorkingStatus workingStatus = iosDeviceInfoService.getTouchIdStatus(device);

        verify(executor).execute(any(IosGetTouchIdStatusCommand1.class));
        verify(executor).execute(any(IosGetTouchIdStatusCommand2.class));
        verify(iosTouchIdCommand1Parser).parse(eq(outputString));
        verify(iosTouchIdCommand2Parser).parse(eq(outputString));
        assertEquals(WorkingStatus.YES, workingStatus);
    }

    @Test
    public void isDeviceRestartCompleted() throws IOException {
        when(executor.execute(any(IosDeviceOnHelloCommand.class), anyLong())).thenReturn("GuessedCountry");
        when(iosIsDeviceOnHelloParser.parse(eq("GuessedCountry"))).thenReturn(true);

        boolean results = iosDeviceInfoService.isDeviceOnHello(device);

        verify(executor).execute(any(IosDeviceOnHelloCommand.class), anyLong());
        verify(iosIsDeviceOnHelloParser).parse(eq("GuessedCountry"));
        assertTrue(results);
    }

    @Test
    public void parseWifiStatusSysLogStreamTest() {
        when(iosWifiStatusParser.parse("sysLogResponse")).thenReturn(true);
        boolean output = iosDeviceInfoService.parseSysLogStream(device, IosSysLogKey.WIFI_CONNECTED_1,
                "sysLogResponse");
        assertTrue(output);
    }


    @Test
    public void testGetProcessIDs() throws IOException {
        when(osChecker.isMac()).thenReturn(true);
        when(executor.execute(any(IosProcessIdCommand.class))).thenReturn("some value");
        when(getIosProcessIdParser.parse(any(), any())).thenReturn(new HashMap<>());

        Map<ProcessName, String> result = iosDeviceInfoService.getDeviceProcessIds(device, POWERD);

        Assertions.assertNotNull(result);
        verify(executor).execute(any(IosProcessIdCommand.class));
        verify(getIosProcessIdParser).parse(eq("some value"), eq(POWERD));
    }

    @Test
    public void testGetEID() throws IOException {
        when(executor.execute(any(GetEIDCommand.class))).thenReturn("TEST Response");
        when(eidParser.parse(any())).thenReturn("Test EID");

        String result = iosDeviceInfoService.getEID(device);

        Assertions.assertNotNull(result);
        Assertions.assertEquals("Test EID", result);
        verify(executor, times(1)).execute(any(GetEIDCommand.class));
        verify(eidParser, times(1)).parse(any());
    }

    @Test
    public void testGetFCCID() {
        device.setProductType("iPhone8,1");
        Map<String, String> testFccIDMap = new HashMap<>();
        testFccIDMap.put("iPhone8,1", "E2946A");
        when(inMemoryStore.getFccIdMap()).thenReturn(testFccIDMap);
        String result = iosDeviceInfoService.getFccId(device);
        Assertions.assertNotNull(result);
        Assertions.assertEquals("BCG-E2946A", result);
    }

    @Test
    public void notifyDeviceChargingStatusTest() throws IOException {

        when(executor.execute(any(IosGetBatteryInfoViaIoRegCommand.class))).thenReturn("-baseline arcas-");
        when(executor.execute(any(IosPostNotificationProxyCommand.class))).thenReturn("testString");
        when(postNotificationProxyParser.parse(eq("testString"))).thenReturn(NotificationStatus.SUCCESS);

        iosDeviceInfoService.notifyDeviceChargingStatus(device);

        verify(executor).execute(any(IosGetBatteryInfoViaIoRegCommand.class));
        verify(executor).execute(any(IosPostNotificationProxyCommand.class));

    }

    @Test
    @DisplayName("Test that device lock status is not changed from " +
            "ON to OFF when syslog determined device lock as OFF")
    void testOnGettingICloudKeyAndDeviceLockDeterminedOffFromSysLog() {

        device.setDeviceLock(DeviceLock.ON);
        device.setOperatingSystem("iOS");
        device.setActivationStatus(ActivationStatus.ACTIVATED);
        IosSysLogKey iosSysLogKey = IosSysLogKey.ICLOUD_APPLE_ID_NOT_FOUND;
        String sysLogResponse = "Sample Response";
        when(iosDeviceLockParser.parse(device, sysLogResponse)).thenReturn(DeviceLock.OFF);
        iosDeviceInfoService.parseSysLogStream(device, iosSysLogKey, sysLogResponse);
        assertNotSame(device.getDeviceLock(), DeviceLock.OFF);
    }

    @Test
    void testEsimOnlyDeviceWhenSimTrayStatusIsBlankReturnsTrue() throws Exception {
        doReturn("").when(executor).execute(any());
        boolean result = iosDeviceInfoService.isEsimOnlyDevice(device);
        assertTrue(result);
    }

    @Test
    void testEsimOnlyDeviceWhenSimTrayStatusIsNotBlankReturnsFalse() throws Exception {
        doReturn("SimTrayPresent").when(executor).execute(any());
        boolean result = iosDeviceInfoService.isEsimOnlyDevice(device);
        assertFalse(result);
    }

}
