package com.zerobugs.service;

import com.zerobugs.model.Project;
import com.zerobugs.model.User;
import com.zerobugs.repository.ProjectRepository;
import com.zerobugs.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service for handling project-level security checks.
 */
@Service
public class ProjectSecurityService {
    
    @Autowired
    private ProjectRepository projectRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    /**
     * Check if user has access to the project (owner or member).
     */
    public boolean hasProjectAccess(Long projectId, String username) {
        User user = userRepository.findByUsername(username).orElse(null);
        if (user == null) {
            return false;
        }
        
        Project project = projectRepository.findById(projectId).orElse(null);
        if (project == null) {
            return false;
        }
        
        // Check if user is owner or member
        return project.isOwner(user) || project.isMember(user) || project.getIsPublic();
    }
    
    /**
     * Check if user is the owner of the project.
     */
    public boolean isProjectOwner(Long projectId, String username) {
        User user = userRepository.findByUsername(username).orElse(null);
        if (user == null) {
            return false;
        }
        
        Project project = projectRepository.findById(projectId).orElse(null);
        if (project == null) {
            return false;
        }
        
        return project.isOwner(user);
    }
    
    /**
     * Check if user is a member of the project.
     */
    public boolean isProjectMember(Long projectId, String username) {
        User user = userRepository.findByUsername(username).orElse(null);
        if (user == null) {
            return false;
        }
        
        Project project = projectRepository.findById(projectId).orElse(null);
        if (project == null) {
            return false;
        }
        
        return project.isMember(user);
    }
    
    /**
     * Check if user can modify project settings.
     */
    public boolean canModifyProject(Long projectId, String username) {
        return isProjectOwner(projectId, username);
    }
    
    /**
     * Check if user can view project details.
     */
    public boolean canViewProject(Long projectId, String username) {
        return hasProjectAccess(projectId, username);
    }
}
