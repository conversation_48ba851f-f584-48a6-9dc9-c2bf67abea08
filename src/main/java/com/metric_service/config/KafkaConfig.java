package com.metric_service.config;

import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Configuration
public class KafkaConfig {

    //read from kafka topics
    @Value("${kafka.metrics.topics}")
    private String kafkaTopics;

    @Bean
    public List<NewTopic> kafkaMetricsTopic(){
        //convert comma separated topics into list
        List<String> exporterNames = Arrays.asList(kafkaTopics.split(","));

        // Generate NewTopic instances dynamically
        return exporterNames.stream()
                .map(exporter -> new NewTopic(exporter.trim(), 1, (short) 1)) // Creating topic with replication factor 1
                .collect(Collectors.toList());
    }
}