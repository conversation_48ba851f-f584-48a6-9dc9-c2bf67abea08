# ----------------------------------------
# APPLICATION SETTINGS
# ----------------------------------------
spring.application.name=metric-service
server.port=8888

# External configuration import
spring.config.import=optional:file:/opt/databahn/config/app.yaml

# ----------------------------------------
# LOGGING CONFIGURATION
# ----------------------------------------
logging.level.root=INFO
logging.level.com.metric_service=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# ----------------------------------------
# <PERSON><PERSON><PERSON> CONFIGURATION
# ----------------------------------------
# Bootstrap servers
spring.kafka.bootstrap-servers=localhost:9092

# Producer settings
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
kafka.producer.acks=all
kafka.producer.retries=3
kafka.producer.batch.size=16384
kafka.producer.linger.ms=1
kafka.producer.buffer.memory=33554432

# Kafka Topics Configuration
kafka.metrics.topics=kafka-metrics,redis-metrics,kubernetes-metrics,node-metrics

# ----------------------------------------
# PROMETHEUS SCRAPING CONFIGURATION
# ----------------------------------------
management.endpoints.web.exposure.include=*
management.metrics.export.prometheus.enabled=true
management.endpoint.prometheus.enabled=true

# ----------------------------------------
# METRICS COLLECTION CONFIGURATION
# ----------------------------------------
# Collection interval in milliseconds (default: 60 seconds)
metrics.collection.interval=60000

# Kafka JMX exporter ports (comma-separated)
metrics.kafka.jmx.ports=10001

# Redis exporter port
metrics.redis.port=9121

# Kubernetes metrics port
metrics.kubernetes.port=8080

# ----------------------------------------
# KUBERNETES CONFIGURATION
# ----------------------------------------
# Namespace for monitoring services
kubernetes.monitoring.namespace=monitoring